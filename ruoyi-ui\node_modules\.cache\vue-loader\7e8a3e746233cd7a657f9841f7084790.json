{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\winning\\index.vue?vue&type=template&id=a257dc60&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\winning\\index.vue", "mtime": 1758863398996}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750942930085}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}