{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\winning\\index.vue?vue&type=template&id=a257dc60&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\winning\\index.vue", "mtime": 1758862985987}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750942930085}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}