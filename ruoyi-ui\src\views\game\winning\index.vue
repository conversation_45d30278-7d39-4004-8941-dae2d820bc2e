<template>
  <div class="app-container">

    <!-- 移动端搜索按钮 -->
    <div v-if="isMobile" class="mobile-search-toggle">
      <el-button
        type="primary"
        icon="el-icon-search"
        size="small"
        @click="toggleMobileSearch"
        class="mobile-search-btn">
        {{ showMobileSearch ? '收起搜索' : '展开搜索' }}
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-container" v-show="isMobile ? showMobileSearch : showSearch">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="!isMobile" label-width="80px" :class="['search-form', { 'mobile-search-form': isMobile }]">
        <el-form-item label="中奖用户" prop="userId">
          <el-select
            v-model="queryParams.userId"
            placeholder="请选择中奖用户"
            clearable
            filterable
            @change="handleQuery"
            prefix-icon="el-icon-user"
            :style="isMobile ? 'width: 100%;' : 'width: 200px;'"
          >
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.name"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="流水号" prop="serialNumber">
          <el-select
            v-model="queryParams.serialNumber"
            placeholder="请选择流水号"
            clearable
            filterable
            @change="handleQuery"
            prefix-icon="el-icon-document"
            :style="isMobile ? 'width: 100%;' : 'width: 200px;'"
          >
            <el-option
              v-for="item in serialNumberList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
      <!-- <el-form-item label="彩种名称" prop="lotteryId">
        <el-select
          v-model="queryParams.lotteryId"
          placeholder="请选择彩种"
          clearable
          @change="handleQuery"
        >
          <el-option label="福彩3D" value="1" />
          <el-option label="体彩排三" value="2" />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="玩法名称" prop="methodId">
        <el-select
          v-model="queryParams.methodId"
          placeholder="请选择玩法名称"
          clearable
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="item in gameMethodsData"
            :key="item.methodId"
            :label="item.methodName"
            :value="item.methodId"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="中奖期号" prop="issueNumber">
        <el-select
          v-model="queryParams.issueNumber"
          placeholder="请选择中奖期号"
          clearable
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="item in issueList"
            :key="item.qihao"
            :label="item.qihao"
            :value="item.qihao"
          />
        </el-select>
      </el-form-item> -->

        <el-form-item label="识别框" prop="shibie">
          <el-input
            v-model="queryParams.shibie"
            placeholder="请输入识别内容（支持模糊搜索）"
            clearable
            type="textarea"
            prefix-icon="el-icon-search"
            :style="isMobile ? 'width: 100%;' : 'width: 350px;'"
            @input="handleShiBieInput"
            @clear="handleShiBieClear"
          />
          <div v-if="queryParams.shibie && queryParams.shibie.trim()" class="search-tip">
            <i class="el-icon-info"></i>
            模糊搜索模式：将显示所有包含"{{ queryParams.shibie.trim() }}"的记录
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery" class="search-btn">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery" class="reset-btn">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

   <!-- <el-row :gutter="10" class="mb8">
       <el-col :span="1.5">
        <el-popconfirm
          title="确认要批量赔付选中的奖金吗？"
          @confirm="handleBatchPay"
        >
          <el-button
            slot="reference"
            type="primary"
            plain
            icon="el-icon-s-finance"
            size="mini"
            :disabled="multiple"
            v-hasPermi="['game:winning:edit']"
          >批量赔付</el-button>
        </el-popconfirm>
      </el-col>
   
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['game:winning:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['game:winning:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <!-- 工具栏 -->
    <div class="toolbar-container" :class="{ 'mobile-toolbar': isMobile }">
      <div class="toolbar-content">
        <div class="toolbar-left" :class="{ 'mobile-toolbar-left': isMobile }">
          <el-button
            type="primary"
            :icon="allExpanded ? 'el-icon-minus' : 'el-icon-plus'"
            :size="isMobile ? 'mini' : 'small'"
            @click="toggleExpandAll"
            class="toolbar-btn expand-btn"
          >{{ isMobile ? (allExpanded ? '收起' : '展开') : (allExpanded ? '收起全部' : '展开全部') }}</el-button>
          <el-button
            type="info"
            icon="el-icon-refresh"
            :size="isMobile ? 'mini' : 'small'"
            @click="getList"
            class="toolbar-btn refresh-btn"
          >{{ isMobile ? '刷新' : '刷新数据' }}</el-button>
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            :size="isMobile ? 'mini' : 'small'"
            @click="handleExport"
            v-hasPermi="['game:winning:export']"
            class="toolbar-btn export-btn"
          >导出</el-button>
          <el-button
            :type="isReconciliationMode ? 'danger' : 'success'"
            :icon="isReconciliationMode ? 'el-icon-close' : 'el-icon-check'"
            :size="isMobile ? 'mini' : 'small'"
            @click="toggleReconciliationMode"
            :loading="reconciliationLoading"
            class="toolbar-btn reconciliation-btn"
          >{{ isReconciliationMode ? '退出对账' : '对账' }}</el-button>
        </div>
        <div class="toolbar-right">
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </div>
      </div>
    </div>

    <!-- 对账模式提示框 -->
    <div v-if="isReconciliationMode" ref="reconciliationNotice" class="reconciliation-notice" :class="{ 'reconciliation-fixed': isReconciliationFixed }">
      <div class="reconciliation-alert">
        <div class="reconciliation-header">
          <i class="el-icon-info"></i>
          <span class="reconciliation-title">对账模式</span>
        </div>
        <div class="reconciliation-content">
          <p class="reconciliation-instruction">
            <strong style="color: #ff4d4f;">操作提示：</strong>点击表格中蓝色虚线框的
            <span class="highlight-amount">"中奖金额: ￥xxx.xx"</span>
            区域即可标记该记录为已对账！
          </p>
          <div class="reconciliation-stats">
            <span class="stats-text">
              已对账中奖记录数量：
              <span class="reconciled-count">{{ reconciledWinningIds.length }}</span> /
              <span class="total-count">{{ winningList.length }}</span>
            </span>
            <span class="stats-text" style="margin-left: 20px;">
              已对账中奖金额：
              <span class="reconciled-amount">￥{{ formatAmount(reconciledWinningAmount) }}</span> /
              <span class="total-amount">￥{{ formatAmount(totalWinningAmount) }}</span>
            </span>
            <el-button
              v-if="isReconciliationFixed"
              type="danger"
              size="mini"
              icon="el-icon-close"
              @click="exitReconciliationMode"
              class="exit-reconciliation-btn">
              退出对账
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container" :class="{ 'mobile-table-container': isMobile }">
      <!-- 移动端卡片式布局 -->
      <div v-if="isMobile" class="mobile-card-container" :class="{ 'reconciliation-mode': isReconciliationMode }">
        <!-- 移动端对账模式提示 -->
        <div v-if="isReconciliationMode" class="mobile-reconciliation-notice">
          <div class="reconciliation-header">
            <i class="el-icon-info"></i>
            <span>对账模式</span>
            <el-button type="text" @click="exitReconciliationMode" class="exit-btn">
              <i class="el-icon-close"></i>
            </el-button>
          </div>
          <div class="reconciliation-stats">
            <span class="stat-item">已对账: <strong>{{ reconciledWinningIds.length }}</strong></span>
            <span class="stat-item">总额: <strong>¥{{ reconciledWinningAmount.toFixed(2) }}</strong></span>
          </div>
        </div>

        <!-- 移动端卡片列表 -->
        <div v-for="(item, index) in winningList" :key="item.winId" class="mobile-card"
             :class="{ 'reconciled': isReconciliationMode && isWinningReconciled(item.winId) }"
             @click="!isReconciliationMode ? toggleMobileCard(item, index) : null">

          <!-- 卡片头部 -->
          <div class="card-header">
            <!-- 左侧点击区域：展开/收起卡片 -->
            <div class="card-header-left" @click="isReconciliationMode ? toggleMobileCard(item, index) : null">
              <div class="winning-info">
                <div class="user-name">{{ getUserName(item.userId) }}</div>
                <div class="winning-meta">
                  <span class="serial-number">流水号: {{ item.serialNumber }}</span>
                  <span class="lottery-type">
                    <el-tag v-if="item.lotteryId === 1" type="danger" size="mini">福彩</el-tag>
                    <el-tag v-else-if="item.lotteryId === 2" type="primary" size="mini">体彩</el-tag>
                    <span v-else>{{ item.lotteryId }}</span>
                  </span>
                  <span class="method-name">{{ getMethodName(item.methodId) }}</span>
                </div>
              </div>
            </div>

            <!-- 右侧点击区域：对账操作（仅在对账模式下可点击） -->
            <div class="card-header-right"
                 :class="{ 'reconciliation-clickable': isReconciliationMode }"
                 @click="isReconciliationMode ? toggleWinningReconciliation(item.winId) : null">
              <div class="amount-info">
                <div class="win-amount">{{ formatAmount(item.winAmount) }}</div>
                <div v-if="isReconciliationMode" class="reconciliation-status"
                     :class="{ 'reconciled': isWinningReconciled(item.winId) }">
                  {{ isWinningReconciled(item.winId) ? '已对账' : '待对账' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 卡片内容（展开时显示） -->
          <div v-if="item.mobileExpanded" class="card-content">
            <div v-if="item.shibie" class="detail-row">
              <span class="detail-label">识别框:</span>
              <span class="detail-value">{{ item.shibie }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">下注号码:</span>
              <div class="detail-value">
                <div v-if="item.betNumber">
                  <!-- 胆拖玩法显示 -->
                  <div v-if="item.methodId >= 44 && item.methodId <= 59">
                    <template v-if="JSON.parse(item.betNumber).numbers.length > 0">
                      <el-tag type="danger" size="mini" style="margin: 2px;">
                        胆{{ JSON.parse(item.betNumber).numbers[0].danma }}
                      </el-tag>
                      <el-tag size="mini" style="margin: 2px;">
                        拖{{ JSON.parse(item.betNumber).numbers[0].tuoma }}
                      </el-tag>
                    </template>
                  </div>
                  <!-- 跨度玩法显示 -->
                  <div v-else-if="item.methodId >= 60 && item.methodId <= 69">
                    <el-tag size="mini" style="margin: 2px;">跨度{{ item.methodId - 60 }}</el-tag>
                  </div>
                  <!-- 其他玩法显示 -->
                  <div v-else>
                    <template v-if="typeof item.betNumber === 'string'">
                      <div v-for="(num, numIndex) in JSON.parse(item.betNumber).numbers" :key="numIndex" style="display: inline-block; margin: 2px;">
                        <el-tag type="primary" size="mini" style="margin: 1px; font-weight: bold;">
                          {{ Object.values(num).join('.') }}
                        </el-tag>
                      </div>
                    </template>
                    <span v-else>{{ item.betNumbers }}</span>
                  </div>
                </div>
                <span v-else>{{ item.betNumbers }}</span>
              </div>
            </div>
            <div class="detail-row">
              <span class="detail-label">中奖号码:</span>
              <div class="detail-value">
                <div v-if="item.winningNumbers">
                  <template v-if="typeof item.winningNumbers === 'string'">
                    <div v-for="(winItem, winIndex) in JSON.parse(item.winningNumbers).winning" :key="winIndex">
                      <el-tag type="success" size="mini" style="margin: 1px;">
                        <template v-if="winItem.b === undefined && winItem.c === undefined">
                          {{ winItem.a }}
                        </template>
                        <template v-else-if="winItem.c === undefined">
                          {{ winItem.a }}.{{ winItem.b }}
                        </template>
                        <template v-else>
                          {{ winItem.a }}.{{ winItem.b }}.{{ winItem.c }}
                        </template>
                      </el-tag>
                    </div>
                  </template>
                  <span v-else>{{ item.winningNumbers }}</span>
                </div>
                <span v-else>{{ item.winningNumbers }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 桌面端表格 -->
      <el-table v-else
        v-loading="loading"
        :data="winningList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        ref="winningTable"
        border
        :header-cell-style="{ background: '#f8f9fa', color: '#606266', fontWeight: 'bold' }"
      >
        <el-table-column type="expand" width="60">
          <template slot-scope="props">
            <div class="expand-content">
              <div class="expand-header">
                <i class="el-icon-info"></i>
                <span>详细信息</span>
              </div>
              <el-form label-position="left" inline class="table-expand">
                <el-form-item label="号码识别" class="full-width">
                  <div class="expand-value">{{ props.row.shibie || '无' }}</div>
                </el-form-item>
                <el-form-item label="下注号码" class="full-width">
                  <div class="expand-value">
                    <div v-if="props.row.betNumber" class="number-tags">
                      <!-- 胆拖玩法详情显示 -->
                      <div v-if="props.row.methodId >= 44 && props.row.methodId <= 59">
                        <template v-if="JSON.parse(props.row.betNumber).numbers.length > 0">
                          <el-tag type="danger" effect="dark" size="medium" style="margin: 4px; font-size: 16px; font-weight: bold;">
                            胆码: {{ JSON.parse(props.row.betNumber).numbers[0].danma }}
                          </el-tag>
                          <el-tag type="info" effect="dark" size="medium" style="margin: 4px; font-size: 16px; font-weight: bold;">
                            拖码: {{ JSON.parse(props.row.betNumber).numbers[0].tuoma }}
                          </el-tag>
                        </template>
                      </div>

                      <!-- 跨度玩法详情显示 -->
                      <div v-else-if="props.row.methodId >= 60 && props.row.methodId <= 69">
                        <el-tag type="warning" effect="dark" size="medium" style="margin: 4px; font-size: 16px; font-weight: bold;">
                          跨度值: {{ props.row.methodId - 60 }}
                        </el-tag>
                      </div>

                      <!-- 原有玩法详情显示 -->
                      <div v-else>
                        <el-tag
                          v-for="(item, index) in JSON.parse(props.row.betNumber).numbers"
                          :key="index"
                          type="primary"
                          effect="dark"
                          size="small"
                          class="number-tag bet-tag"
                        >
                          {{ Object.values(item).join('.') }}
                        </el-tag>
                      </div>
                    </div>
                    <span v-else class="no-data">{{ props.row.betNumbers || '无' }}</span>
                  </div>
                </el-form-item>
                <el-form-item label="中奖号码" class="full-width">
                  <div class="expand-value">
                    <div v-if="props.row.winningNumbers" class="number-tags">
                      <template v-if="typeof props.row.winningNumbers === 'string'">
                        <el-tag
                          v-for="(item, index) in JSON.parse(props.row.winningNumbers).winning"
                          :key="index"
                          type="success"
                          effect="dark"
                          size="small"
                          class="number-tag winning-tag"
                        >
                          <template v-if="item.b === undefined && item.c === undefined">
                            {{ item.a }}
                          </template>
                          <template v-else-if="item.c === undefined">
                            {{ item.a }}.{{ item.b }}
                          </template>
                          <template v-else>
                            {{ item.a }}.{{ item.b }}.{{ item.c }}
                          </template>
                        </el-tag>
                      </template>
                      <template v-else>
                        <span class="no-data">{{ props.row.winningNumbers }}</span>
                      </template>
                    </div>
                    <span v-else class="no-data">{{ props.row.winningNumbers || '无' }}</span>
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </template>
      </el-table-column>
        <el-table-column label="中奖用户" align="center" prop="userId" min-width="120">
          <template slot-scope="scope">
            <div class="user-info">
              <i class="el-icon-user"></i>
              <span>{{ getUserName(scope.row.userId) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="彩种" align="center" prop="lotteryId" width="80">
          <template slot-scope="scope">
            <div class="lottery-type">
              <el-tag v-if="scope.row.lotteryId === 1" type="danger" effect="dark" size="small" class="lottery-tag">福彩</el-tag>
              <el-tag v-else-if="scope.row.lotteryId === 2" type="primary" effect="dark" size="small" class="lottery-tag">体彩</el-tag>
              <span v-else class="lottery-unknown">{{ scope.row.lotteryId }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="玩法名称" align="center" prop="methodId" min-width="120">
          <template slot-scope="scope">
            <div class="method-info">
              <i class="el-icon-star-on"></i>
              <span>{{ getMethodName(scope.row.methodId) }}</span>
            </div>
          </template>
        </el-table-column>
      <el-table-column label="下注号码" align="center" prop="betNumber" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.betNumber">
            <!-- 胆拖玩法显示 (method_id 44-59) -->
            <div v-if="scope.row.methodId >= 44 && scope.row.methodId <= 59" style="white-space: nowrap;">
              <template v-if="JSON.parse(scope.row.betNumber).numbers.length > 0">
                <el-tag type="danger" effect="dark" size="mini" style="margin: 1px; font-weight: bold;">
                  胆{{ JSON.parse(scope.row.betNumber).numbers[0].danma }}
                </el-tag>
                <el-tag effect="dark" size="mini" style="margin: 1px; font-weight: bold;">
                  拖{{ JSON.parse(scope.row.betNumber).numbers[0].tuoma }}
                </el-tag>
              </template>
            </div>

            <!-- 跨度玩法显示 (method_id 60-69) -->
            <div v-else-if="scope.row.methodId >= 60 && scope.row.methodId <= 69" style="white-space: nowrap;">
              <el-tag  effect="dark" size="mini" style="margin: 1px; font-weight: bold;">
                跨度{{ scope.row.methodId - 60 }}
              </el-tag>
            </div>

            <!-- 原有玩法显示保持不变 -->
            <div v-else style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
              <div v-for="(item, index) in JSON.parse(scope.row.betNumber).numbers" :key="index" style="display: inline-block;">
                <el-tag
                  type="primary"
                  effect="dark"
                  size="small"
                  style="margin: 2px; font-weight: bold;"
                >
                  {{ Object.values(item).join('.') }}
                </el-tag>
              </div>
            </div>
          </div>
          <span v-else style="font-size: 14px; font-weight: bold;">{{ scope.row.betNumbers }}</span>
        </template>
      </el-table-column>
         <el-table-column label="中奖号码" align="center" prop="winningNumbers" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.winningNumbers" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
            <template v-if="typeof scope.row.winningNumbers === 'string'">
              <div v-for="(item, index) in JSON.parse(scope.row.winningNumbers).winning" :key="index"
                style="display: inline-block;">
                <el-tag type="success" effect="dark" size="small" style="margin: 2px; font-weight: bold;">
                  <template v-if="item.b === undefined && item.c === undefined">
                    {{ item.a }}
                  </template>
                  <template v-else-if="item.c === undefined">
                    {{ item.a }}.{{ item.b }}
                  </template>
                  <template v-else>
                    {{ item.a }}.{{ item.b }}.{{ item.c }}
                  </template>
                </el-tag>
              </div>
            </template>
            <template v-else>
              <span style="font-size: 14px; font-weight: bold;">{{ scope.row.winningNumbers }}</span>
            </template>
          </div>
          <span v-else style="font-size: 14px; font-weight: bold;">{{ scope.row.winningNumbers }}</span>
        </template>
      </el-table-column>
    
        <el-table-column label="流水号" align="center" prop="serialNumber" width="100">
          <template slot-scope="scope">
            <div class="serial-number">
              <span class="serial-value">{{ scope.row.serialNumber }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="中奖金额" align="center" prop="winAmount" min-width="120" sortable :sort-method="sortByAmount">
          <template slot-scope="scope">
            <div v-if="isReconciliationMode"
                 class="reconciliation-amount-container"
                 :class="{ 'reconciled': isWinningReconciled(scope.row.winId) }"
                 @click.stop="toggleWinningReconciliation(scope.row.winId)">
              <span class="reconciliation-amount-text">
                中奖金额: {{ formatAmount(scope.row.winAmount) }}
              </span>
              <span class="reconciliation-status-badge"
                    :class="{ 'reconciled': isWinningReconciled(scope.row.winId) }">
                {{ isWinningReconciled(scope.row.winId) ? '已对账' : '待对账' }}
              </span>
            </div>
            <span v-else class="amount-simple">{{ formatAmount(scope.row.winAmount) }}</span>
          </template>
        </el-table-column>
      <!-- <el-table-column label="是否赔付" align="center" prop="isPaid">
        <template slot-scope="scope">
          <span style="font-size: 14px; font-weight: bold;">{{ scope.row.isPaid === 1 ? '是' : '否' }}</span>
        </template>
      </el-table-column> -->
   
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-popconfirm
            title="确认要赔付该笔奖金吗？"
            @confirm="handlePay(scope.row)"
            v-if="scope.row.isPaid === 0"
          >
            <el-button
              slot="reference"
              size="mini"
              type="text"
              icon="el-icon-s-finance"
              style="font-size: 14px; font-weight: bold;"
              v-hasPermi="['game:winning:edit']"
            >赔付</el-button>
          </el-popconfirm>
          <span v-else style="color: #56575AFF; font-size: 14px; font-weight: 700;">已赔付</span>
        </template>
      </el-table-column> -->
      </el-table>
    </div>

    <pagination
      v-show="total > 0 && !isReconciliationMode"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改中奖管理对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="中奖用户" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入中奖用户" />
        </el-form-item>
        <el-form-item label="玩法名称" prop="methodId">
          <el-input v-model="form.methodId" placeholder="请输入玩法名称" />
        </el-form-item>
        <el-form-item label="下注号码" prop="betNumber">
          <el-input v-model="form.betNumber" placeholder="请输入下注号码" />
        </el-form-item>
        <el-form-item label="彩种名称" prop="lotteryId">
          <el-input v-model="form.lotteryId" placeholder="请输入彩种名称" />
        </el-form-item>
        <el-form-item label="中奖期号" prop="issueNumber">
          <el-input v-model="form.issueNumber" placeholder="请输入中奖期号" />
        </el-form-item>
        <el-form-item label="中奖金额" prop="winAmount">
          <el-input v-model="form.winAmount" placeholder="请输入中奖金额" />
        </el-form-item>
        <el-form-item label="是否赔付" prop="isPaid">
          <el-input v-model="form.isPaid" placeholder="请输入是否赔付" />
        </el-form-item>
        <el-form-item label="中奖号码" prop="winningNumbers">
          <el-input v-model="form.winningNumbers" placeholder="请输入中奖号码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import { listWinning, getWinning, delWinning, addWinning, updateWinning, exportWinning } from "@/api/game/winning"
import { listCustomer } from "@/api/game/customer"
import { mapState } from 'vuex'
import { listUser } from "@/api/system/user"
import { getAllMethods } from "@/api/game/method"
import { listDraw } from "@/api/game/draw"
import { parseTime } from '@/utils/ruoyi'
import { cancelPay } from "@/api/game/winning"
import { listSerial } from "@/api/game/serial"

export default {
  name: "Winning",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 中奖管理表格数据
      winningList: [],
      // 是否全部展开
      allExpanded: false,
      // 移动端相关
      isMobile: false,
      showMobileSearch: false,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 用户列表
      userList: [],
      // 期号列表
      issueList: [],
      // 流水号列表
      serialNumberList: [],
      // 识别框搜索防抖定时器
      shiBieSearchTimer: null,
      // 对账模式相关
      isReconciliationMode: false,
      reconciliationLoading: false,
      reconciledWinningIds: [], // 已对账的中奖记录ID数组（响应式）
      originalQueryParams: null, // 保存原始查询参数
      isReconciliationFixed: false, // 对账提示框是否固定在顶部
      reconciliationOriginalTop: 0, // 对账提示框原始位置
      scrollDebounceTimer: null, // 滚动防抖定时器
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        userId: null,
        methodId: null,
        betNumbers: null,
        lotteryId: null,
        issueNumber: null,
        winAmount: null,
        isPaid: null,
        winningNumbers: null,
        shibie: null,
        serialNumber: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "中奖用户不能为空", trigger: "blur" }
        ],
        methodId: [
          { required: true, message: "玩法名称不能为空", trigger: "blur" }
        ],
        lotteryId: [
          { required: true, message: "彩种名称不能为空", trigger: "blur" }
        ],
        issueNumber: [
          { required: true, message: "中奖期号不能为空", trigger: "blur" }
        ],
      }
    }
  },
  computed: {
    ...mapState({
      gameMethodsData: state => state.game.methodsData
    }),
    /** 计算已对账的中奖金额总和 */
    reconciledWinningAmount() {
      if (!this.isReconciliationMode || !this.winningList || this.winningList.length === 0) {
        return 0;
      }

      return this.winningList
        .filter(winning => this.isWinningReconciled(winning.winId))
        .reduce((total, winning) => {
          const amount = parseFloat(winning.winAmount) || 0;
          return total + amount;
        }, 0);
    },
    /** 计算所有中奖金额总和 */
    totalWinningAmount() {
      if (!this.winningList || this.winningList.length === 0) {
        return 0;
      }

      return this.winningList.reduce((total, winning) => {
        const amount = parseFloat(winning.winAmount) || 0;
        return total + amount;
      }, 0);
    }
  },
  created() {
    // 初始化移动端检测
    this.initMobileDetection();

    this.getList()
    this.getCustomerList()
    this.getGameMethods()
    this.getIssueList()
    this.getSerialNumberList()
    // 恢复对账模式状态
    this.restoreReconciliationState()
  },
  mounted() {
    // 监听滚动事件
    window.addEventListener('scroll', this.handleScroll);
    // 监听一键清空事件
    window.addEventListener('clearAllData', this.handleClearAllData);
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 移除滚动监听
    window.removeEventListener('scroll', this.handleScroll);
    // 移除一键清空事件监听
    window.removeEventListener('clearAllData', this.handleClearAllData);
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize);

    // 清理防抖定时器
    if (this.scrollDebounceTimer) {
      clearTimeout(this.scrollDebounceTimer);
      this.scrollDebounceTimer = null;
    }
  },
  methods: {
    /** 初始化移动端检测 */
    initMobileDetection() {
      if (typeof window !== 'undefined') {
        this.isMobile = window.innerWidth <= 768;
      }
    },

    /** 处理窗口大小变化 */
    handleResize() {
      if (typeof window !== 'undefined') {
        const newIsMobile = window.innerWidth <= 768;
        if (this.isMobile !== newIsMobile) {
          this.isMobile = newIsMobile;
          // 在移动端和桌面端切换时，重置搜索框状态
          if (!newIsMobile && this.showMobileSearch) {
            this.showMobileSearch = false;
          }
          // 强制重新渲染以适应新的屏幕尺寸
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        }
      }
    },

    /** 切换移动端搜索框显示 */
    toggleMobileSearch() {
      this.showMobileSearch = !this.showMobileSearch;
    },

    /** 切换移动端卡片展开状态 */
    toggleMobileCard(item, index) {
      this.$set(item, 'mobileExpanded', !item.mobileExpanded);
    },

    /** 退出对账模式 */
    exitReconciliationMode() {
      this.toggleReconciliationMode();
    },

    /** 获取期号列表 */
    getIssueList() {
      listDraw().then(response => {
        this.issueList = response.rows
      })
    },
    /** 获取用户列表 */
    getCustomerList() {
      listCustomer().then(response => {
        this.userList = response.rows
      })
    },
    /** 查询中奖管理列表 */
    getList() {
      this.loading = true
      const params = { ...this.queryParams };

      // 如果是对账模式，获取所有数据
      if (this.isReconciliationMode) {
        params.pageNum = 1;
        params.pageSize = 999999;
      }

      // 记录是否是识别框搜索
      const isShiBieSearch = params.shibie && params.shibie.trim() !== '';

      // 如果有识别框搜索内容
      if (isShiBieSearch) {
        // 去除首尾空格
        params.shibie = params.shibie.trim();
        // 对于识别框搜索，不传递给后端，而是获取所有数据进行前端过滤
        delete params.shibie;
      }

      listWinning(params).then(response => {
        let filteredRows = response.rows;

        // 如果有识别框搜索内容且有返回结果，进行前端过滤
        if (isShiBieSearch && filteredRows.length > 0) {
          const searchText = this.queryParams.shibie.trim();

          // 模糊匹配：包含搜索文本的所有记录
          filteredRows = filteredRows.filter(row =>
            row.shibie && row.shibie.includes(searchText)
          );

          // 更新总数为过滤后的数量
          response.total = filteredRows.length;
        }

        this.winningList = filteredRows;
        this.total = response.total;

        // 如果是对账模式，强制正序排序
        if (this.isReconciliationMode) {
          this.sortWinningListForReconciliation();
        }

        this.loading = false;

        // 如果是识别框搜索，自动展开所有搜索结果
        if (isShiBieSearch) {
          this.$nextTick(() => {
            this.allExpanded = true;
            this.winningList.forEach((row) => {
              this.$refs.winningTable.toggleRowExpansion(row, true);
            });
          });
        } else {
          // 非识别框搜索，重置展开状态
          this.resetExpandState();
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        winId: null,
        userId: null,
        methodId: null,
        betNumber: null,
        lotteryId: null,
        issueNumber: null,
        winAmount: null,
        isPaid: null,
        winningNumbers: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.winId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 处理表格行点击 */
    handleRowClick(row, column, event) {
      // 避免点击操作按钮时触发行展开
      if (column && column.type === 'selection') {
        return;
      }

      // 在对账模式下，如果点击的是中奖金额列，不展开行
      if (this.isReconciliationMode && column && column.property === 'winAmount') {
        return;
      }

      console.log('中奖记录表格行点击:', row.winId || 'unknown');

      // 切换行的展开状态
      if (this.$refs.winningTable) {
        this.$refs.winningTable.toggleRowExpansion(row);
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加中奖管理"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const winId = row.winId || this.ids
      getWinning(winId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改中奖管理"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.winId != null) {
            updateWinning(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addWinning(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const winIds = row.winId || this.ids
      this.$modal.confirm('是否确认删除中奖管理编号为"' + winIds + '"的数据项？').then(function() {
        return delWinning(winIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有中奖管理数据项？').then(() => {
        this.exportLoading = true
        // 获取所有数据用于导出
        const exportParams = {
          ...this.queryParams,
          pageNum: 1,
          pageSize: 10000 // 获取大量数据
        }

        listWinning(exportParams).then(response => {
          const data = response.rows
          const formattedData = this.formatDataForExport(data)
          this.exportToExcel(formattedData)
        }).catch(() => {
          this.$modal.msgError("导出失败")
        }).finally(() => {
          this.exportLoading = false
        })
      }).catch(() => {})
    },

    /** 格式化导出数据，与页面显示保持一致 */
    formatDataForExport(data) {
      return data.map(item => {
        // 格式化下注号码
        let betNumbersFormatted = ''
        if (item.betNumber) {
          try {
            const betData = JSON.parse(item.betNumber)
            if (betData.numbers && Array.isArray(betData.numbers)) {
              // 胆拖玩法格式化 (method_id 44-59)
              if (item.methodId >= 44 && item.methodId <= 59) {
                if (betData.numbers.length > 0) {
                  const firstNum = betData.numbers[0]
                  betNumbersFormatted = `胆${firstNum.danma} 拖${firstNum.tuoma}`
                }
              }
              // 跨度玩法格式化 (method_id 60-69)
              else if (item.methodId >= 60 && item.methodId <= 69) {
                betNumbersFormatted = `跨度${item.methodId - 60}`
              }
              // 原有玩法格式化
              else {
                betNumbersFormatted = betData.numbers.map(num => Object.values(num).join('.')).join(', ')
              }
            }
          } catch (e) {
            betNumbersFormatted = item.betNumbers || ''
          }
        } else {
          betNumbersFormatted = item.betNumbers || ''
        }

        // 格式化中奖号码
        let winningNumbersFormatted = ''
        if (item.winningNumbers) {
          try {
            if (typeof item.winningNumbers === 'string') {
              const winData = JSON.parse(item.winningNumbers)
              if (winData.winning && Array.isArray(winData.winning)) {
                winningNumbersFormatted = winData.winning.map(num => {
                  if (num.b === undefined && num.c === undefined) {
                    return num.a
                  } else if (num.c === undefined) {
                    return `${num.a}.${num.b}`
                  } else {
                    return `${num.a}.${num.b}.${num.c}`
                  }
                }).join(', ')
              }
            } else {
              winningNumbersFormatted = item.winningNumbers
            }
          } catch (e) {
            winningNumbersFormatted = item.winningNumbers || ''
          }
        }

        // 格式化中奖金额（去掉￥符号）
        const formatAmountForExport = (amount) => {
          if (!amount && amount !== 0) return '0.00'
          const num = parseFloat(amount)
          return num.toFixed(2)
        }

        return {
          '中奖用户': this.getUserName(item.userId),
          '彩种': this.getLotteryName(item.lotteryId),
          '玩法名称': this.getMethodName(item.methodId),
          '下注号码': betNumbersFormatted,
          '中奖号码': winningNumbersFormatted,
          '流水号': item.serialNumber,
          '中奖金额': formatAmountForExport(item.winAmount),
          '号码识别': item.shibie || ''
        }
      })
    },

    /** 导出到Excel */
    exportToExcel(data) {
      import('@/utils/Export2Excel').then(excel => {
        const tHeader = ['中奖用户', '彩种', '玩法名称', '下注号码', '中奖号码', '流水号', '中奖金额', '号码识别']
        const filterVal = ['中奖用户', '彩种', '玩法名称', '下注号码', '中奖号码', '流水号', '中奖金额', '号码识别']
        const exportData = data.map(v => filterVal.map(j => v[j]))

        excel.export_json_to_excel({
          header: tHeader,
          data: exportData,
          filename: `中奖管理_${this.parseTime(new Date(), '{y}{m}{d}_{h}{i}{s}')}`,
          autoWidth: true,
          bookType: 'xlsx'
        })

        this.$modal.msgSuccess("导出成功")
      }).catch(() => {
        this.$modal.msgError("导出失败")
      })
    },
    /** 获取玩法名称 */
    getMethodName(methodId) {
      if (!this.gameMethodsData || !this.gameMethodsData.length) {
        return methodId
      }
      const method = this.gameMethodsData.find(item => Number(item.methodId) === Number(methodId))
      return method ? method.methodName : methodId
    },
    /** 获取用户名称 */
    getUserName(userId) {
      if (!this.userList || !this.userList.length) {
        return userId
      }
      const user = this.userList.find(item => Number(item.userId) === Number(userId))
      return user ? user.name : userId
    },
    /** 获取彩种名称 */
    getLotteryName(lotteryId) {
      const lotteryMap = {
        '1': '福彩3D',
        '2': '体彩排三'
      }
      return lotteryMap[lotteryId] || lotteryId
    },
    /** 处理赔付 */
    handlePay(row) {
      updateWinning({
        winId: row.winId,
        isPaid: 1
      }).then(response => {
        this.$modal.msgSuccess("赔付成功")
        this.getList()
      })
    },
    /** 批量赔付 */
    handleBatchPay() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要赔付的记录")
        return
      }
      this.loading = true
      const promises = this.ids.map(winId => 
        updateWinning({
          winId: winId,
          isPaid: 1
        })
      )
      Promise.all(promises).then(() => {
        this.$modal.msgSuccess("批量赔付成功")
        this.getList()
      }).catch(error => {
        this.$modal.msgError("批量赔付失败：" + (error.message || "未知错误"))
      }).finally(() => {
        this.loading = false
      })
    },
    formatAmount(amount) {
      if (amount === null || amount === undefined) {
        return '￥0.00'
      }
      return '￥' + parseFloat(amount).toFixed(2)
    },
    /** 切换对账模式 */
    toggleReconciliationMode() {
      if (!this.isReconciliationMode) {
        // 进入对账模式
        this.enterReconciliationMode();
      } else {
        // 退出对账模式
        this.exitReconciliationMode();
      }
    },
    /** 进入对账模式 */
    enterReconciliationMode() {
      // 保存原始查询参数
      this.originalQueryParams = { ...this.queryParams };

      // 清空已对账的中奖记录ID数组
      this.reconciledWinningIds = [];

      // 重置固定状态和防抖定时器
      this.isReconciliationFixed = false;
      this.reconciliationOriginalTop = 0;
      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer);
        this.scrollDebounceTimer = null;
      }

      // 设置为获取所有数据（不分页）
      const allDataParams = {
        ...this.queryParams,
        pageNum: 1,
        pageSize: 999999 // 设置一个很大的数值来获取所有数据
      };

      this.loading = true;

      listWinning(allDataParams).then(response => {
        this.winningList = response.rows;
        this.total = response.rows.length;

        // 强制正序排序（对账模式下按winId正序）
        this.sortWinningListForReconciliation();

        // 设置对账模式
        this.isReconciliationMode = true;

        // 保存对账状态
        this.saveReconciliationState();

        this.loading = false;
        this.reconciliationLoading = false;

        this.$modal.msgSuccess(`已进入对账模式，共加载 ${response.rows.length} 条中奖记录，按流水号正序排列显示`);
      }).catch(error => {
        this.loading = false;
        this.reconciliationLoading = false;
        this.$modal.msgError('进入对账模式失败：' + error.message);
      });
    },
    /** 退出对账模式 */
    exitReconciliationMode() {
      // 恢复原始查询参数
      if (this.originalQueryParams) {
        this.queryParams = { ...this.originalQueryParams };
      }

      // 清空已对账的中奖记录ID数组
      this.reconciledWinningIds = [];

      // 重置固定状态和清理定时器
      this.isReconciliationFixed = false;
      this.reconciliationOriginalTop = 0;
      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer);
        this.scrollDebounceTimer = null;
      }

      // 设置对账模式为false
      this.isReconciliationMode = false;

      // 清除对账状态
      this.clearReconciliationState();

      // 重新加载数据
      this.getList();

      this.$modal.msgSuccess('已退出对账模式');
    },
    /** 对账模式下的排序（强制正序） */
    sortWinningListForReconciliation() {
      if (!this.winningList || this.winningList.length === 0) {
   
        return;
      }

   

      // 对中奖记录按流水号正序排序
      this.winningList.sort((a, b) => {
        const aSerialNumber = a.serialNumber || 0;
        const bSerialNumber = b.serialNumber || 0;
        return aSerialNumber - bSerialNumber; // 强制按流水号正序
      });

 
    

      // 强制触发Vue的响应式更新
      this.$forceUpdate();

      // 确保表格重新渲染
      this.$nextTick(() => {
        if (this.$refs.winningTable) {
          this.$refs.winningTable.doLayout();
        }
      });
    },
    /** 标记中奖记录为已对账 */
    markWinningAsReconciled(winId) {
      if (!this.reconciledWinningIds.includes(winId)) {
        this.reconciledWinningIds.push(winId);
        this.saveReconciliationState();
      }
    },
    /** 取消中奖记录的对账状态 */
    unmarkWinningAsReconciled(winId) {
      const index = this.reconciledWinningIds.indexOf(winId);
      if (index > -1) {
        this.reconciledWinningIds.splice(index, 1);
        this.saveReconciliationState();
      }
    },
    /** 切换中奖记录的对账状态 */
    toggleWinningReconciliation(winId) {
      if (this.isWinningReconciled(winId)) {
        this.unmarkWinningAsReconciled(winId);
      } else {
        this.markWinningAsReconciled(winId);
      }
    },
    /** 检查中奖记录是否已对账 */
    isWinningReconciled(winId) {
      return this.reconciledWinningIds.includes(winId);
    },
    /** 保存对账状态到localStorage */
    saveReconciliationState() {
      try {
        const reconciliationData = {
          isReconciliationMode: this.isReconciliationMode,
          reconciledWinningIds: this.reconciledWinningIds,
          originalQueryParams: this.originalQueryParams,
          timestamp: Date.now()
        };
        localStorage.setItem('winning_reconciliation_state', JSON.stringify(reconciliationData));
      } catch (error) {
        console.error('保存winning对账状态失败:', error);
      }
    },
    /** 从localStorage恢复对账状态 */
    restoreReconciliationState() {
      try {
        const savedData = localStorage.getItem('winning_reconciliation_state');
        if (savedData) {
          const reconciliationData = JSON.parse(savedData);

          // 恢复对账模式状态
          if (reconciliationData.isReconciliationMode) {
            this.isReconciliationMode = true;
            this.reconciledWinningIds = reconciliationData.reconciledWinningIds || [];
            this.originalQueryParams = reconciliationData.originalQueryParams || null;

            console.log('恢复winning对账模式状态:', {
              reconciledCount: this.reconciledWinningIds.length,
              hasOriginalParams: !!this.originalQueryParams
            });

            // 在下一个tick中重新加载数据以确保对账模式正确应用
            this.$nextTick(() => {
              this.getList();
            });
          }
        }
      } catch (error) {
        console.error('恢复winning对账状态失败:', error);
        // 清除损坏的数据
        localStorage.removeItem('winning_reconciliation_state');
      }
    },
    /** 清除对账状态 */
    clearReconciliationState() {
      try {
        localStorage.removeItem('winning_reconciliation_state');
      } catch (error) {
        console.error('清除winning对账状态失败:', error);
      }
    },
    /** 处理一键清空事件 */
    handleClearAllData(event) {
      console.log('winning页面收到一键清空事件:', event.detail);

      // 只有当前页面处于对账模式时才处理，避免与record页面冲突
      if (this.isReconciliationMode) {
        // 添加短暂延迟，确保事件处理的顺序性
        setTimeout(() => {
          this.exitReconciliationMode();
          console.log('winning页面因一键清空操作退出对账模式');
          this.$message.info('检测到一键清空操作，已自动退出对账模式');
        }, 100);
      }
    },
    /** 处理滚动事件 */
    handleScroll() {
      if (!this.isReconciliationMode || !this.$refs.reconciliationNotice) {
        return;
      }

      const noticeElement = this.$refs.reconciliationNotice;
      const rect = noticeElement.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // 初始化原始位置（只在第一次计算）
      if (this.reconciliationOriginalTop === 0 && !this.isReconciliationFixed) {
        this.reconciliationOriginalTop = scrollTop + rect.top;
      }

      // 使用防抖处理，避免频繁切换
      if (!this.scrollDebounceTimer) {
        this.scrollDebounceTimer = setTimeout(() => {
          // 如果对账提示框的顶部离开屏幕超过阈值，则固定它
          if (rect.top <= 0 && !this.isReconciliationFixed) {
            this.isReconciliationFixed = true;
          }
          // 如果滚动回到原始位置附近，则取消固定
          else if (scrollTop <= this.reconciliationOriginalTop - 20 && this.isReconciliationFixed) {
            this.isReconciliationFixed = false;
          }

          this.scrollDebounceTimer = null;
        }, 16); // 约60fps的更新频率
      }
    },
    /** 撤销结算按钮操作 */
    handleCancelPay(row) {
      this.$modal.confirm('是否确认撤销该笔奖金的结算？').then(() => {
        return cancelPay(row.winId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("撤销结算成功");
      }).catch(() => {});
    },
    /** 获取玩法列表 */
    getGameMethods() {
      getAllMethods().then(response => {
        this.$store.commit('game/SET_METHODS_DATA', response.rows)
      })
    },
    /** 获取流水号列表 */
    getSerialNumberList() {
      // 注意：listSerial接口已经在后端实现了用户隔离，所以这里获取的就是当前用户的流水号
      listSerial({ pageNum: 1, pageSize: 1000 }).then(response => {
        
        if (response && response.rows && response.rows.length > 0) {
          // 提取流水号并去重排序
          this.serialNumberList = [...new Set(response.rows.map(item => item.serialNumbers))]
            .filter(item => item != null)
            .sort((a, b) => b - a); // 降序排列，最新的在前面
          
        } else {
          console.log('没有找到流水号数据，尝试从中奖记录获取');
          this.getSerialNumberFromWinning();
        }
      }).catch(error => {
        console.error('获取流水号列表失败，尝试从中奖记录获取:', error);
        this.getSerialNumberFromWinning();
      })
    },
    /** 从中奖记录中获取流水号列表 */
    getSerialNumberFromWinning() {
      // 从中奖记录中提取流水号
      listWinning({ pageNum: 1, pageSize: 1000 }).then(response => {
        if (response && response.rows && response.rows.length > 0) {
          // 从中奖记录中提取流水号并去重排序
          this.serialNumberList = [...new Set(response.rows.map(item => item.serialNumber))]
            .filter(item => item != null)
            .sort((a, b) => b - a); // 降序排列，最新的在前面
          console.log('从中奖记录获取的流水号列表:', this.serialNumberList);
        }
      }).catch(error => {
        console.error('从中奖记录获取流水号失败:', error);
      })
    },
    /** 展开/收起全部 */
    toggleExpandAll() {
      this.allExpanded = !this.allExpanded;
      this.$nextTick(() => {
        if (this.allExpanded) {
          // 展开所有行
          this.winningList.forEach((row, index) => {
            this.$refs.winningTable.toggleRowExpansion(row, true);
          });
        } else {
          // 收起所有行
          this.winningList.forEach((row, index) => {
            this.$refs.winningTable.toggleRowExpansion(row, false);
          });
        }
      });
    },
    /** 重置展开状态 */
    resetExpandState() {
      this.allExpanded = false;
      this.$nextTick(() => {
        // 收起所有行
        this.winningList.forEach((row) => {
          this.$refs.winningTable.toggleRowExpansion(row, false);
        });
      });
    },
    /** 识别框输入处理 */
    handleShiBieInput() {
      // 防抖处理，避免频繁搜索
      if (this.shiBieSearchTimer) {
        clearTimeout(this.shiBieSearchTimer);
      }
      this.shiBieSearchTimer = setTimeout(() => {
        if (this.queryParams.shibie && this.queryParams.shibie.trim()) {
          this.handleQuery();
        }
      }, 500); // 500ms 延迟
    },
    /** 识别框清空处理 */
    handleShiBieClear() {
      this.queryParams.shibie = '';
      this.handleQuery();
    },
    /** 中奖金额排序方法 */
    sortByAmount(a, b) {
      const amountA = parseFloat(a.winAmount) || 0;
      const amountB = parseFloat(b.winAmount) || 0;
      return amountA - amountB;
    },
  }
}
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
}

.header-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-info p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* 搜索区域样式 */
.search-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.reset-btn {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  transform: translateY(-1px);
}

/* 工具栏样式 */
.toolbar-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16px;
  padding: 20px 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.toolbar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
}

/* 工具栏布局 */
.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.toolbar-left {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.toolbar-right {
  margin-left: auto;
  flex-shrink: 0;
}

/* 工具栏按钮美化 */
.toolbar-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-width: 1.5px !important;
  padding: 8px 16px !important;
  font-size: 13px !important;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.toolbar-btn:hover::before {
  left: 100%;
}

.toolbar-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
}

/* 展开按钮样式 */
.expand-btn.el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%) !important;
  border-color: #409eff !important;
  color: white !important;
}

.expand-btn.el-button--primary:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%) !important;
  border-color: #66b1ff !important;
  color: white !important;
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.5) !important;
}

/* 刷新按钮样式 */
.refresh-btn.el-button--info {
  background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%) !important;
  border-color: #909399 !important;
  color: white !important;
}

.refresh-btn.el-button--info:hover {
  background: linear-gradient(135deg, #b1b3b8 0%, #909399 100%) !important;
  border-color: #b1b3b8 !important;
  color: white !important;
  box-shadow: 0 6px 20px rgba(144, 147, 153, 0.5) !important;
}

/* 导出按钮样式 */
.export-btn.el-button--warning.is-plain {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;
  border-color: #e6a23c !important;
  color: white !important;
}

.export-btn.el-button--warning.is-plain:hover {
  background: linear-gradient(135deg, #ebb563 0%, #e6a23c 100%) !important;
  border-color: #ebb563 !important;
  color: white !important;
  box-shadow: 0 6px 20px rgba(230, 162, 60, 0.5) !important;
}

/* 对账按钮样式 */
.reconciliation-btn.el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  border-color: #67c23a !important;
  color: white !important;
}

.reconciliation-btn.el-button--success:hover {
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%) !important;
  border-color: #85ce61 !important;
  color: white !important;
  box-shadow: 0 6px 20px rgba(103, 194, 58, 0.5) !important;
}

.reconciliation-btn.el-button--danger {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%) !important;
  border-color: #f56c6c !important;
  color: white !important;
}

.reconciliation-btn.el-button--danger:hover {
  background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%) !important;
  border-color: #f78989 !important;
  color: white !important;
  box-shadow: 0 6px 20px rgba(245, 108, 108, 0.5) !important;
}

/* 禁用状态样式 */
.toolbar-btn.is-disabled {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cdd7 100%) !important;
  border-color: #dcdfe6 !important;
  color: #c0c4cc !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.toolbar-btn.is-disabled::before {
  display: none;
}

/* 按钮图标样式优化 */
.toolbar-btn [class*="el-icon-"] {
  margin-right: 6px;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .toolbar-container {
    padding: 16px 20px;
  }

  .toolbar-left {
    gap: 8px;
  }

  .toolbar-btn {
    padding: 6px 12px !important;
    font-size: 12px !important;
  }
}

@media (max-width: 768px) {
  .toolbar-container {
    padding: 12px 16px;
  }

  .toolbar-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left {
    gap: 6px;
  }

  .toolbar-right {
    margin-left: 0;
    align-self: flex-end;
  }

  .toolbar-btn {
    padding: 4px 8px !important;
    font-size: 11px !important;
  }

  .toolbar-btn [class*="el-icon-"] {
    margin-right: 4px;
    font-size: 12px;
  }
}

/* 表格容器样式 */
.table-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 展开内容样式 */
.expand-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 10px 0;
}

.expand-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.expand-header i {
  color: #667eea;
}

.table-expand {
  font-size: 0;
}

.table-expand label {
  width: 90px;
  color: #606266;
  font-weight: 600;
}

.table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 12px;
  width: 50%;
}

.table-expand .el-form-item.full-width {
  width: 100%;
  margin-top: 0;
}

.expand-value {
  font-size: 14px;
  color: #2c3e50;
}

.number-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.number-tag {
  margin: 0;
  font-weight: 600;
  border-radius: 6px;
}

.bet-tag {
  background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
  border-color: #409EFF;
}

.winning-tag {
  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
  border-color: #67C23A;
}

.no-data {
  color: #909399;
  font-style: italic;
}

/* 表格列样式 */
.user-info, .method-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-weight: 600;
}

.user-info {
  color: #409EFF;
}

.method-info {
  color: #67C23A;
}

.serial-number {
  display: flex;
  align-items: center;
  justify-content: center;
}

.serial-value {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
}

.lottery-type {
  display: flex;
  justify-content: center;
}

.lottery-tag {
  font-weight: 600;
  border-radius: 6px;
}

.lottery-unknown {
  color: #909399;
  font-weight: 600;
}

.amount-simple {
  font-size: 16px;
  font-weight: bold;
  color: #F56C6C;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .search-container {
    padding: 16px;
  }

  .table-expand .el-form-item {
    width: 100%;
  }

  .number-tags {
    justify-content: center;
  }

  /* 移动端搜索按钮样式 */
  .mobile-search-toggle {
    margin-bottom: 10px;
    text-align: center;
  }

  .mobile-search-btn {
    width: 100%;
    max-width: 200px;
  }

  /* 移动端搜索表单样式 */
  .mobile-search-form {
    padding: 10px;
    background: #f5f7fa;
    border-radius: 4px;
  }

  .mobile-search-form .el-form-item {
    margin-bottom: 10px;
    margin-right: 0;
    width: 100%;
  }

  .mobile-search-form .el-form-item__label {
    width: 60px !important;
    font-size: 12px;
  }

  .mobile-search-form .el-input,
  .mobile-search-form .el-select {
    width: 100% !important;
  }

  /* 移动端识别框输入区域优化 */
  .mobile-search-form .el-form-item .el-textarea {
    width: 100% !important;
  }

  .mobile-search-form .el-form-item .el-textarea .el-textarea__inner {
    width: 100% !important;
    max-width: 100% !important;
    min-height: 60px !important;
    max-height: 80px !important;
    resize: vertical;
    box-sizing: border-box;
    font-size: 14px;
    line-height: 1.4;
  }

  /* 移动端工具栏样式 */
  .mobile-toolbar .toolbar-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .mobile-toolbar-left {
    display: flex;
    flex-wrap: nowrap;
    gap: 3px;
    justify-content: space-between;
  }

  .mobile-toolbar-left .toolbar-btn {
    flex: 1;
    min-width: calc(25% - 6px);
    max-width: calc(25% - 6px);
    padding: 4px 2px !important;
    font-size: 9px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 移动端卡片布局 */
  .mobile-card-container {
    padding: 10px;
  }

  .mobile-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .mobile-card.reconciled {
    border: 2px solid #67c23a;
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  }

  .mobile-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
  }

  .mobile-card .card-header-left {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .mobile-card .winning-info {
    flex: 1;
  }

  .mobile-card .user-name {
    font-size: 16px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 4px;
  }

  .mobile-card .winning-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #666;
    flex-wrap: wrap;
  }

  .mobile-card .serial-number {
    color: #2c3e50;
    font-weight: 600;
    font-size: 13px;
  }

  .mobile-card .method-name {
    color: #67c23a;
    font-weight: 500;
  }

  .mobile-card .card-header-right {
    text-align: right;
  }

  .mobile-card .win-amount {
    font-size: 18px;
    font-weight: bold;
    color: #f56c6c;
    margin-bottom: 4px;
  }

  .mobile-card .reconciliation-status {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    background: #e6f7ff;
    color: #1890ff;
  }

  .mobile-card .reconciliation-status.reconciled {
    background: #f6ffed;
    color: #52c41a;
  }

  .mobile-card .card-content {
    padding: 12px;
    background: #fafafa;
  }

  .mobile-card .detail-row {
    display: flex;
    margin-bottom: 8px;
    align-items: flex-start;
  }

  .mobile-card .detail-row:last-child {
    margin-bottom: 0;
  }

  .mobile-card .detail-label {
    width: 70px;
    font-size: 12px;
    color: #666;
    flex-shrink: 0;
  }

  .mobile-card .detail-value {
    flex: 1;
    font-size: 12px;
    color: #333;
    word-break: break-all;
  }

  .mobile-card .detail-value .el-tag {
    font-size: 10px;
    padding: 0 4px;
    margin: 1px;
  }

  /* 移动端对账模式提示 */
  .mobile-reconciliation-notice {
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
    border: 1px solid #b3d8ff;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .mobile-reconciliation-notice .reconciliation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .mobile-reconciliation-notice .reconciliation-header i {
    color: #409EFF;
    margin-right: 6px;
  }

  .mobile-reconciliation-notice .reconciliation-stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
  }

  .mobile-reconciliation-notice .stat-item {
    color: #666;
  }

  .mobile-reconciliation-notice .exit-btn {
    padding: 0;
    font-size: 16px;
    color: #f56c6c;
  }

  /* 移动端卡片点击样式 */
  .mobile-card-container:not(.reconciliation-mode) .mobile-card {
    cursor: pointer;
  }

  .mobile-card-container:not(.reconciliation-mode) .mobile-card:hover {
    background-color: rgba(64, 158, 255, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .reconciliation-mode .mobile-card .card-header-left {
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .reconciliation-mode .mobile-card .card-header-left:hover {
    background-color: rgba(64, 158, 255, 0.1);
  }

  .mobile-card .card-header-right.reconciliation-clickable {
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 4px;
    padding: 4px;
  }

  .mobile-card .card-header-right.reconciliation-clickable:hover {
    background-color: rgba(245, 108, 108, 0.1);
  }
}
/* 对账模式提示样式 */
.reconciliation-notice {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.reconciliation-notice.reconciliation-fixed {
  position: fixed;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
  width: calc(100% - 40px);
  max-width: 1200px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: slideDown 0.3s ease-out;
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.reconciliation-alert {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  border: 2px solid #1890ff;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
}

.reconciliation-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.reconciliation-header i {
  font-size: 18px;
  color: #1890ff;
  margin-right: 8px;
}

.reconciliation-title {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
}

.reconciliation-content {
  margin: 0;
}

.reconciliation-instruction {
  background: linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%);
  border: 2px solid #ffa940;
  border-radius: 8px;
  padding: 12px 16px;
  margin: 12px 0 !important;
  font-size: 15px;
  line-height: 1.6;
  box-shadow: 0 2px 8px rgba(255, 169, 64, 0.2);
}

.highlight-amount {
  background: linear-gradient(135deg, #0080FFFF 0%, #067EFFFF 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 6px;
  font-weight: bold;
  font-size: 16px;
  border: 2px dashed white;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
  animation: highlightPulse 2s infinite;
}

@keyframes highlightPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.5);
  }
}

.highlight-amount::after {
  content: " 👆";
  animation: bounce 1.5s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

.reconciliation-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 15px;
}

.stats-text {
  flex: 1;
}

.reconciled-count {
  color: #1890ff;
  font-weight: bold;
  font-size: 16px;
}

.reconciled-amount {
  color: #52c41a;
  font-weight: bold;
  font-size: 16px;
}

.total-amount {
  color: #8c8c8c;
  font-weight: bold;
  font-size: 16px;
}

.total-count {
  color: #52c41a;
  font-weight: bold;
  font-size: 16px;
}

.exit-reconciliation-btn {
  margin-left: 16px;
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(245, 108, 108, 0.3);
}

.exit-reconciliation-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.5);
}

/* 对账模式中奖金额样式 */
.reconciliation-amount-container {
  border: 2px dashed #1890ff;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  position: relative;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reconciliation-amount-container:hover {
  border-color: #40a9ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.reconciliation-amount-container.reconciled {
  border-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border-style: solid;
}

.reconciliation-amount-container.reconciled:hover {
  border-color: #73d13d;
  background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);
}

.reconciliation-amount-text {
  font-weight: bold;
  font-size: 14px;
  color: #FF0037FF;
}

.reconciliation-amount-container.reconciled .reconciliation-amount-text {
  color: #FF0000FF;
}

.reconciled-icon {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: #EAFFEAFF !important;
  color: #52c41a;
  font-size: 16px;
  font-weight: bold;
}

.reconciliation-status-badge {
  position: absolute;
  top: -1px;
  right: -1px;
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 0 6px 0 8px;
  font-weight: bold;
  line-height: 1;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.reconciliation-status-badge.reconciled {
  background: #52c41a;
}

</style>
