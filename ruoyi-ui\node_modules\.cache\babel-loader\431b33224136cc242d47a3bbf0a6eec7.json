{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\winning\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\winning\\index.vue", "mtime": 1758863398996}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\babel.config.js", "mtime": 1750852368688}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_winning", "require", "_customer", "_vuex", "_user", "_method", "_draw", "_ruoyi", "_serial", "_methods", "name", "data", "loading", "exportLoading", "ids", "single", "multiple", "showSearch", "total", "winningList", "allExpanded", "isMobile", "showMobileSearch", "title", "open", "userList", "issueList", "serialNumberList", "shi<PERSON>ieSearchTimer", "isReconciliationMode", "reconciliationLoading", "reconciledWinningIds", "originalQueryParams", "isReconciliationFixed", "reconciliationOriginalTop", "scrollDebounceTimer", "queryParams", "pageNum", "pageSize", "userId", "methodId", "betNumbers", "lotteryId", "issueNumber", "winAmount", "isPaid", "winningNumbers", "shibie", "serialNumber", "form", "rules", "required", "message", "trigger", "computed", "_objectSpread2", "default", "mapState", "gameMethodsData", "state", "game", "methodsData", "reconciledWinningAmount", "_this", "length", "filter", "winning", "isWinningReconciled", "winId", "reduce", "amount", "parseFloat", "totalWinningAmount", "created", "initMobileDetection", "getList", "getCustomerList", "getGameMethods", "getIssueList", "getSerialNumberList", "restoreReconciliationState", "mounted", "window", "addEventListener", "handleScroll", "handleClearAllData", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "clearTimeout", "methods", "innerWidth", "_this2", "newIsMobile", "$nextTick", "$forceUpdate", "toggleMobileSearch", "toggleMobileCard", "item", "index", "$set", "mobileExpanded", "exitReconciliationMode", "toggleReconciliationMode", "_this3", "listDraw", "then", "response", "rows", "_this4", "listCustomer", "_this5", "params", "isShiBie<PERSON>ch", "trim", "listWinning", "filteredRows", "searchText", "row", "includes", "sortWinningListForReconciliation", "for<PERSON>ach", "$refs", "winningTable", "toggleRowExpansion", "resetExpandState", "cancel", "reset", "betNumber", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "handleRowClick", "column", "event", "type", "property", "console", "log", "handleAdd", "handleUpdate", "_this6", "getWinning", "submitForm", "_this7", "validate", "valid", "updateWinning", "$modal", "msgSuccess", "addWinning", "handleDelete", "_this8", "winIds", "confirm", "delWinning", "catch", "handleExport", "_this9", "exportParams", "formattedData", "formatDataForExport", "exportToExcel", "msgError", "finally", "_this0", "betNumbersFormatted", "betData", "JSON", "parse", "numbers", "Array", "isArray", "firstNum", "concat", "danma", "tuoma", "num", "Object", "values", "join", "e", "winningNumbersFormatted", "winData", "b", "undefined", "c", "a", "formatAmountForExport", "toFixed", "getUserName", "getLotteryName", "getMethodName", "_this1", "Promise", "resolve", "_interopRequireWildcard2", "excel", "tHeader", "filterVal", "exportData", "v", "j", "export_json_to_excel", "header", "filename", "parseTime", "Date", "autoWidth", "bookType", "method", "find", "Number", "methodName", "user", "lotteryMap", "handlePay", "_this10", "handleBatchPay", "_this11", "promises", "all", "error", "formatAmount", "enterReconciliationMode", "_this12", "allDataParams", "saveReconciliationState", "_defineProperty2", "clearReconciliationState", "_this13", "sort", "aSerialNumber", "bSerialNumber", "doLayout", "mark<PERSON><PERSON>ingAsReconciled", "push", "unmarkWinningAsReconciled", "indexOf", "splice", "toggleWinningReconciliation", "reconciliationData", "timestamp", "now", "localStorage", "setItem", "stringify", "_this14", "savedData", "getItem", "reconciledCount", "hasOriginalParams", "removeItem", "_this15", "detail", "setTimeout", "$message", "info", "_this16", "reconciliationNotice", "noticeElement", "rect", "getBoundingClientRect", "scrollTop", "pageYOffset", "document", "documentElement", "top", "handleCancelPay", "_this17", "cancelPay", "_this18", "getAllMethods", "$store", "commit", "_this19", "listSerial", "_toConsumableArray2", "Set", "serialNumbers", "getSerialNumberFromWinning", "_this20", "toggleExpandAll", "_this21", "_this22", "handleShiBieInput", "_this23", "handleShiBieClear", "sortByAmount", "amountA", "amountB"], "sources": ["src/views/game/winning/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 移动端搜索按钮 -->\r\n    <div v-if=\"isMobile\" class=\"mobile-search-toggle\">\r\n      <el-button\r\n        type=\"primary\"\r\n        icon=\"el-icon-search\"\r\n        size=\"small\"\r\n        @click=\"toggleMobileSearch\"\r\n        class=\"mobile-search-btn\">\r\n        {{ showMobileSearch ? '收起搜索' : '展开搜索' }}\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-container\" v-show=\"isMobile ? showMobileSearch : showSearch\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"!isMobile\" label-width=\"80px\" :class=\"['search-form', { 'mobile-search-form': isMobile }]\">\r\n        <el-form-item label=\"中奖用户\" prop=\"userId\">\r\n          <el-select\r\n            v-model=\"queryParams.userId\"\r\n            placeholder=\"请选择中奖用户\"\r\n            clearable\r\n            filterable\r\n            @change=\"handleQuery\"\r\n            prefix-icon=\"el-icon-user\"\r\n            :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in userList\"\r\n              :key=\"item.userId\"\r\n              :label=\"item.name\"\r\n              :value=\"item.userId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"流水号\" prop=\"serialNumber\">\r\n          <el-select\r\n            v-model=\"queryParams.serialNumber\"\r\n            placeholder=\"请选择流水号\"\r\n            clearable\r\n            filterable\r\n            @change=\"handleQuery\"\r\n            prefix-icon=\"el-icon-document\"\r\n            :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in serialNumberList\"\r\n              :key=\"item\"\r\n              :label=\"item\"\r\n              :value=\"item\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n      <!-- <el-form-item label=\"彩种名称\" prop=\"lotteryId\">\r\n        <el-select\r\n          v-model=\"queryParams.lotteryId\"\r\n          placeholder=\"请选择彩种\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option label=\"福彩3D\" value=\"1\" />\r\n          <el-option label=\"体彩排三\" value=\"2\" />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"玩法名称\" prop=\"methodId\">\r\n        <el-select\r\n          v-model=\"queryParams.methodId\"\r\n          placeholder=\"请选择玩法名称\"\r\n          clearable\r\n          filterable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in gameMethodsData\"\r\n            :key=\"item.methodId\"\r\n            :label=\"item.methodName\"\r\n            :value=\"item.methodId\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"中奖期号\" prop=\"issueNumber\">\r\n        <el-select\r\n          v-model=\"queryParams.issueNumber\"\r\n          placeholder=\"请选择中奖期号\"\r\n          clearable\r\n          filterable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in issueList\"\r\n            :key=\"item.qihao\"\r\n            :label=\"item.qihao\"\r\n            :value=\"item.qihao\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n\r\n        <el-form-item label=\"识别框\" prop=\"shibie\">\r\n          <el-input\r\n            v-model=\"queryParams.shibie\"\r\n            placeholder=\"请输入识别内容（支持模糊搜索）\"\r\n            clearable\r\n            type=\"textarea\"\r\n            prefix-icon=\"el-icon-search\"\r\n            :style=\"isMobile ? 'width: 100%;' : 'width: 350px;'\"\r\n            @input=\"handleShiBieInput\"\r\n            @clear=\"handleShiBieClear\"\r\n          />\r\n          <div v-if=\"queryParams.shibie && queryParams.shibie.trim()\" class=\"search-tip\">\r\n            <i class=\"el-icon-info\"></i>\r\n            模糊搜索模式：将显示所有包含\"{{ queryParams.shibie.trim() }}\"的记录\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\" class=\"search-btn\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" class=\"reset-btn\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n   <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n       <el-col :span=\"1.5\">\r\n        <el-popconfirm\r\n          title=\"确认要批量赔付选中的奖金吗？\"\r\n          @confirm=\"handleBatchPay\"\r\n        >\r\n          <el-button\r\n            slot=\"reference\"\r\n            type=\"primary\"\r\n            plain\r\n            icon=\"el-icon-s-finance\"\r\n            size=\"mini\"\r\n            :disabled=\"multiple\"\r\n            v-hasPermi=\"['game:winning:edit']\"\r\n          >批量赔付</el-button>\r\n        </el-popconfirm>\r\n      </el-col>\r\n   \r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['game:winning:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['game:winning:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row> -->\r\n\r\n    <!-- 工具栏 -->\r\n    <div class=\"toolbar-container\" :class=\"{ 'mobile-toolbar': isMobile }\">\r\n      <div class=\"toolbar-content\">\r\n        <div class=\"toolbar-left\" :class=\"{ 'mobile-toolbar-left': isMobile }\">\r\n          <el-button\r\n            type=\"primary\"\r\n            :icon=\"allExpanded ? 'el-icon-minus' : 'el-icon-plus'\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"toggleExpandAll\"\r\n            class=\"toolbar-btn expand-btn\"\r\n          >{{ isMobile ? (allExpanded ? '收起' : '展开') : (allExpanded ? '收起全部' : '展开全部') }}</el-button>\r\n          <el-button\r\n            type=\"info\"\r\n            icon=\"el-icon-refresh\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"getList\"\r\n            class=\"toolbar-btn refresh-btn\"\r\n          >{{ isMobile ? '刷新' : '刷新数据' }}</el-button>\r\n          <el-button\r\n            type=\"warning\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"handleExport\"\r\n            v-hasPermi=\"['game:winning:export']\"\r\n            class=\"toolbar-btn export-btn\"\r\n          >导出</el-button>\r\n          <el-button\r\n            :type=\"isReconciliationMode ? 'danger' : 'success'\"\r\n            :icon=\"isReconciliationMode ? 'el-icon-close' : 'el-icon-check'\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"toggleReconciliationMode\"\r\n            :loading=\"reconciliationLoading\"\r\n            class=\"toolbar-btn reconciliation-btn\"\r\n          >{{ isReconciliationMode ? '退出对账' : '对账' }}</el-button>\r\n        </div>\r\n        <div class=\"toolbar-right\">\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 对账模式提示框 -->\r\n    <div v-if=\"isReconciliationMode\" ref=\"reconciliationNotice\" class=\"reconciliation-notice\" :class=\"{ 'reconciliation-fixed': isReconciliationFixed }\">\r\n      <div class=\"reconciliation-alert\">\r\n        <div class=\"reconciliation-header\">\r\n          <i class=\"el-icon-info\"></i>\r\n          <span class=\"reconciliation-title\">对账模式</span>\r\n        </div>\r\n        <div class=\"reconciliation-content\">\r\n          <p class=\"reconciliation-instruction\">\r\n            <strong style=\"color: #ff4d4f;\">操作提示：</strong>点击表格中蓝色虚线框的\r\n            <span class=\"highlight-amount\">\"中奖金额: ￥xxx.xx\"</span>\r\n            区域即可标记该记录为已对账！\r\n          </p>\r\n          <div class=\"reconciliation-stats\">\r\n            <span class=\"stats-text\">\r\n              已对账中奖记录数量：\r\n              <span class=\"reconciled-count\">{{ reconciledWinningIds.length }}</span> /\r\n              <span class=\"total-count\">{{ winningList.length }}</span>\r\n            </span>\r\n            <span class=\"stats-text\" style=\"margin-left: 20px;\">\r\n              已对账中奖金额：\r\n              <span class=\"reconciled-amount\">￥{{ formatAmount(reconciledWinningAmount) }}</span> /\r\n              <span class=\"total-amount\">￥{{ formatAmount(totalWinningAmount) }}</span>\r\n            </span>\r\n            <el-button\r\n              v-if=\"isReconciliationFixed\"\r\n              type=\"danger\"\r\n              size=\"mini\"\r\n              icon=\"el-icon-close\"\r\n              @click=\"exitReconciliationMode\"\r\n              class=\"exit-reconciliation-btn\">\r\n              退出对账\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格 -->\r\n    <div class=\"table-container\" :class=\"{ 'mobile-table-container': isMobile }\">\r\n      <!-- 移动端卡片式布局 -->\r\n      <div v-if=\"isMobile\" class=\"mobile-card-container\" :class=\"{ 'reconciliation-mode': isReconciliationMode }\">\r\n        <!-- 移动端对账模式提示 -->\r\n        <div v-if=\"isReconciliationMode\" class=\"mobile-reconciliation-notice\">\r\n          <div class=\"reconciliation-header\">\r\n            <i class=\"el-icon-info\"></i>\r\n            <span>对账模式</span>\r\n            <el-button type=\"text\" @click=\"exitReconciliationMode\" class=\"exit-btn\">\r\n              <i class=\"el-icon-close\"></i>\r\n            </el-button>\r\n          </div>\r\n          <div class=\"reconciliation-stats\">\r\n            <span class=\"stat-item\">已对账: <strong>{{ reconciledWinningIds.length }}</strong></span>\r\n            <span class=\"stat-item\">总额: <strong>¥{{ reconciledWinningAmount.toFixed(2) }}</strong></span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 移动端卡片列表 -->\r\n        <div v-for=\"(item, index) in winningList\" :key=\"item.winId\" class=\"mobile-card\"\r\n             :class=\"{ 'reconciled': isReconciliationMode && isWinningReconciled(item.winId) }\"\r\n             @click=\"!isReconciliationMode ? toggleMobileCard(item, index) : null\">\r\n\r\n          <!-- 卡片头部 -->\r\n          <div class=\"card-header\">\r\n            <!-- 左侧点击区域：展开/收起卡片 -->\r\n            <div class=\"card-header-left\" @click=\"isReconciliationMode ? toggleMobileCard(item, index) : null\">\r\n              <div class=\"winning-info\">\r\n                <div class=\"user-name\">{{ getUserName(item.userId) }}</div>\r\n                <div class=\"winning-meta\">\r\n                  <span class=\"serial-number\">流水号: {{ item.serialNumber }}</span>\r\n                  <span class=\"lottery-type\">\r\n                    <el-tag v-if=\"item.lotteryId === 1\" type=\"danger\" size=\"mini\">福彩</el-tag>\r\n                    <el-tag v-else-if=\"item.lotteryId === 2\" type=\"primary\" size=\"mini\">体彩</el-tag>\r\n                    <span v-else>{{ item.lotteryId }}</span>\r\n                  </span>\r\n                  <span class=\"method-name\">{{ getMethodName(item.methodId) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右侧点击区域：对账操作（仅在对账模式下可点击） -->\r\n            <div class=\"card-header-right\"\r\n                 :class=\"{ 'reconciliation-clickable': isReconciliationMode }\"\r\n                 @click=\"isReconciliationMode ? toggleWinningReconciliation(item.winId) : null\">\r\n              <div class=\"amount-info\">\r\n                <div class=\"win-amount\">{{ formatAmount(item.winAmount) }}</div>\r\n                <div v-if=\"isReconciliationMode\" class=\"reconciliation-status\"\r\n                     :class=\"{ 'reconciled': isWinningReconciled(item.winId) }\">\r\n                  {{ isWinningReconciled(item.winId) ? '已对账' : '待对账' }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 卡片内容（展开时显示） -->\r\n          <div v-if=\"item.mobileExpanded\" class=\"card-content\">\r\n            <div v-if=\"item.shibie\" class=\"detail-row\">\r\n              <span class=\"detail-label\">识别框:</span>\r\n              <span class=\"detail-value\">{{ item.shibie }}</span>\r\n            </div>\r\n            <div class=\"detail-row\">\r\n              <span class=\"detail-label\">下注号码:</span>\r\n              <div class=\"detail-value\">\r\n                <div v-if=\"item.betNumber\">\r\n                  <!-- 胆拖玩法显示 -->\r\n                  <div v-if=\"item.methodId >= 44 && item.methodId <= 59\">\r\n                    <template v-if=\"JSON.parse(item.betNumber).numbers.length > 0\">\r\n                      <el-tag type=\"danger\" size=\"mini\" style=\"margin: 2px;\">\r\n                        胆{{ JSON.parse(item.betNumber).numbers[0].danma }}\r\n                      </el-tag>\r\n                      <el-tag size=\"mini\" style=\"margin: 2px;\">\r\n                        拖{{ JSON.parse(item.betNumber).numbers[0].tuoma }}\r\n                      </el-tag>\r\n                    </template>\r\n                  </div>\r\n                  <!-- 跨度玩法显示 -->\r\n                  <div v-else-if=\"item.methodId >= 60 && item.methodId <= 69\">\r\n                    <el-tag size=\"mini\" style=\"margin: 2px;\">跨度{{ item.methodId - 60 }}</el-tag>\r\n                  </div>\r\n                  <!-- 其他玩法显示 -->\r\n                  <div v-else>\r\n                    <template v-if=\"typeof item.betNumber === 'string'\">\r\n                      <div v-for=\"(num, numIndex) in JSON.parse(item.betNumber).numbers\" :key=\"numIndex\" style=\"display: inline-block; margin: 2px;\">\r\n                        <el-tag size=\"mini\" style=\"margin: 1px;\">\r\n                          <template v-if=\"num.a !== undefined && num.b !== undefined && num.c !== undefined\">\r\n                            {{ num.a }}{{ num.b }}{{ num.c }}\r\n                          </template>\r\n                          <template v-else-if=\"num.a !== undefined && num.b !== undefined\">\r\n                            {{ num.a }}{{ num.b }}\r\n                          </template>\r\n                          <template v-else-if=\"num.a !== undefined\">\r\n                            {{ num.a }}\r\n                          </template>\r\n                          <template v-else>\r\n                            {{ Object.values(num).join('') }}\r\n                          </template>\r\n                        </el-tag>\r\n                      </div>\r\n                    </template>\r\n                    <span v-else>{{ item.betNumbers }}</span>\r\n                  </div>\r\n                </div>\r\n                <span v-else>{{ item.betNumbers }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"detail-row\">\r\n              <span class=\"detail-label\">中奖号码:</span>\r\n              <div class=\"detail-value\">\r\n                <div v-if=\"item.winningNumbers\">\r\n                  <template v-if=\"typeof item.winningNumbers === 'string'\">\r\n                    <div v-for=\"(winItem, winIndex) in JSON.parse(item.winningNumbers).winning\" :key=\"winIndex\">\r\n                      <el-tag type=\"success\" size=\"mini\" style=\"margin: 1px;\">\r\n                        <template v-if=\"winItem.b === undefined && winItem.c === undefined\">\r\n                          {{ winItem.a }}\r\n                        </template>\r\n                        <template v-else-if=\"winItem.c === undefined\">\r\n                          {{ winItem.a }}.{{ winItem.b }}\r\n                        </template>\r\n                        <template v-else>\r\n                          {{ winItem.a }}.{{ winItem.b }}.{{ winItem.c }}\r\n                        </template>\r\n                      </el-tag>\r\n                    </div>\r\n                  </template>\r\n                  <span v-else>{{ item.winningNumbers }}</span>\r\n                </div>\r\n                <span v-else>{{ item.winningNumbers }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 桌面端表格 -->\r\n      <el-table v-else\r\n        v-loading=\"loading\"\r\n        :data=\"winningList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        @row-click=\"handleRowClick\"\r\n        ref=\"winningTable\"\r\n        border\r\n        :header-cell-style=\"{ background: '#f8f9fa', color: '#606266', fontWeight: 'bold' }\"\r\n      >\r\n        <el-table-column type=\"expand\" width=\"60\">\r\n          <template slot-scope=\"props\">\r\n            <div class=\"expand-content\">\r\n              <div class=\"expand-header\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>详细信息</span>\r\n              </div>\r\n              <el-form label-position=\"left\" inline class=\"table-expand\">\r\n                <el-form-item label=\"号码识别\" class=\"full-width\">\r\n                  <div class=\"expand-value\">{{ props.row.shibie || '无' }}</div>\r\n                </el-form-item>\r\n                <el-form-item label=\"下注号码\" class=\"full-width\">\r\n                  <div class=\"expand-value\">\r\n                    <div v-if=\"props.row.betNumber\" class=\"number-tags\">\r\n                      <!-- 胆拖玩法详情显示 -->\r\n                      <div v-if=\"props.row.methodId >= 44 && props.row.methodId <= 59\">\r\n                        <template v-if=\"JSON.parse(props.row.betNumber).numbers.length > 0\">\r\n                          <el-tag type=\"danger\" effect=\"dark\" size=\"medium\" style=\"margin: 4px; font-size: 16px; font-weight: bold;\">\r\n                            胆码: {{ JSON.parse(props.row.betNumber).numbers[0].danma }}\r\n                          </el-tag>\r\n                          <el-tag type=\"info\" effect=\"dark\" size=\"medium\" style=\"margin: 4px; font-size: 16px; font-weight: bold;\">\r\n                            拖码: {{ JSON.parse(props.row.betNumber).numbers[0].tuoma }}\r\n                          </el-tag>\r\n                        </template>\r\n                      </div>\r\n\r\n                      <!-- 跨度玩法详情显示 -->\r\n                      <div v-else-if=\"props.row.methodId >= 60 && props.row.methodId <= 69\">\r\n                        <el-tag type=\"warning\" effect=\"dark\" size=\"medium\" style=\"margin: 4px; font-size: 16px; font-weight: bold;\">\r\n                          跨度值: {{ props.row.methodId - 60 }}\r\n                        </el-tag>\r\n                      </div>\r\n\r\n                      <!-- 原有玩法详情显示 -->\r\n                      <div v-else>\r\n                        <el-tag\r\n                          v-for=\"(item, index) in JSON.parse(props.row.betNumber).numbers\"\r\n                          :key=\"index\"\r\n                          type=\"primary\"\r\n                          effect=\"dark\"\r\n                          size=\"small\"\r\n                          class=\"number-tag bet-tag\"\r\n                        >\r\n                          {{ Object.values(item).join('.') }}\r\n                        </el-tag>\r\n                      </div>\r\n                    </div>\r\n                    <span v-else class=\"no-data\">{{ props.row.betNumbers || '无' }}</span>\r\n                  </div>\r\n                </el-form-item>\r\n                <el-form-item label=\"中奖号码\" class=\"full-width\">\r\n                  <div class=\"expand-value\">\r\n                    <div v-if=\"props.row.winningNumbers\" class=\"number-tags\">\r\n                      <template v-if=\"typeof props.row.winningNumbers === 'string'\">\r\n                        <el-tag\r\n                          v-for=\"(item, index) in JSON.parse(props.row.winningNumbers).winning\"\r\n                          :key=\"index\"\r\n                          type=\"success\"\r\n                          effect=\"dark\"\r\n                          size=\"small\"\r\n                          class=\"number-tag winning-tag\"\r\n                        >\r\n                          <template v-if=\"item.b === undefined && item.c === undefined\">\r\n                            {{ item.a }}\r\n                          </template>\r\n                          <template v-else-if=\"item.c === undefined\">\r\n                            {{ item.a }}.{{ item.b }}\r\n                          </template>\r\n                          <template v-else>\r\n                            {{ item.a }}.{{ item.b }}.{{ item.c }}\r\n                          </template>\r\n                        </el-tag>\r\n                      </template>\r\n                      <template v-else>\r\n                        <span class=\"no-data\">{{ props.row.winningNumbers }}</span>\r\n                      </template>\r\n                    </div>\r\n                    <span v-else class=\"no-data\">{{ props.row.winningNumbers || '无' }}</span>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </template>\r\n      </el-table-column>\r\n        <el-table-column label=\"中奖用户\" align=\"center\" prop=\"userId\" min-width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"user-info\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>{{ getUserName(scope.row.userId) }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"彩种\" align=\"center\" prop=\"lotteryId\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"lottery-type\">\r\n              <el-tag v-if=\"scope.row.lotteryId === 1\" type=\"danger\" effect=\"dark\" size=\"small\" class=\"lottery-tag\">福彩</el-tag>\r\n              <el-tag v-else-if=\"scope.row.lotteryId === 2\" type=\"primary\" effect=\"dark\" size=\"small\" class=\"lottery-tag\">体彩</el-tag>\r\n              <span v-else class=\"lottery-unknown\">{{ scope.row.lotteryId }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"玩法名称\" align=\"center\" prop=\"methodId\" min-width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"method-info\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n              <span>{{ getMethodName(scope.row.methodId) }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      <el-table-column label=\"下注号码\" align=\"center\" prop=\"betNumber\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.betNumber\">\r\n            <!-- 胆拖玩法显示 (method_id 44-59) -->\r\n            <div v-if=\"scope.row.methodId >= 44 && scope.row.methodId <= 59\" style=\"white-space: nowrap;\">\r\n              <template v-if=\"JSON.parse(scope.row.betNumber).numbers.length > 0\">\r\n                <el-tag type=\"danger\" effect=\"dark\" size=\"mini\" style=\"margin: 1px; font-weight: bold;\">\r\n                  胆{{ JSON.parse(scope.row.betNumber).numbers[0].danma }}\r\n                </el-tag>\r\n                <el-tag effect=\"dark\" size=\"mini\" style=\"margin: 1px; font-weight: bold;\">\r\n                  拖{{ JSON.parse(scope.row.betNumber).numbers[0].tuoma }}\r\n                </el-tag>\r\n              </template>\r\n            </div>\r\n\r\n            <!-- 跨度玩法显示 (method_id 60-69) -->\r\n            <div v-else-if=\"scope.row.methodId >= 60 && scope.row.methodId <= 69\" style=\"white-space: nowrap;\">\r\n              <el-tag  effect=\"dark\" size=\"mini\" style=\"margin: 1px; font-weight: bold;\">\r\n                跨度{{ scope.row.methodId - 60 }}\r\n              </el-tag>\r\n            </div>\r\n\r\n            <!-- 原有玩法显示保持不变 -->\r\n            <div v-else style=\"white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">\r\n              <div v-for=\"(item, index) in JSON.parse(scope.row.betNumber).numbers\" :key=\"index\" style=\"display: inline-block;\">\r\n                <el-tag\r\n                  type=\"primary\"\r\n                  effect=\"dark\"\r\n                  size=\"small\"\r\n                  style=\"margin: 2px; font-weight: bold;\"\r\n                >\r\n                  {{ Object.values(item).join('.') }}\r\n                </el-tag>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <span v-else style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.betNumbers }}</span>\r\n        </template>\r\n      </el-table-column>\r\n         <el-table-column label=\"中奖号码\" align=\"center\" prop=\"winningNumbers\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.winningNumbers\" style=\"white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">\r\n            <template v-if=\"typeof scope.row.winningNumbers === 'string'\">\r\n              <div v-for=\"(item, index) in JSON.parse(scope.row.winningNumbers).winning\" :key=\"index\"\r\n                style=\"display: inline-block;\">\r\n                <el-tag type=\"success\" effect=\"dark\" size=\"small\" style=\"margin: 2px; font-weight: bold;\">\r\n                  <template v-if=\"item.b === undefined && item.c === undefined\">\r\n                    {{ item.a }}\r\n                  </template>\r\n                  <template v-else-if=\"item.c === undefined\">\r\n                    {{ item.a }}.{{ item.b }}\r\n                  </template>\r\n                  <template v-else>\r\n                    {{ item.a }}.{{ item.b }}.{{ item.c }}\r\n                  </template>\r\n                </el-tag>\r\n              </div>\r\n            </template>\r\n            <template v-else>\r\n              <span style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.winningNumbers }}</span>\r\n            </template>\r\n          </div>\r\n          <span v-else style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.winningNumbers }}</span>\r\n        </template>\r\n      </el-table-column>\r\n    \r\n        <el-table-column label=\"流水号\" align=\"center\" prop=\"serialNumber\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"serial-number\">\r\n              <span class=\"serial-value\">{{ scope.row.serialNumber }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"中奖金额\" align=\"center\" prop=\"winAmount\" min-width=\"120\" sortable :sort-method=\"sortByAmount\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"isReconciliationMode\"\r\n                 class=\"reconciliation-amount-container\"\r\n                 :class=\"{ 'reconciled': isWinningReconciled(scope.row.winId) }\"\r\n                 @click.stop=\"toggleWinningReconciliation(scope.row.winId)\">\r\n              <span class=\"reconciliation-amount-text\">\r\n                中奖金额: {{ formatAmount(scope.row.winAmount) }}\r\n              </span>\r\n              <span class=\"reconciliation-status-badge\"\r\n                    :class=\"{ 'reconciled': isWinningReconciled(scope.row.winId) }\">\r\n                {{ isWinningReconciled(scope.row.winId) ? '已对账' : '待对账' }}\r\n              </span>\r\n            </div>\r\n            <span v-else class=\"amount-simple\">{{ formatAmount(scope.row.winAmount) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      <!-- <el-table-column label=\"是否赔付\" align=\"center\" prop=\"isPaid\">\r\n        <template slot-scope=\"scope\">\r\n          <span style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.isPaid === 1 ? '是' : '否' }}</span>\r\n        </template>\r\n      </el-table-column> -->\r\n   \r\n      <!-- <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popconfirm\r\n            title=\"确认要赔付该笔奖金吗？\"\r\n            @confirm=\"handlePay(scope.row)\"\r\n            v-if=\"scope.row.isPaid === 0\"\r\n          >\r\n            <el-button\r\n              slot=\"reference\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-s-finance\"\r\n              style=\"font-size: 14px; font-weight: bold;\"\r\n              v-hasPermi=\"['game:winning:edit']\"\r\n            >赔付</el-button>\r\n          </el-popconfirm>\r\n          <span v-else style=\"color: #56575AFF; font-size: 14px; font-weight: 700;\">已赔付</span>\r\n        </template>\r\n      </el-table-column> -->\r\n      </el-table>\r\n    </div>\r\n\r\n    <pagination\r\n      v-show=\"total > 0 && !isReconciliationMode\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改中奖管理对话框 -->\r\n    <!-- <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body :close-on-click-modal=\"false\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"中奖用户\" prop=\"userId\">\r\n          <el-input v-model=\"form.userId\" placeholder=\"请输入中奖用户\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"玩法名称\" prop=\"methodId\">\r\n          <el-input v-model=\"form.methodId\" placeholder=\"请输入玩法名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"下注号码\" prop=\"betNumber\">\r\n          <el-input v-model=\"form.betNumber\" placeholder=\"请输入下注号码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"彩种名称\" prop=\"lotteryId\">\r\n          <el-input v-model=\"form.lotteryId\" placeholder=\"请输入彩种名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"中奖期号\" prop=\"issueNumber\">\r\n          <el-input v-model=\"form.issueNumber\" placeholder=\"请输入中奖期号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"中奖金额\" prop=\"winAmount\">\r\n          <el-input v-model=\"form.winAmount\" placeholder=\"请输入中奖金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否赔付\" prop=\"isPaid\">\r\n          <el-input v-model=\"form.isPaid\" placeholder=\"请输入是否赔付\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"中奖号码\" prop=\"winningNumbers\">\r\n          <el-input v-model=\"form.winningNumbers\" placeholder=\"请输入中奖号码\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listWinning, getWinning, delWinning, addWinning, updateWinning, exportWinning } from \"@/api/game/winning\"\r\nimport { listCustomer } from \"@/api/game/customer\"\r\nimport { mapState } from 'vuex'\r\nimport { listUser } from \"@/api/system/user\"\r\nimport { getAllMethods } from \"@/api/game/method\"\r\nimport { listDraw } from \"@/api/game/draw\"\r\nimport { parseTime } from '@/utils/ruoyi'\r\nimport { cancelPay } from \"@/api/game/winning\"\r\nimport { listSerial } from \"@/api/game/serial\"\r\n\r\nexport default {\r\n  name: \"Winning\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 中奖管理表格数据\r\n      winningList: [],\r\n      // 是否全部展开\r\n      allExpanded: false,\r\n      // 移动端相关\r\n      isMobile: false,\r\n      showMobileSearch: false,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 用户列表\r\n      userList: [],\r\n      // 期号列表\r\n      issueList: [],\r\n      // 流水号列表\r\n      serialNumberList: [],\r\n      // 识别框搜索防抖定时器\r\n      shiBieSearchTimer: null,\r\n      // 对账模式相关\r\n      isReconciliationMode: false,\r\n      reconciliationLoading: false,\r\n      reconciledWinningIds: [], // 已对账的中奖记录ID数组（响应式）\r\n      originalQueryParams: null, // 保存原始查询参数\r\n      isReconciliationFixed: false, // 对账提示框是否固定在顶部\r\n      reconciliationOriginalTop: 0, // 对账提示框原始位置\r\n      scrollDebounceTimer: null, // 滚动防抖定时器\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        userId: null,\r\n        methodId: null,\r\n        betNumbers: null,\r\n        lotteryId: null,\r\n        issueNumber: null,\r\n        winAmount: null,\r\n        isPaid: null,\r\n        winningNumbers: null,\r\n        shibie: null,\r\n        serialNumber: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userId: [\r\n          { required: true, message: \"中奖用户不能为空\", trigger: \"blur\" }\r\n        ],\r\n        methodId: [\r\n          { required: true, message: \"玩法名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        lotteryId: [\r\n          { required: true, message: \"彩种名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        issueNumber: [\r\n          { required: true, message: \"中奖期号不能为空\", trigger: \"blur\" }\r\n        ],\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      gameMethodsData: state => state.game.methodsData\r\n    }),\r\n    /** 计算已对账的中奖金额总和 */\r\n    reconciledWinningAmount() {\r\n      if (!this.isReconciliationMode || !this.winningList || this.winningList.length === 0) {\r\n        return 0;\r\n      }\r\n\r\n      return this.winningList\r\n        .filter(winning => this.isWinningReconciled(winning.winId))\r\n        .reduce((total, winning) => {\r\n          const amount = parseFloat(winning.winAmount) || 0;\r\n          return total + amount;\r\n        }, 0);\r\n    },\r\n    /** 计算所有中奖金额总和 */\r\n    totalWinningAmount() {\r\n      if (!this.winningList || this.winningList.length === 0) {\r\n        return 0;\r\n      }\r\n\r\n      return this.winningList.reduce((total, winning) => {\r\n        const amount = parseFloat(winning.winAmount) || 0;\r\n        return total + amount;\r\n      }, 0);\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化移动端检测\r\n    this.initMobileDetection();\r\n\r\n    this.getList()\r\n    this.getCustomerList()\r\n    this.getGameMethods()\r\n    this.getIssueList()\r\n    this.getSerialNumberList()\r\n    // 恢复对账模式状态\r\n    this.restoreReconciliationState()\r\n  },\r\n  mounted() {\r\n    // 监听滚动事件\r\n    window.addEventListener('scroll', this.handleScroll);\r\n    // 监听一键清空事件\r\n    window.addEventListener('clearAllData', this.handleClearAllData);\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize);\r\n  },\r\n  beforeDestroy() {\r\n    // 移除滚动监听\r\n    window.removeEventListener('scroll', this.handleScroll);\r\n    // 移除一键清空事件监听\r\n    window.removeEventListener('clearAllData', this.handleClearAllData);\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.handleResize);\r\n\r\n    // 清理防抖定时器\r\n    if (this.scrollDebounceTimer) {\r\n      clearTimeout(this.scrollDebounceTimer);\r\n      this.scrollDebounceTimer = null;\r\n    }\r\n  },\r\n  methods: {\r\n    /** 初始化移动端检测 */\r\n    initMobileDetection() {\r\n      if (typeof window !== 'undefined') {\r\n        this.isMobile = window.innerWidth <= 768;\r\n      }\r\n    },\r\n\r\n    /** 处理窗口大小变化 */\r\n    handleResize() {\r\n      if (typeof window !== 'undefined') {\r\n        const newIsMobile = window.innerWidth <= 768;\r\n        if (this.isMobile !== newIsMobile) {\r\n          this.isMobile = newIsMobile;\r\n          // 在移动端和桌面端切换时，重置搜索框状态\r\n          if (!newIsMobile && this.showMobileSearch) {\r\n            this.showMobileSearch = false;\r\n          }\r\n          // 强制重新渲染以适应新的屏幕尺寸\r\n          this.$nextTick(() => {\r\n            this.$forceUpdate();\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 切换移动端搜索框显示 */\r\n    toggleMobileSearch() {\r\n      this.showMobileSearch = !this.showMobileSearch;\r\n    },\r\n\r\n    /** 切换移动端卡片展开状态 */\r\n    toggleMobileCard(item, index) {\r\n      this.$set(item, 'mobileExpanded', !item.mobileExpanded);\r\n    },\r\n\r\n    /** 退出对账模式 */\r\n    exitReconciliationMode() {\r\n      this.toggleReconciliationMode();\r\n    },\r\n\r\n    /** 获取期号列表 */\r\n    getIssueList() {\r\n      listDraw().then(response => {\r\n        this.issueList = response.rows\r\n      })\r\n    },\r\n    /** 获取用户列表 */\r\n    getCustomerList() {\r\n      listCustomer().then(response => {\r\n        this.userList = response.rows\r\n      })\r\n    },\r\n    /** 查询中奖管理列表 */\r\n    getList() {\r\n      this.loading = true\r\n      const params = { ...this.queryParams };\r\n\r\n      // 如果是对账模式，获取所有数据\r\n      if (this.isReconciliationMode) {\r\n        params.pageNum = 1;\r\n        params.pageSize = 999999;\r\n      }\r\n\r\n      // 记录是否是识别框搜索\r\n      const isShiBieSearch = params.shibie && params.shibie.trim() !== '';\r\n\r\n      // 如果有识别框搜索内容\r\n      if (isShiBieSearch) {\r\n        // 去除首尾空格\r\n        params.shibie = params.shibie.trim();\r\n        // 对于识别框搜索，不传递给后端，而是获取所有数据进行前端过滤\r\n        delete params.shibie;\r\n      }\r\n\r\n      listWinning(params).then(response => {\r\n        let filteredRows = response.rows;\r\n\r\n        // 如果有识别框搜索内容且有返回结果，进行前端过滤\r\n        if (isShiBieSearch && filteredRows.length > 0) {\r\n          const searchText = this.queryParams.shibie.trim();\r\n\r\n          // 模糊匹配：包含搜索文本的所有记录\r\n          filteredRows = filteredRows.filter(row =>\r\n            row.shibie && row.shibie.includes(searchText)\r\n          );\r\n\r\n          // 更新总数为过滤后的数量\r\n          response.total = filteredRows.length;\r\n        }\r\n\r\n        this.winningList = filteredRows;\r\n        this.total = response.total;\r\n\r\n        // 如果是对账模式，强制正序排序\r\n        if (this.isReconciliationMode) {\r\n          this.sortWinningListForReconciliation();\r\n        }\r\n\r\n        this.loading = false;\r\n\r\n        // 如果是识别框搜索，自动展开所有搜索结果\r\n        if (isShiBieSearch) {\r\n          this.$nextTick(() => {\r\n            this.allExpanded = true;\r\n            this.winningList.forEach((row) => {\r\n              this.$refs.winningTable.toggleRowExpansion(row, true);\r\n            });\r\n          });\r\n        } else {\r\n          // 非识别框搜索，重置展开状态\r\n          this.resetExpandState();\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        winId: null,\r\n        userId: null,\r\n        methodId: null,\r\n        betNumber: null,\r\n        lotteryId: null,\r\n        issueNumber: null,\r\n        winAmount: null,\r\n        isPaid: null,\r\n        winningNumbers: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.winId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 处理表格行点击 */\r\n    handleRowClick(row, column, event) {\r\n      // 避免点击操作按钮时触发行展开\r\n      if (column && column.type === 'selection') {\r\n        return;\r\n      }\r\n\r\n      // 在对账模式下，如果点击的是中奖金额列，不展开行\r\n      if (this.isReconciliationMode && column && column.property === 'winAmount') {\r\n        return;\r\n      }\r\n\r\n      console.log('中奖记录表格行点击:', row.winId || 'unknown');\r\n\r\n      // 切换行的展开状态\r\n      if (this.$refs.winningTable) {\r\n        this.$refs.winningTable.toggleRowExpansion(row);\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加中奖管理\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const winId = row.winId || this.ids\r\n      getWinning(winId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改中奖管理\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.winId != null) {\r\n            updateWinning(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addWinning(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const winIds = row.winId || this.ids\r\n      this.$modal.confirm('是否确认删除中奖管理编号为\"' + winIds + '\"的数据项？').then(function() {\r\n        return delWinning(winIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出所有中奖管理数据项？').then(() => {\r\n        this.exportLoading = true\r\n        // 获取所有数据用于导出\r\n        const exportParams = {\r\n          ...this.queryParams,\r\n          pageNum: 1,\r\n          pageSize: 10000 // 获取大量数据\r\n        }\r\n\r\n        listWinning(exportParams).then(response => {\r\n          const data = response.rows\r\n          const formattedData = this.formatDataForExport(data)\r\n          this.exportToExcel(formattedData)\r\n        }).catch(() => {\r\n          this.$modal.msgError(\"导出失败\")\r\n        }).finally(() => {\r\n          this.exportLoading = false\r\n        })\r\n      }).catch(() => {})\r\n    },\r\n\r\n    /** 格式化导出数据，与页面显示保持一致 */\r\n    formatDataForExport(data) {\r\n      return data.map(item => {\r\n        // 格式化下注号码\r\n        let betNumbersFormatted = ''\r\n        if (item.betNumber) {\r\n          try {\r\n            const betData = JSON.parse(item.betNumber)\r\n            if (betData.numbers && Array.isArray(betData.numbers)) {\r\n              // 胆拖玩法格式化 (method_id 44-59)\r\n              if (item.methodId >= 44 && item.methodId <= 59) {\r\n                if (betData.numbers.length > 0) {\r\n                  const firstNum = betData.numbers[0]\r\n                  betNumbersFormatted = `胆${firstNum.danma} 拖${firstNum.tuoma}`\r\n                }\r\n              }\r\n              // 跨度玩法格式化 (method_id 60-69)\r\n              else if (item.methodId >= 60 && item.methodId <= 69) {\r\n                betNumbersFormatted = `跨度${item.methodId - 60}`\r\n              }\r\n              // 原有玩法格式化\r\n              else {\r\n                betNumbersFormatted = betData.numbers.map(num => Object.values(num).join('.')).join(', ')\r\n              }\r\n            }\r\n          } catch (e) {\r\n            betNumbersFormatted = item.betNumbers || ''\r\n          }\r\n        } else {\r\n          betNumbersFormatted = item.betNumbers || ''\r\n        }\r\n\r\n        // 格式化中奖号码\r\n        let winningNumbersFormatted = ''\r\n        if (item.winningNumbers) {\r\n          try {\r\n            if (typeof item.winningNumbers === 'string') {\r\n              const winData = JSON.parse(item.winningNumbers)\r\n              if (winData.winning && Array.isArray(winData.winning)) {\r\n                winningNumbersFormatted = winData.winning.map(num => {\r\n                  if (num.b === undefined && num.c === undefined) {\r\n                    return num.a\r\n                  } else if (num.c === undefined) {\r\n                    return `${num.a}.${num.b}`\r\n                  } else {\r\n                    return `${num.a}.${num.b}.${num.c}`\r\n                  }\r\n                }).join(', ')\r\n              }\r\n            } else {\r\n              winningNumbersFormatted = item.winningNumbers\r\n            }\r\n          } catch (e) {\r\n            winningNumbersFormatted = item.winningNumbers || ''\r\n          }\r\n        }\r\n\r\n        // 格式化中奖金额（去掉￥符号）\r\n        const formatAmountForExport = (amount) => {\r\n          if (!amount && amount !== 0) return '0.00'\r\n          const num = parseFloat(amount)\r\n          return num.toFixed(2)\r\n        }\r\n\r\n        return {\r\n          '中奖用户': this.getUserName(item.userId),\r\n          '彩种': this.getLotteryName(item.lotteryId),\r\n          '玩法名称': this.getMethodName(item.methodId),\r\n          '下注号码': betNumbersFormatted,\r\n          '中奖号码': winningNumbersFormatted,\r\n          '流水号': item.serialNumber,\r\n          '中奖金额': formatAmountForExport(item.winAmount),\r\n          '号码识别': item.shibie || ''\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出到Excel */\r\n    exportToExcel(data) {\r\n      import('@/utils/Export2Excel').then(excel => {\r\n        const tHeader = ['中奖用户', '彩种', '玩法名称', '下注号码', '中奖号码', '流水号', '中奖金额', '号码识别']\r\n        const filterVal = ['中奖用户', '彩种', '玩法名称', '下注号码', '中奖号码', '流水号', '中奖金额', '号码识别']\r\n        const exportData = data.map(v => filterVal.map(j => v[j]))\r\n\r\n        excel.export_json_to_excel({\r\n          header: tHeader,\r\n          data: exportData,\r\n          filename: `中奖管理_${this.parseTime(new Date(), '{y}{m}{d}_{h}{i}{s}')}`,\r\n          autoWidth: true,\r\n          bookType: 'xlsx'\r\n        })\r\n\r\n        this.$modal.msgSuccess(\"导出成功\")\r\n      }).catch(() => {\r\n        this.$modal.msgError(\"导出失败\")\r\n      })\r\n    },\r\n    /** 获取玩法名称 */\r\n    getMethodName(methodId) {\r\n      if (!this.gameMethodsData || !this.gameMethodsData.length) {\r\n        return methodId\r\n      }\r\n      const method = this.gameMethodsData.find(item => Number(item.methodId) === Number(methodId))\r\n      return method ? method.methodName : methodId\r\n    },\r\n    /** 获取用户名称 */\r\n    getUserName(userId) {\r\n      if (!this.userList || !this.userList.length) {\r\n        return userId\r\n      }\r\n      const user = this.userList.find(item => Number(item.userId) === Number(userId))\r\n      return user ? user.name : userId\r\n    },\r\n    /** 获取彩种名称 */\r\n    getLotteryName(lotteryId) {\r\n      const lotteryMap = {\r\n        '1': '福彩3D',\r\n        '2': '体彩排三'\r\n      }\r\n      return lotteryMap[lotteryId] || lotteryId\r\n    },\r\n    /** 处理赔付 */\r\n    handlePay(row) {\r\n      updateWinning({\r\n        winId: row.winId,\r\n        isPaid: 1\r\n      }).then(response => {\r\n        this.$modal.msgSuccess(\"赔付成功\")\r\n        this.getList()\r\n      })\r\n    },\r\n    /** 批量赔付 */\r\n    handleBatchPay() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请选择要赔付的记录\")\r\n        return\r\n      }\r\n      this.loading = true\r\n      const promises = this.ids.map(winId => \r\n        updateWinning({\r\n          winId: winId,\r\n          isPaid: 1\r\n        })\r\n      )\r\n      Promise.all(promises).then(() => {\r\n        this.$modal.msgSuccess(\"批量赔付成功\")\r\n        this.getList()\r\n      }).catch(error => {\r\n        this.$modal.msgError(\"批量赔付失败：\" + (error.message || \"未知错误\"))\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    formatAmount(amount) {\r\n      if (amount === null || amount === undefined) {\r\n        return '￥0.00'\r\n      }\r\n      return '￥' + parseFloat(amount).toFixed(2)\r\n    },\r\n    /** 切换对账模式 */\r\n    toggleReconciliationMode() {\r\n      if (!this.isReconciliationMode) {\r\n        // 进入对账模式\r\n        this.enterReconciliationMode();\r\n      } else {\r\n        // 退出对账模式\r\n        this.exitReconciliationMode();\r\n      }\r\n    },\r\n    /** 进入对账模式 */\r\n    enterReconciliationMode() {\r\n      // 保存原始查询参数\r\n      this.originalQueryParams = { ...this.queryParams };\r\n\r\n      // 清空已对账的中奖记录ID数组\r\n      this.reconciledWinningIds = [];\r\n\r\n      // 重置固定状态和防抖定时器\r\n      this.isReconciliationFixed = false;\r\n      this.reconciliationOriginalTop = 0;\r\n      if (this.scrollDebounceTimer) {\r\n        clearTimeout(this.scrollDebounceTimer);\r\n        this.scrollDebounceTimer = null;\r\n      }\r\n\r\n      // 设置为获取所有数据（不分页）\r\n      const allDataParams = {\r\n        ...this.queryParams,\r\n        pageNum: 1,\r\n        pageSize: 999999 // 设置一个很大的数值来获取所有数据\r\n      };\r\n\r\n      this.loading = true;\r\n\r\n      listWinning(allDataParams).then(response => {\r\n        this.winningList = response.rows;\r\n        this.total = response.rows.length;\r\n\r\n        // 强制正序排序（对账模式下按winId正序）\r\n        this.sortWinningListForReconciliation();\r\n\r\n        // 设置对账模式\r\n        this.isReconciliationMode = true;\r\n\r\n        // 保存对账状态\r\n        this.saveReconciliationState();\r\n\r\n        this.loading = false;\r\n        this.reconciliationLoading = false;\r\n\r\n        this.$modal.msgSuccess(`已进入对账模式，共加载 ${response.rows.length} 条中奖记录，按流水号正序排列显示`);\r\n      }).catch(error => {\r\n        this.loading = false;\r\n        this.reconciliationLoading = false;\r\n        this.$modal.msgError('进入对账模式失败：' + error.message);\r\n      });\r\n    },\r\n    /** 退出对账模式 */\r\n    exitReconciliationMode() {\r\n      // 恢复原始查询参数\r\n      if (this.originalQueryParams) {\r\n        this.queryParams = { ...this.originalQueryParams };\r\n      }\r\n\r\n      // 清空已对账的中奖记录ID数组\r\n      this.reconciledWinningIds = [];\r\n\r\n      // 重置固定状态和清理定时器\r\n      this.isReconciliationFixed = false;\r\n      this.reconciliationOriginalTop = 0;\r\n      if (this.scrollDebounceTimer) {\r\n        clearTimeout(this.scrollDebounceTimer);\r\n        this.scrollDebounceTimer = null;\r\n      }\r\n\r\n      // 设置对账模式为false\r\n      this.isReconciliationMode = false;\r\n\r\n      // 清除对账状态\r\n      this.clearReconciliationState();\r\n\r\n      // 重新加载数据\r\n      this.getList();\r\n\r\n      this.$modal.msgSuccess('已退出对账模式');\r\n    },\r\n    /** 对账模式下的排序（强制正序） */\r\n    sortWinningListForReconciliation() {\r\n      if (!this.winningList || this.winningList.length === 0) {\r\n   \r\n        return;\r\n      }\r\n\r\n   \r\n\r\n      // 对中奖记录按流水号正序排序\r\n      this.winningList.sort((a, b) => {\r\n        const aSerialNumber = a.serialNumber || 0;\r\n        const bSerialNumber = b.serialNumber || 0;\r\n        return aSerialNumber - bSerialNumber; // 强制按流水号正序\r\n      });\r\n\r\n \r\n    \r\n\r\n      // 强制触发Vue的响应式更新\r\n      this.$forceUpdate();\r\n\r\n      // 确保表格重新渲染\r\n      this.$nextTick(() => {\r\n        if (this.$refs.winningTable) {\r\n          this.$refs.winningTable.doLayout();\r\n        }\r\n      });\r\n    },\r\n    /** 标记中奖记录为已对账 */\r\n    markWinningAsReconciled(winId) {\r\n      if (!this.reconciledWinningIds.includes(winId)) {\r\n        this.reconciledWinningIds.push(winId);\r\n        this.saveReconciliationState();\r\n      }\r\n    },\r\n    /** 取消中奖记录的对账状态 */\r\n    unmarkWinningAsReconciled(winId) {\r\n      const index = this.reconciledWinningIds.indexOf(winId);\r\n      if (index > -1) {\r\n        this.reconciledWinningIds.splice(index, 1);\r\n        this.saveReconciliationState();\r\n      }\r\n    },\r\n    /** 切换中奖记录的对账状态 */\r\n    toggleWinningReconciliation(winId) {\r\n      if (this.isWinningReconciled(winId)) {\r\n        this.unmarkWinningAsReconciled(winId);\r\n      } else {\r\n        this.markWinningAsReconciled(winId);\r\n      }\r\n    },\r\n    /** 检查中奖记录是否已对账 */\r\n    isWinningReconciled(winId) {\r\n      return this.reconciledWinningIds.includes(winId);\r\n    },\r\n    /** 保存对账状态到localStorage */\r\n    saveReconciliationState() {\r\n      try {\r\n        const reconciliationData = {\r\n          isReconciliationMode: this.isReconciliationMode,\r\n          reconciledWinningIds: this.reconciledWinningIds,\r\n          originalQueryParams: this.originalQueryParams,\r\n          timestamp: Date.now()\r\n        };\r\n        localStorage.setItem('winning_reconciliation_state', JSON.stringify(reconciliationData));\r\n      } catch (error) {\r\n        console.error('保存winning对账状态失败:', error);\r\n      }\r\n    },\r\n    /** 从localStorage恢复对账状态 */\r\n    restoreReconciliationState() {\r\n      try {\r\n        const savedData = localStorage.getItem('winning_reconciliation_state');\r\n        if (savedData) {\r\n          const reconciliationData = JSON.parse(savedData);\r\n\r\n          // 恢复对账模式状态\r\n          if (reconciliationData.isReconciliationMode) {\r\n            this.isReconciliationMode = true;\r\n            this.reconciledWinningIds = reconciliationData.reconciledWinningIds || [];\r\n            this.originalQueryParams = reconciliationData.originalQueryParams || null;\r\n\r\n            console.log('恢复winning对账模式状态:', {\r\n              reconciledCount: this.reconciledWinningIds.length,\r\n              hasOriginalParams: !!this.originalQueryParams\r\n            });\r\n\r\n            // 在下一个tick中重新加载数据以确保对账模式正确应用\r\n            this.$nextTick(() => {\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('恢复winning对账状态失败:', error);\r\n        // 清除损坏的数据\r\n        localStorage.removeItem('winning_reconciliation_state');\r\n      }\r\n    },\r\n    /** 清除对账状态 */\r\n    clearReconciliationState() {\r\n      try {\r\n        localStorage.removeItem('winning_reconciliation_state');\r\n      } catch (error) {\r\n        console.error('清除winning对账状态失败:', error);\r\n      }\r\n    },\r\n    /** 处理一键清空事件 */\r\n    handleClearAllData(event) {\r\n      console.log('winning页面收到一键清空事件:', event.detail);\r\n\r\n      // 只有当前页面处于对账模式时才处理，避免与record页面冲突\r\n      if (this.isReconciliationMode) {\r\n        // 添加短暂延迟，确保事件处理的顺序性\r\n        setTimeout(() => {\r\n          this.exitReconciliationMode();\r\n          console.log('winning页面因一键清空操作退出对账模式');\r\n          this.$message.info('检测到一键清空操作，已自动退出对账模式');\r\n        }, 100);\r\n      }\r\n    },\r\n    /** 处理滚动事件 */\r\n    handleScroll() {\r\n      if (!this.isReconciliationMode || !this.$refs.reconciliationNotice) {\r\n        return;\r\n      }\r\n\r\n      const noticeElement = this.$refs.reconciliationNotice;\r\n      const rect = noticeElement.getBoundingClientRect();\r\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\r\n\r\n      // 初始化原始位置（只在第一次计算）\r\n      if (this.reconciliationOriginalTop === 0 && !this.isReconciliationFixed) {\r\n        this.reconciliationOriginalTop = scrollTop + rect.top;\r\n      }\r\n\r\n      // 使用防抖处理，避免频繁切换\r\n      if (!this.scrollDebounceTimer) {\r\n        this.scrollDebounceTimer = setTimeout(() => {\r\n          // 如果对账提示框的顶部离开屏幕超过阈值，则固定它\r\n          if (rect.top <= 0 && !this.isReconciliationFixed) {\r\n            this.isReconciliationFixed = true;\r\n          }\r\n          // 如果滚动回到原始位置附近，则取消固定\r\n          else if (scrollTop <= this.reconciliationOriginalTop - 20 && this.isReconciliationFixed) {\r\n            this.isReconciliationFixed = false;\r\n          }\r\n\r\n          this.scrollDebounceTimer = null;\r\n        }, 16); // 约60fps的更新频率\r\n      }\r\n    },\r\n    /** 撤销结算按钮操作 */\r\n    handleCancelPay(row) {\r\n      this.$modal.confirm('是否确认撤销该笔奖金的结算？').then(() => {\r\n        return cancelPay(row.winId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"撤销结算成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 获取玩法列表 */\r\n    getGameMethods() {\r\n      getAllMethods().then(response => {\r\n        this.$store.commit('game/SET_METHODS_DATA', response.rows)\r\n      })\r\n    },\r\n    /** 获取流水号列表 */\r\n    getSerialNumberList() {\r\n      // 注意：listSerial接口已经在后端实现了用户隔离，所以这里获取的就是当前用户的流水号\r\n      listSerial({ pageNum: 1, pageSize: 1000 }).then(response => {\r\n        \r\n        if (response && response.rows && response.rows.length > 0) {\r\n          // 提取流水号并去重排序\r\n          this.serialNumberList = [...new Set(response.rows.map(item => item.serialNumbers))]\r\n            .filter(item => item != null)\r\n            .sort((a, b) => b - a); // 降序排列，最新的在前面\r\n          \r\n        } else {\r\n          console.log('没有找到流水号数据，尝试从中奖记录获取');\r\n          this.getSerialNumberFromWinning();\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取流水号列表失败，尝试从中奖记录获取:', error);\r\n        this.getSerialNumberFromWinning();\r\n      })\r\n    },\r\n    /** 从中奖记录中获取流水号列表 */\r\n    getSerialNumberFromWinning() {\r\n      // 从中奖记录中提取流水号\r\n      listWinning({ pageNum: 1, pageSize: 1000 }).then(response => {\r\n        if (response && response.rows && response.rows.length > 0) {\r\n          // 从中奖记录中提取流水号并去重排序\r\n          this.serialNumberList = [...new Set(response.rows.map(item => item.serialNumber))]\r\n            .filter(item => item != null)\r\n            .sort((a, b) => b - a); // 降序排列，最新的在前面\r\n          console.log('从中奖记录获取的流水号列表:', this.serialNumberList);\r\n        }\r\n      }).catch(error => {\r\n        console.error('从中奖记录获取流水号失败:', error);\r\n      })\r\n    },\r\n    /** 展开/收起全部 */\r\n    toggleExpandAll() {\r\n      this.allExpanded = !this.allExpanded;\r\n      this.$nextTick(() => {\r\n        if (this.allExpanded) {\r\n          // 展开所有行\r\n          this.winningList.forEach((row, index) => {\r\n            this.$refs.winningTable.toggleRowExpansion(row, true);\r\n          });\r\n        } else {\r\n          // 收起所有行\r\n          this.winningList.forEach((row, index) => {\r\n            this.$refs.winningTable.toggleRowExpansion(row, false);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 重置展开状态 */\r\n    resetExpandState() {\r\n      this.allExpanded = false;\r\n      this.$nextTick(() => {\r\n        // 收起所有行\r\n        this.winningList.forEach((row) => {\r\n          this.$refs.winningTable.toggleRowExpansion(row, false);\r\n        });\r\n      });\r\n    },\r\n    /** 识别框输入处理 */\r\n    handleShiBieInput() {\r\n      // 防抖处理，避免频繁搜索\r\n      if (this.shiBieSearchTimer) {\r\n        clearTimeout(this.shiBieSearchTimer);\r\n      }\r\n      this.shiBieSearchTimer = setTimeout(() => {\r\n        if (this.queryParams.shibie && this.queryParams.shibie.trim()) {\r\n          this.handleQuery();\r\n        }\r\n      }, 500); // 500ms 延迟\r\n    },\r\n    /** 识别框清空处理 */\r\n    handleShiBieClear() {\r\n      this.queryParams.shibie = '';\r\n      this.handleQuery();\r\n    },\r\n    /** 中奖金额排序方法 */\r\n    sortByAmount(a, b) {\r\n      const amountA = parseFloat(a.winAmount) || 0;\r\n      const amountB = parseFloat(b.winAmount) || 0;\r\n      return amountA - amountB;\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面头部样式 */\r\n.page-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.header-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28px;\r\n}\r\n\r\n.header-info h2 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n}\r\n\r\n.header-info p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-container {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.reset-btn:hover {\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 工具栏样式 */\r\n.toolbar-container {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 16px;\r\n  padding: 20px 24px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.toolbar-container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);\r\n}\r\n\r\n/* 工具栏布局 */\r\n.toolbar-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.toolbar-right {\r\n  margin-left: auto;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 工具栏按钮美化 */\r\n.toolbar-btn {\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\r\n  border-width: 1.5px !important;\r\n  padding: 8px 16px !important;\r\n  font-size: 13px !important;\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.toolbar-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n  transition: left 0.5s;\r\n}\r\n\r\n.toolbar-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.toolbar-btn:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n/* 展开按钮样式 */\r\n.expand-btn.el-button--primary {\r\n  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%) !important;\r\n  border-color: #409eff !important;\r\n  color: white !important;\r\n}\r\n\r\n.expand-btn.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%) !important;\r\n  border-color: #66b1ff !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.5) !important;\r\n}\r\n\r\n/* 刷新按钮样式 */\r\n.refresh-btn.el-button--info {\r\n  background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%) !important;\r\n  border-color: #909399 !important;\r\n  color: white !important;\r\n}\r\n\r\n.refresh-btn.el-button--info:hover {\r\n  background: linear-gradient(135deg, #b1b3b8 0%, #909399 100%) !important;\r\n  border-color: #b1b3b8 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(144, 147, 153, 0.5) !important;\r\n}\r\n\r\n/* 导出按钮样式 */\r\n.export-btn.el-button--warning.is-plain {\r\n  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;\r\n  border-color: #e6a23c !important;\r\n  color: white !important;\r\n}\r\n\r\n.export-btn.el-button--warning.is-plain:hover {\r\n  background: linear-gradient(135deg, #ebb563 0%, #e6a23c 100%) !important;\r\n  border-color: #ebb563 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(230, 162, 60, 0.5) !important;\r\n}\r\n\r\n/* 对账按钮样式 */\r\n.reconciliation-btn.el-button--success {\r\n  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;\r\n  border-color: #67c23a !important;\r\n  color: white !important;\r\n}\r\n\r\n.reconciliation-btn.el-button--success:hover {\r\n  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%) !important;\r\n  border-color: #85ce61 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(103, 194, 58, 0.5) !important;\r\n}\r\n\r\n.reconciliation-btn.el-button--danger {\r\n  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%) !important;\r\n  border-color: #f56c6c !important;\r\n  color: white !important;\r\n}\r\n\r\n.reconciliation-btn.el-button--danger:hover {\r\n  background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%) !important;\r\n  border-color: #f78989 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(245, 108, 108, 0.5) !important;\r\n}\r\n\r\n/* 禁用状态样式 */\r\n.toolbar-btn.is-disabled {\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cdd7 100%) !important;\r\n  border-color: #dcdfe6 !important;\r\n  color: #c0c4cc !important;\r\n  cursor: not-allowed !important;\r\n  transform: none !important;\r\n  box-shadow: none !important;\r\n}\r\n\r\n.toolbar-btn.is-disabled::before {\r\n  display: none;\r\n}\r\n\r\n/* 按钮图标样式优化 */\r\n.toolbar-btn [class*=\"el-icon-\"] {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 1200px) {\r\n  .toolbar-container {\r\n    padding: 16px 20px;\r\n  }\r\n\r\n  .toolbar-left {\r\n    gap: 8px;\r\n  }\r\n\r\n  .toolbar-btn {\r\n    padding: 6px 12px !important;\r\n    font-size: 12px !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .toolbar-container {\r\n    padding: 12px 16px;\r\n  }\r\n\r\n  .toolbar-content {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .toolbar-left {\r\n    gap: 6px;\r\n  }\r\n\r\n  .toolbar-right {\r\n    margin-left: 0;\r\n    align-self: flex-end;\r\n  }\r\n\r\n  .toolbar-btn {\r\n    padding: 4px 8px !important;\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .toolbar-btn [class*=\"el-icon-\"] {\r\n    margin-right: 4px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 展开内容样式 */\r\n.expand-content {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.expand-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 16px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.expand-header i {\r\n  color: #667eea;\r\n}\r\n\r\n.table-expand {\r\n  font-size: 0;\r\n}\r\n\r\n.table-expand label {\r\n  width: 90px;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.table-expand .el-form-item {\r\n  margin-right: 0;\r\n  margin-bottom: 12px;\r\n  width: 50%;\r\n}\r\n\r\n.table-expand .el-form-item.full-width {\r\n  width: 100%;\r\n  margin-top: 0;\r\n}\r\n\r\n.expand-value {\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.number-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n}\r\n\r\n.number-tag {\r\n  margin: 0;\r\n  font-weight: 600;\r\n  border-radius: 6px;\r\n}\r\n\r\n.bet-tag {\r\n  background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.winning-tag {\r\n  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);\r\n  border-color: #67C23A;\r\n}\r\n\r\n.no-data {\r\n  color: #909399;\r\n  font-style: italic;\r\n}\r\n\r\n/* 表格列样式 */\r\n.user-info, .method-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-info {\r\n  color: #409EFF;\r\n}\r\n\r\n.method-info {\r\n  color: #67C23A;\r\n}\r\n\r\n.serial-number {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.serial-value {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.lottery-type {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.lottery-tag {\r\n  font-weight: 600;\r\n  border-radius: 6px;\r\n}\r\n\r\n.lottery-unknown {\r\n  color: #909399;\r\n  font-weight: 600;\r\n}\r\n\r\n.amount-simple {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #F56C6C;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .page-header {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .table-expand .el-form-item {\r\n    width: 100%;\r\n  }\r\n\r\n  .number-tags {\r\n    justify-content: center;\r\n  }\r\n\r\n  /* 移动端搜索按钮样式 */\r\n  .mobile-search-toggle {\r\n    margin-bottom: 10px;\r\n    text-align: center;\r\n  }\r\n\r\n  .mobile-search-btn {\r\n    width: 100%;\r\n    max-width: 200px;\r\n  }\r\n\r\n  /* 移动端搜索表单样式 */\r\n  .mobile-search-form {\r\n    padding: 10px;\r\n    background: #f5f7fa;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .mobile-search-form .el-form-item {\r\n    margin-bottom: 10px;\r\n    margin-right: 0;\r\n    width: 100%;\r\n  }\r\n\r\n  .mobile-search-form .el-form-item__label {\r\n    width: 60px !important;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .mobile-search-form .el-input,\r\n  .mobile-search-form .el-select {\r\n    width: 100% !important;\r\n  }\r\n\r\n  /* 移动端识别框输入区域优化 */\r\n  .mobile-search-form .el-form-item .el-textarea {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .mobile-search-form .el-form-item .el-textarea .el-textarea__inner {\r\n    width: 100% !important;\r\n    max-width: 100% !important;\r\n    min-height: 60px !important;\r\n    max-height: 80px !important;\r\n    resize: vertical;\r\n    box-sizing: border-box;\r\n    font-size: 14px;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  /* 移动端工具栏样式 */\r\n  .mobile-toolbar .toolbar-content {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .mobile-toolbar-left {\r\n    display: flex;\r\n    flex-wrap: nowrap;\r\n    gap: 3px;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .mobile-toolbar-left .toolbar-btn {\r\n    flex: 1;\r\n    min-width: calc(25% - 6px);\r\n    max-width: calc(25% - 6px);\r\n    padding: 4px 2px !important;\r\n    font-size: 9px !important;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n  }\r\n\r\n  /* 移动端卡片布局 */\r\n  .mobile-card-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .mobile-card {\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    margin-bottom: 12px;\r\n    overflow: hidden;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .mobile-card.reconciled {\r\n    border: 2px solid #67c23a;\r\n    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);\r\n  }\r\n\r\n  .mobile-card .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .mobile-card .card-header-left {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .mobile-card .winning-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .mobile-card .user-name {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #409eff;\r\n    margin-bottom: 4px;\r\n  }\r\n\r\n  .mobile-card .winning-meta {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 12px;\r\n    color: #666;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .mobile-card .serial-number {\r\n    color: #2c3e50;\r\n    font-weight: 600;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .mobile-card .method-name {\r\n    color: #67c23a;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .mobile-card .card-header-right {\r\n    text-align: right;\r\n  }\r\n\r\n  .mobile-card .win-amount {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    color: #f56c6c;\r\n    margin-bottom: 4px;\r\n  }\r\n\r\n  .mobile-card .reconciliation-status {\r\n    font-size: 12px;\r\n    padding: 2px 6px;\r\n    border-radius: 4px;\r\n    background: #e6f7ff;\r\n    color: #1890ff;\r\n  }\r\n\r\n  .mobile-card .reconciliation-status.reconciled {\r\n    background: #f6ffed;\r\n    color: #52c41a;\r\n  }\r\n\r\n  .mobile-card .card-content {\r\n    padding: 12px;\r\n    background: #fafafa;\r\n  }\r\n\r\n  .mobile-card .detail-row {\r\n    display: flex;\r\n    margin-bottom: 8px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .mobile-card .detail-row:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .mobile-card .detail-label {\r\n    width: 70px;\r\n    font-size: 12px;\r\n    color: #666;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .mobile-card .detail-value {\r\n    flex: 1;\r\n    font-size: 12px;\r\n    color: #333;\r\n    word-break: break-all;\r\n  }\r\n\r\n  .mobile-card .detail-value .el-tag {\r\n    font-size: 10px;\r\n    padding: 0 4px;\r\n    margin: 1px;\r\n  }\r\n\r\n  /* 移动端对账模式提示 */\r\n  .mobile-reconciliation-notice {\r\n    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);\r\n    border: 1px solid #b3d8ff;\r\n    border-radius: 8px;\r\n    padding: 12px;\r\n    margin-bottom: 15px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .mobile-reconciliation-notice .reconciliation-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .mobile-reconciliation-notice .reconciliation-header i {\r\n    color: #409EFF;\r\n    margin-right: 6px;\r\n  }\r\n\r\n  .mobile-reconciliation-notice .reconciliation-stats {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .mobile-reconciliation-notice .stat-item {\r\n    color: #666;\r\n  }\r\n\r\n  .mobile-reconciliation-notice .exit-btn {\r\n    padding: 0;\r\n    font-size: 16px;\r\n    color: #f56c6c;\r\n  }\r\n\r\n  /* 移动端卡片点击样式 */\r\n  .mobile-card-container:not(.reconciliation-mode) .mobile-card {\r\n    cursor: pointer;\r\n  }\r\n\r\n  .mobile-card-container:not(.reconciliation-mode) .mobile-card:hover {\r\n    background-color: rgba(64, 158, 255, 0.05);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .reconciliation-mode .mobile-card .card-header-left {\r\n    cursor: pointer;\r\n    transition: background-color 0.2s ease;\r\n  }\r\n\r\n  .reconciliation-mode .mobile-card .card-header-left:hover {\r\n    background-color: rgba(64, 158, 255, 0.1);\r\n  }\r\n\r\n  .mobile-card .card-header-right.reconciliation-clickable {\r\n    cursor: pointer;\r\n    transition: background-color 0.2s ease;\r\n    border-radius: 4px;\r\n    padding: 4px;\r\n  }\r\n\r\n  .mobile-card .card-header-right.reconciliation-clickable:hover {\r\n    background-color: rgba(245, 108, 108, 0.1);\r\n  }\r\n}\r\n/* 对账模式提示样式 */\r\n.reconciliation-notice {\r\n  margin-bottom: 16px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.reconciliation-notice.reconciliation-fixed {\r\n  position: fixed;\r\n  top: 10px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  z-index: 1001;\r\n  width: calc(100% - 40px);\r\n  max-width: 1200px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  animation: slideDown 0.3s ease-out;\r\n  backdrop-filter: blur(8px);\r\n  background: rgba(255, 255, 255, 0.95);\r\n}\r\n\r\n@keyframes slideDown {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(-50%) translateY(-20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(-50%) translateY(0);\r\n  }\r\n}\r\n\r\n.reconciliation-alert {\r\n  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);\r\n  border: 2px solid #1890ff;\r\n  border-radius: 12px;\r\n  padding: 16px 20px;\r\n  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\n.reconciliation-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.reconciliation-header i {\r\n  font-size: 18px;\r\n  color: #1890ff;\r\n  margin-right: 8px;\r\n}\r\n\r\n.reconciliation-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #1890ff;\r\n}\r\n\r\n.reconciliation-content {\r\n  margin: 0;\r\n}\r\n\r\n.reconciliation-instruction {\r\n  background: linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%);\r\n  border: 2px solid #ffa940;\r\n  border-radius: 8px;\r\n  padding: 12px 16px;\r\n  margin: 12px 0 !important;\r\n  font-size: 15px;\r\n  line-height: 1.6;\r\n  box-shadow: 0 2px 8px rgba(255, 169, 64, 0.2);\r\n}\r\n\r\n.highlight-amount {\r\n  background: linear-gradient(135deg, #0080FFFF 0%, #067EFFFF 100%);\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 6px;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  border: 2px dashed white;\r\n  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);\r\n  animation: highlightPulse 2s infinite;\r\n}\r\n\r\n@keyframes highlightPulse {\r\n  0%, 100% {\r\n    transform: scale(1);\r\n    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);\r\n  }\r\n  50% {\r\n    transform: scale(1.05);\r\n    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.5);\r\n  }\r\n}\r\n\r\n.highlight-amount::after {\r\n  content: \" 👆\";\r\n  animation: bounce 1.5s infinite;\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-5px);\r\n  }\r\n  60% {\r\n    transform: translateY(-3px);\r\n  }\r\n}\r\n\r\n.reconciliation-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  font-weight: 600;\r\n  font-size: 15px;\r\n}\r\n\r\n.stats-text {\r\n  flex: 1;\r\n}\r\n\r\n.reconciled-count {\r\n  color: #1890ff;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.reconciled-amount {\r\n  color: #52c41a;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.total-amount {\r\n  color: #8c8c8c;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.total-count {\r\n  color: #52c41a;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.exit-reconciliation-btn {\r\n  margin-left: 16px;\r\n  padding: 4px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 6px rgba(245, 108, 108, 0.3);\r\n}\r\n\r\n.exit-reconciliation-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.5);\r\n}\r\n\r\n/* 对账模式中奖金额样式 */\r\n.reconciliation-amount-container {\r\n  border: 2px dashed #1890ff;\r\n  border-radius: 8px;\r\n  padding: 8px 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);\r\n  position: relative;\r\n  min-height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.reconciliation-amount-container:hover {\r\n  border-color: #40a9ff;\r\n  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);\r\n  transform: scale(1.02);\r\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);\r\n}\r\n\r\n.reconciliation-amount-container.reconciled {\r\n  border-color: #52c41a;\r\n  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);\r\n  border-style: solid;\r\n}\r\n\r\n.reconciliation-amount-container.reconciled:hover {\r\n  border-color: #73d13d;\r\n  background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);\r\n}\r\n\r\n.reconciliation-amount-text {\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  color: #FF0037FF;\r\n}\r\n\r\n.reconciliation-amount-container.reconciled .reconciliation-amount-text {\r\n  color: #FF0000FF;\r\n}\r\n\r\n.reconciled-icon {\r\n  position: absolute;\r\n  top: 2px;\r\n  right: 2px;\r\n  background-color: #EAFFEAFF !important;\r\n  color: #52c41a;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.reconciliation-status-badge {\r\n  position: absolute;\r\n  top: -1px;\r\n  right: -1px;\r\n  background: #ff4d4f;\r\n  color: white;\r\n  font-size: 10px;\r\n  padding: 2px 6px;\r\n  border-radius: 0 6px 0 8px;\r\n  font-weight: bold;\r\n  line-height: 1;\r\n  z-index: 10;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.reconciliation-status-badge.reconciled {\r\n  background: #52c41a;\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAypBA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AAEA,IAAAO,OAAA,GAAAP,OAAA;AAAA,IAAAQ,QAAA,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,aAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;MACA;MACAC,QAAA;MACAC,gBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,QAAA;MACA;MACAC,SAAA;MACA;MACAC,gBAAA;MACA;MACAC,iBAAA;MACA;MACAC,oBAAA;MACAC,qBAAA;MACAC,oBAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,qBAAA;MAAA;MACAC,yBAAA;MAAA;MACAC,mBAAA;MAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,WAAA;QACAC,SAAA;QACAC,MAAA;QACAC,cAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAX,MAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,QAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,SAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,WAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,cAAA;IACAC,eAAA,WAAAA,gBAAAC,KAAA;MAAA,OAAAA,KAAA,CAAAC,IAAA,CAAAC,WAAA;IAAA;EACA;IACA,mBACAC,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,KAAA;MACA,UAAAlC,oBAAA,UAAAV,WAAA,SAAAA,WAAA,CAAA6C,MAAA;QACA;MACA;MAEA,YAAA7C,WAAA,CACA8C,MAAA,WAAAC,OAAA;QAAA,OAAAH,KAAA,CAAAI,mBAAA,CAAAD,OAAA,CAAAE,KAAA;MAAA,GACAC,MAAA,WAAAnD,KAAA,EAAAgD,OAAA;QACA,IAAAI,MAAA,GAAAC,UAAA,CAAAL,OAAA,CAAAtB,SAAA;QACA,OAAA1B,KAAA,GAAAoD,MAAA;MACA;IACA;IACA,iBACAE,kBAAA,WAAAA,mBAAA;MACA,UAAArD,WAAA,SAAAA,WAAA,CAAA6C,MAAA;QACA;MACA;MAEA,YAAA7C,WAAA,CAAAkD,MAAA,WAAAnD,KAAA,EAAAgD,OAAA;QACA,IAAAI,MAAA,GAAAC,UAAA,CAAAL,OAAA,CAAAtB,SAAA;QACA,OAAA1B,KAAA,GAAAoD,MAAA;MACA;IACA;EAAA,EACA;EACAG,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,mBAAA;IAEA,KAAAC,OAAA;IACA,KAAAC,eAAA;IACA,KAAAC,cAAA;IACA,KAAAC,YAAA;IACA,KAAAC,mBAAA;IACA;IACA,KAAAC,0BAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,YAAA;IACA;IACAF,MAAA,CAAAC,gBAAA,sBAAAE,kBAAA;IACA;IACAH,MAAA,CAAAC,gBAAA,gBAAAG,YAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACAL,MAAA,CAAAM,mBAAA,gBAAAJ,YAAA;IACA;IACAF,MAAA,CAAAM,mBAAA,sBAAAH,kBAAA;IACA;IACAH,MAAA,CAAAM,mBAAA,gBAAAF,YAAA;;IAEA;IACA,SAAAnD,mBAAA;MACAsD,YAAA,MAAAtD,mBAAA;MACA,KAAAA,mBAAA;IACA;EACA;EACAuD,OAAA,GAAAjF,QAAA;IACA,eACAiE,mBAAA,WAAAA,oBAAA;MACA,WAAAQ,MAAA;QACA,KAAA7D,QAAA,GAAA6D,MAAA,CAAAS,UAAA;MACA;IACA;IAEA,eACAL,YAAA,WAAAA,aAAA;MAAA,IAAAM,MAAA;MACA,WAAAV,MAAA;QACA,IAAAW,WAAA,GAAAX,MAAA,CAAAS,UAAA;QACA,SAAAtE,QAAA,KAAAwE,WAAA;UACA,KAAAxE,QAAA,GAAAwE,WAAA;UACA;UACA,KAAAA,WAAA,SAAAvE,gBAAA;YACA,KAAAA,gBAAA;UACA;UACA;UACA,KAAAwE,SAAA;YACAF,MAAA,CAAAG,YAAA;UACA;QACA;MACA;IACA;IAEA,iBACAC,kBAAA,WAAAA,mBAAA;MACA,KAAA1E,gBAAA,SAAAA,gBAAA;IACA;IAEA,kBACA2E,gBAAA,WAAAA,iBAAAC,IAAA,EAAAC,KAAA;MACA,KAAAC,IAAA,CAAAF,IAAA,qBAAAA,IAAA,CAAAG,cAAA;IACA;IAEA,aACAC,sBAAA,WAAAA,uBAAA;MACA,KAAAC,wBAAA;IACA;IAEA,aACAzB,YAAA,WAAAA,aAAA;MAAA,IAAA0B,MAAA;MACA,IAAAC,cAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAA9E,SAAA,GAAAiF,QAAA,CAAAC,IAAA;MACA;IACA;IACA,aACAhC,eAAA,WAAAA,gBAAA;MAAA,IAAAiC,MAAA;MACA,IAAAC,sBAAA,IAAAJ,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAApF,QAAA,GAAAkF,QAAA,CAAAC,IAAA;MACA;IACA;IACA,eACAjC,OAAA,WAAAA,QAAA;MAAA,IAAAoC,MAAA;MACA,KAAAnG,OAAA;MACA,IAAAoG,MAAA,OAAAzD,cAAA,CAAAC,OAAA,WAAApB,WAAA;;MAEA;MACA,SAAAP,oBAAA;QACAmF,MAAA,CAAA3E,OAAA;QACA2E,MAAA,CAAA1E,QAAA;MACA;;MAEA;MACA,IAAA2E,cAAA,GAAAD,MAAA,CAAAjE,MAAA,IAAAiE,MAAA,CAAAjE,MAAA,CAAAmE,IAAA;;MAEA;MACA,IAAAD,cAAA;QACA;QACAD,MAAA,CAAAjE,MAAA,GAAAiE,MAAA,CAAAjE,MAAA,CAAAmE,IAAA;QACA;QACA,OAAAF,MAAA,CAAAjE,MAAA;MACA;MAEA,IAAAoE,oBAAA,EAAAH,MAAA,EAAAN,IAAA,WAAAC,QAAA;QACA,IAAAS,YAAA,GAAAT,QAAA,CAAAC,IAAA;;QAEA;QACA,IAAAK,cAAA,IAAAG,YAAA,CAAApD,MAAA;UACA,IAAAqD,UAAA,GAAAN,MAAA,CAAA3E,WAAA,CAAAW,MAAA,CAAAmE,IAAA;;UAEA;UACAE,YAAA,GAAAA,YAAA,CAAAnD,MAAA,WAAAqD,GAAA;YAAA,OACAA,GAAA,CAAAvE,MAAA,IAAAuE,GAAA,CAAAvE,MAAA,CAAAwE,QAAA,CAAAF,UAAA;UAAA,CACA;;UAEA;UACAV,QAAA,CAAAzF,KAAA,GAAAkG,YAAA,CAAApD,MAAA;QACA;QAEA+C,MAAA,CAAA5F,WAAA,GAAAiG,YAAA;QACAL,MAAA,CAAA7F,KAAA,GAAAyF,QAAA,CAAAzF,KAAA;;QAEA;QACA,IAAA6F,MAAA,CAAAlF,oBAAA;UACAkF,MAAA,CAAAS,gCAAA;QACA;QAEAT,MAAA,CAAAnG,OAAA;;QAEA;QACA,IAAAqG,cAAA;UACAF,MAAA,CAAAjB,SAAA;YACAiB,MAAA,CAAA3F,WAAA;YACA2F,MAAA,CAAA5F,WAAA,CAAAsG,OAAA,WAAAH,GAAA;cACAP,MAAA,CAAAW,KAAA,CAAAC,YAAA,CAAAC,kBAAA,CAAAN,GAAA;YACA;UACA;QACA;UACA;UACAP,MAAA,CAAAc,gBAAA;QACA;MACA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAtG,IAAA;MACA,KAAAuG,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA9E,IAAA;QACAmB,KAAA;QACA7B,MAAA;QACAC,QAAA;QACAwF,SAAA;QACAtF,SAAA;QACAC,WAAA;QACAC,SAAA;QACAC,MAAA;QACAC,cAAA;MACA;MACA,KAAAmF,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA9F,WAAA,CAAAC,OAAA;MACA,KAAAsC,OAAA;IACA;IACA,aACAwD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAvH,GAAA,GAAAuH,SAAA,CAAAC,GAAA,WAAApC,IAAA;QAAA,OAAAA,IAAA,CAAA9B,KAAA;MAAA;MACA,KAAArD,MAAA,GAAAsH,SAAA,CAAArE,MAAA;MACA,KAAAhD,QAAA,IAAAqH,SAAA,CAAArE,MAAA;IACA;IACA,cACAuE,cAAA,WAAAA,eAAAjB,GAAA,EAAAkB,MAAA,EAAAC,KAAA;MACA;MACA,IAAAD,MAAA,IAAAA,MAAA,CAAAE,IAAA;QACA;MACA;;MAEA;MACA,SAAA7G,oBAAA,IAAA2G,MAAA,IAAAA,MAAA,CAAAG,QAAA;QACA;MACA;MAEAC,OAAA,CAAAC,GAAA,eAAAvB,GAAA,CAAAlD,KAAA;;MAEA;MACA,SAAAsD,KAAA,CAAAC,YAAA;QACA,KAAAD,KAAA,CAAAC,YAAA,CAAAC,kBAAA,CAAAN,GAAA;MACA;IACA;IACA,aACAwB,SAAA,WAAAA,UAAA;MACA,KAAAf,KAAA;MACA,KAAAvG,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAwH,YAAA,WAAAA,aAAAzB,GAAA;MAAA,IAAA0B,MAAA;MACA,KAAAjB,KAAA;MACA,IAAA3D,KAAA,GAAAkD,GAAA,CAAAlD,KAAA,SAAAtD,GAAA;MACA,IAAAmI,mBAAA,EAAA7E,KAAA,EAAAsC,IAAA,WAAAC,QAAA;QACAqC,MAAA,CAAA/F,IAAA,GAAA0D,QAAA,CAAAhG,IAAA;QACAqI,MAAA,CAAAxH,IAAA;QACAwH,MAAA,CAAAzH,KAAA;MACA;IACA;IACA,WACA2H,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAzB,KAAA,SAAA0B,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAAlG,IAAA,CAAAmB,KAAA;YACA,IAAAkF,sBAAA,EAAAH,MAAA,CAAAlG,IAAA,EAAAyD,IAAA,WAAAC,QAAA;cACAwC,MAAA,CAAAI,MAAA,CAAAC,UAAA;cACAL,MAAA,CAAA3H,IAAA;cACA2H,MAAA,CAAAxE,OAAA;YACA;UACA;YACA,IAAA8E,mBAAA,EAAAN,MAAA,CAAAlG,IAAA,EAAAyD,IAAA,WAAAC,QAAA;cACAwC,MAAA,CAAAI,MAAA,CAAAC,UAAA;cACAL,MAAA,CAAA3H,IAAA;cACA2H,MAAA,CAAAxE,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA+E,YAAA,WAAAA,aAAApC,GAAA;MAAA,IAAAqC,MAAA;MACA,IAAAC,MAAA,GAAAtC,GAAA,CAAAlD,KAAA,SAAAtD,GAAA;MACA,KAAAyI,MAAA,CAAAM,OAAA,oBAAAD,MAAA,aAAAlD,IAAA;QACA,WAAAoD,mBAAA,EAAAF,MAAA;MACA,GAAAlD,IAAA;QACAiD,MAAA,CAAAhF,OAAA;QACAgF,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAV,MAAA,CAAAM,OAAA,qBAAAnD,IAAA;QACAuD,MAAA,CAAApJ,aAAA;QACA;QACA,IAAAqJ,YAAA,OAAA3G,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAyG,MAAA,CAAA7H,WAAA;UACAC,OAAA;UACAC,QAAA;QAAA,EACA;QAEA,IAAA6E,oBAAA,EAAA+C,YAAA,EAAAxD,IAAA,WAAAC,QAAA;UACA,IAAAhG,IAAA,GAAAgG,QAAA,CAAAC,IAAA;UACA,IAAAuD,aAAA,GAAAF,MAAA,CAAAG,mBAAA,CAAAzJ,IAAA;UACAsJ,MAAA,CAAAI,aAAA,CAAAF,aAAA;QACA,GAAAJ,KAAA;UACAE,MAAA,CAAAV,MAAA,CAAAe,QAAA;QACA,GAAAC,OAAA;UACAN,MAAA,CAAApJ,aAAA;QACA;MACA,GAAAkJ,KAAA;IACA;IAEA,wBACAK,mBAAA,WAAAA,oBAAAzJ,IAAA;MAAA,IAAA6J,MAAA;MACA,OAAA7J,IAAA,CAAA2H,GAAA,WAAApC,IAAA;QACA;QACA,IAAAuE,mBAAA;QACA,IAAAvE,IAAA,CAAA8B,SAAA;UACA;YACA,IAAA0C,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAA1E,IAAA,CAAA8B,SAAA;YACA,IAAA0C,OAAA,CAAAG,OAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAL,OAAA,CAAAG,OAAA;cACA;cACA,IAAA3E,IAAA,CAAA1D,QAAA,UAAA0D,IAAA,CAAA1D,QAAA;gBACA,IAAAkI,OAAA,CAAAG,OAAA,CAAA7G,MAAA;kBACA,IAAAgH,QAAA,GAAAN,OAAA,CAAAG,OAAA;kBACAJ,mBAAA,YAAAQ,MAAA,CAAAD,QAAA,CAAAE,KAAA,aAAAD,MAAA,CAAAD,QAAA,CAAAG,KAAA;gBACA;cACA;cACA;cAAA,KACA,IAAAjF,IAAA,CAAA1D,QAAA,UAAA0D,IAAA,CAAA1D,QAAA;gBACAiI,mBAAA,kBAAAQ,MAAA,CAAA/E,IAAA,CAAA1D,QAAA;cACA;cACA;cAAA,KACA;gBACAiI,mBAAA,GAAAC,OAAA,CAAAG,OAAA,CAAAvC,GAAA,WAAA8C,GAAA;kBAAA,OAAAC,MAAA,CAAAC,MAAA,CAAAF,GAAA,EAAAG,IAAA;gBAAA,GAAAA,IAAA;cACA;YACA;UACA,SAAAC,CAAA;YACAf,mBAAA,GAAAvE,IAAA,CAAAzD,UAAA;UACA;QACA;UACAgI,mBAAA,GAAAvE,IAAA,CAAAzD,UAAA;QACA;;QAEA;QACA,IAAAgJ,uBAAA;QACA,IAAAvF,IAAA,CAAApD,cAAA;UACA;YACA,WAAAoD,IAAA,CAAApD,cAAA;cACA,IAAA4I,OAAA,GAAAf,IAAA,CAAAC,KAAA,CAAA1E,IAAA,CAAApD,cAAA;cACA,IAAA4I,OAAA,CAAAxH,OAAA,IAAA4G,KAAA,CAAAC,OAAA,CAAAW,OAAA,CAAAxH,OAAA;gBACAuH,uBAAA,GAAAC,OAAA,CAAAxH,OAAA,CAAAoE,GAAA,WAAA8C,GAAA;kBACA,IAAAA,GAAA,CAAAO,CAAA,KAAAC,SAAA,IAAAR,GAAA,CAAAS,CAAA,KAAAD,SAAA;oBACA,OAAAR,GAAA,CAAAU,CAAA;kBACA,WAAAV,GAAA,CAAAS,CAAA,KAAAD,SAAA;oBACA,UAAAX,MAAA,CAAAG,GAAA,CAAAU,CAAA,OAAAb,MAAA,CAAAG,GAAA,CAAAO,CAAA;kBACA;oBACA,UAAAV,MAAA,CAAAG,GAAA,CAAAU,CAAA,OAAAb,MAAA,CAAAG,GAAA,CAAAO,CAAA,OAAAV,MAAA,CAAAG,GAAA,CAAAS,CAAA;kBACA;gBACA,GAAAN,IAAA;cACA;YACA;cACAE,uBAAA,GAAAvF,IAAA,CAAApD,cAAA;YACA;UACA,SAAA0I,CAAA;YACAC,uBAAA,GAAAvF,IAAA,CAAApD,cAAA;UACA;QACA;;QAEA;QACA,IAAAiJ,qBAAA,YAAAA,sBAAAzH,MAAA;UACA,KAAAA,MAAA,IAAAA,MAAA;UACA,IAAA8G,GAAA,GAAA7G,UAAA,CAAAD,MAAA;UACA,OAAA8G,GAAA,CAAAY,OAAA;QACA;QAEA;UACA,QAAAxB,MAAA,CAAAyB,WAAA,CAAA/F,IAAA,CAAA3D,MAAA;UACA,MAAAiI,MAAA,CAAA0B,cAAA,CAAAhG,IAAA,CAAAxD,SAAA;UACA,QAAA8H,MAAA,CAAA2B,aAAA,CAAAjG,IAAA,CAAA1D,QAAA;UACA,QAAAiI,mBAAA;UACA,QAAAgB,uBAAA;UACA,OAAAvF,IAAA,CAAAlD,YAAA;UACA,QAAA+I,qBAAA,CAAA7F,IAAA,CAAAtD,SAAA;UACA,QAAAsD,IAAA,CAAAnD,MAAA;QACA;MACA;IACA;IAEA,eACAsH,aAAA,WAAAA,cAAA1J,IAAA;MAAA,IAAAyL,MAAA;MACAC,OAAA,CAAAC,OAAA,GAAA5F,IAAA;QAAA,WAAA6F,wBAAA,CAAA/I,OAAA,EAAAvD,OAAA;MAAA,GAAAyG,IAAA,WAAA8F,KAAA;QACA,IAAAC,OAAA;QACA,IAAAC,SAAA;QACA,IAAAC,UAAA,GAAAhM,IAAA,CAAA2H,GAAA,WAAAsE,CAAA;UAAA,OAAAF,SAAA,CAAApE,GAAA,WAAAuE,CAAA;YAAA,OAAAD,CAAA,CAAAC,CAAA;UAAA;QAAA;QAEAL,KAAA,CAAAM,oBAAA;UACAC,MAAA,EAAAN,OAAA;UACA9L,IAAA,EAAAgM,UAAA;UACAK,QAAA,8BAAA/B,MAAA,CAAAmB,MAAA,CAAAa,SAAA,KAAAC,IAAA;UACAC,SAAA;UACAC,QAAA;QACA;QAEAhB,MAAA,CAAA7C,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;QACAqC,MAAA,CAAA7C,MAAA,CAAAe,QAAA;MACA;IACA;IACA,aACA6B,aAAA,WAAAA,cAAA3J,QAAA;MACA,UAAAkB,eAAA,UAAAA,eAAA,CAAAM,MAAA;QACA,OAAAxB,QAAA;MACA;MACA,IAAA6K,MAAA,QAAA3J,eAAA,CAAA4J,IAAA,WAAApH,IAAA;QAAA,OAAAqH,MAAA,CAAArH,IAAA,CAAA1D,QAAA,MAAA+K,MAAA,CAAA/K,QAAA;MAAA;MACA,OAAA6K,MAAA,GAAAA,MAAA,CAAAG,UAAA,GAAAhL,QAAA;IACA;IACA,aACAyJ,WAAA,WAAAA,YAAA1J,MAAA;MACA,UAAAd,QAAA,UAAAA,QAAA,CAAAuC,MAAA;QACA,OAAAzB,MAAA;MACA;MACA,IAAAkL,IAAA,QAAAhM,QAAA,CAAA6L,IAAA,WAAApH,IAAA;QAAA,OAAAqH,MAAA,CAAArH,IAAA,CAAA3D,MAAA,MAAAgL,MAAA,CAAAhL,MAAA;MAAA;MACA,OAAAkL,IAAA,GAAAA,IAAA,CAAA/M,IAAA,GAAA6B,MAAA;IACA;IACA,aACA2J,cAAA,WAAAA,eAAAxJ,SAAA;MACA,IAAAgL,UAAA;QACA;QACA;MACA;MACA,OAAAA,UAAA,CAAAhL,SAAA,KAAAA,SAAA;IACA;IACA,WACAiL,SAAA,WAAAA,UAAArG,GAAA;MAAA,IAAAsG,OAAA;MACA,IAAAtE,sBAAA;QACAlF,KAAA,EAAAkD,GAAA,CAAAlD,KAAA;QACAvB,MAAA;MACA,GAAA6D,IAAA,WAAAC,QAAA;QACAiH,OAAA,CAAArE,MAAA,CAAAC,UAAA;QACAoE,OAAA,CAAAjJ,OAAA;MACA;IACA;IACA,WACAkJ,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,SAAAhN,GAAA,CAAAkD,MAAA;QACA,KAAAuF,MAAA,CAAAe,QAAA;QACA;MACA;MACA,KAAA1J,OAAA;MACA,IAAAmN,QAAA,QAAAjN,GAAA,CAAAwH,GAAA,WAAAlE,KAAA;QAAA,OACA,IAAAkF,sBAAA;UACAlF,KAAA,EAAAA,KAAA;UACAvB,MAAA;QACA;MAAA,CACA;MACAwJ,OAAA,CAAA2B,GAAA,CAAAD,QAAA,EAAArH,IAAA;QACAoH,OAAA,CAAAvE,MAAA,CAAAC,UAAA;QACAsE,OAAA,CAAAnJ,OAAA;MACA,GAAAoF,KAAA,WAAAkE,KAAA;QACAH,OAAA,CAAAvE,MAAA,CAAAe,QAAA,cAAA2D,KAAA,CAAA7K,OAAA;MACA,GAAAmH,OAAA;QACAuD,OAAA,CAAAlN,OAAA;MACA;IACA;IACAsN,YAAA,WAAAA,aAAA5J,MAAA;MACA,IAAAA,MAAA,aAAAA,MAAA,KAAAsH,SAAA;QACA;MACA;MACA,aAAArH,UAAA,CAAAD,MAAA,EAAA0H,OAAA;IACA;IACA,aACAzF,wBAAA,WAAAA,yBAAA;MACA,UAAA1E,oBAAA;QACA;QACA,KAAAsM,uBAAA;MACA;QACA;QACA,KAAA7H,sBAAA;MACA;IACA;IACA,aACA6H,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAApM,mBAAA,OAAAuB,cAAA,CAAAC,OAAA,WAAApB,WAAA;;MAEA;MACA,KAAAL,oBAAA;;MAEA;MACA,KAAAE,qBAAA;MACA,KAAAC,yBAAA;MACA,SAAAC,mBAAA;QACAsD,YAAA,MAAAtD,mBAAA;QACA,KAAAA,mBAAA;MACA;;MAEA;MACA,IAAAkM,aAAA,OAAA9K,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,KAAApB,WAAA;QACAC,OAAA;QACAC,QAAA;MAAA,EACA;MAEA,KAAA1B,OAAA;MAEA,IAAAuG,oBAAA,EAAAkH,aAAA,EAAA3H,IAAA,WAAAC,QAAA;QACAyH,OAAA,CAAAjN,WAAA,GAAAwF,QAAA,CAAAC,IAAA;QACAwH,OAAA,CAAAlN,KAAA,GAAAyF,QAAA,CAAAC,IAAA,CAAA5C,MAAA;;QAEA;QACAoK,OAAA,CAAA5G,gCAAA;;QAEA;QACA4G,OAAA,CAAAvM,oBAAA;;QAEA;QACAuM,OAAA,CAAAE,uBAAA;QAEAF,OAAA,CAAAxN,OAAA;QACAwN,OAAA,CAAAtM,qBAAA;QAEAsM,OAAA,CAAA7E,MAAA,CAAAC,UAAA,uEAAAyB,MAAA,CAAAtE,QAAA,CAAAC,IAAA,CAAA5C,MAAA;MACA,GAAA+F,KAAA,WAAAkE,KAAA;QACAG,OAAA,CAAAxN,OAAA;QACAwN,OAAA,CAAAtM,qBAAA;QACAsM,OAAA,CAAA7E,MAAA,CAAAe,QAAA,eAAA2D,KAAA,CAAA7K,OAAA;MACA;IACA;EAAA,OAAAmL,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,EAAA/C,QAAA,qCAAA6F,uBAAA,EAEA;IACA;IACA,SAAAtE,mBAAA;MACA,KAAAI,WAAA,OAAAmB,cAAA,CAAAC,OAAA,WAAAxB,mBAAA;IACA;;IAEA;IACA,KAAAD,oBAAA;;IAEA;IACA,KAAAE,qBAAA;IACA,KAAAC,yBAAA;IACA,SAAAC,mBAAA;MACAsD,YAAA,MAAAtD,mBAAA;MACA,KAAAA,mBAAA;IACA;;IAEA;IACA,KAAAN,oBAAA;;IAEA;IACA,KAAA2M,wBAAA;;IAEA;IACA,KAAA7J,OAAA;IAEA,KAAA4E,MAAA,CAAAC,UAAA;EACA,iDAEAhC,iCAAA;IAAA,IAAAiH,OAAA;IACA,UAAAtN,WAAA,SAAAA,WAAA,CAAA6C,MAAA;MAEA;IACA;;IAIA;IACA,KAAA7C,WAAA,CAAAuN,IAAA,WAAA5C,CAAA,EAAAH,CAAA;MACA,IAAAgD,aAAA,GAAA7C,CAAA,CAAA9I,YAAA;MACA,IAAA4L,aAAA,GAAAjD,CAAA,CAAA3I,YAAA;MACA,OAAA2L,aAAA,GAAAC,aAAA;IACA;;IAKA;IACA,KAAA7I,YAAA;;IAEA;IACA,KAAAD,SAAA;MACA,IAAA2I,OAAA,CAAA/G,KAAA,CAAAC,YAAA;QACA8G,OAAA,CAAA/G,KAAA,CAAAC,YAAA,CAAAkH,QAAA;MACA;IACA;EACA,wCAEAC,wBAAA1K,KAAA;IACA,UAAArC,oBAAA,CAAAwF,QAAA,CAAAnD,KAAA;MACA,KAAArC,oBAAA,CAAAgN,IAAA,CAAA3K,KAAA;MACA,KAAAkK,uBAAA;IACA;EACA,0CAEAU,0BAAA5K,KAAA;IACA,IAAA+B,KAAA,QAAApE,oBAAA,CAAAkN,OAAA,CAAA7K,KAAA;IACA,IAAA+B,KAAA;MACA,KAAApE,oBAAA,CAAAmN,MAAA,CAAA/I,KAAA;MACA,KAAAmI,uBAAA;IACA;EACA,4CAEAa,4BAAA/K,KAAA;IACA,SAAAD,mBAAA,CAAAC,KAAA;MACA,KAAA4K,yBAAA,CAAA5K,KAAA;IACA;MACA,KAAA0K,uBAAA,CAAA1K,KAAA;IACA;EACA,oCAEAD,oBAAAC,KAAA;IACA,YAAArC,oBAAA,CAAAwF,QAAA,CAAAnD,KAAA;EACA,wCAEAkK,wBAAA;IACA;MACA,IAAAc,kBAAA;QACAvN,oBAAA,OAAAA,oBAAA;QACAE,oBAAA,OAAAA,oBAAA;QACAC,mBAAA,OAAAA,mBAAA;QACAqN,SAAA,EAAAnC,IAAA,CAAAoC,GAAA;MACA;MACAC,YAAA,CAAAC,OAAA,iCAAA7E,IAAA,CAAA8E,SAAA,CAAAL,kBAAA;IACA,SAAAnB,KAAA;MACArF,OAAA,CAAAqF,KAAA,qBAAAA,KAAA;IACA;EACA,2CAEAjJ,2BAAA;IAAA,IAAA0K,OAAA;IACA;MACA,IAAAC,SAAA,GAAAJ,YAAA,CAAAK,OAAA;MACA,IAAAD,SAAA;QACA,IAAAP,kBAAA,GAAAzE,IAAA,CAAAC,KAAA,CAAA+E,SAAA;;QAEA;QACA,IAAAP,kBAAA,CAAAvN,oBAAA;UACA,KAAAA,oBAAA;UACA,KAAAE,oBAAA,GAAAqN,kBAAA,CAAArN,oBAAA;UACA,KAAAC,mBAAA,GAAAoN,kBAAA,CAAApN,mBAAA;UAEA4G,OAAA,CAAAC,GAAA;YACAgH,eAAA,OAAA9N,oBAAA,CAAAiC,MAAA;YACA8L,iBAAA,SAAA9N;UACA;;UAEA;UACA,KAAA8D,SAAA;YACA4J,OAAA,CAAA/K,OAAA;UACA;QACA;MACA;IACA,SAAAsJ,KAAA;MACArF,OAAA,CAAAqF,KAAA,qBAAAA,KAAA;MACA;MACAsB,YAAA,CAAAQ,UAAA;IACA;EACA,yCAEAvB,yBAAA;IACA;MACAe,YAAA,CAAAQ,UAAA;IACA,SAAA9B,KAAA;MACArF,OAAA,CAAAqF,KAAA,qBAAAA,KAAA;IACA;EACA,mCAEA5I,mBAAAoD,KAAA;IAAA,IAAAuH,OAAA;IACApH,OAAA,CAAAC,GAAA,uBAAAJ,KAAA,CAAAwH,MAAA;;IAEA;IACA,SAAApO,oBAAA;MACA;MACAqO,UAAA;QACAF,OAAA,CAAA1J,sBAAA;QACAsC,OAAA,CAAAC,GAAA;QACAmH,OAAA,CAAAG,QAAA,CAAAC,IAAA;MACA;IACA;EACA,QAAA7B,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,MAAA+K,gBAAA,CAAA/K,OAAA,EAAA/C,QAAA,2BAEA2E,aAAA;IAAA,IAAAiL,OAAA;IACA,UAAAxO,oBAAA,UAAA6F,KAAA,CAAA4I,oBAAA;MACA;IACA;IAEA,IAAAC,aAAA,QAAA7I,KAAA,CAAA4I,oBAAA;IACA,IAAAE,IAAA,GAAAD,aAAA,CAAAE,qBAAA;IACA,IAAAC,SAAA,GAAAxL,MAAA,CAAAyL,WAAA,IAAAC,QAAA,CAAAC,eAAA,CAAAH,SAAA;;IAEA;IACA,SAAAxO,yBAAA,gBAAAD,qBAAA;MACA,KAAAC,yBAAA,GAAAwO,SAAA,GAAAF,IAAA,CAAAM,GAAA;IACA;;IAEA;IACA,UAAA3O,mBAAA;MACA,KAAAA,mBAAA,GAAA+N,UAAA;QACA;QACA,IAAAM,IAAA,CAAAM,GAAA,UAAAT,OAAA,CAAApO,qBAAA;UACAoO,OAAA,CAAApO,qBAAA;QACA;QACA;QAAA,KACA,IAAAyO,SAAA,IAAAL,OAAA,CAAAnO,yBAAA,SAAAmO,OAAA,CAAApO,qBAAA;UACAoO,OAAA,CAAApO,qBAAA;QACA;QAEAoO,OAAA,CAAAlO,mBAAA;MACA;IACA;EACA,gCAEA4O,gBAAAzJ,GAAA;IAAA,IAAA0J,OAAA;IACA,KAAAzH,MAAA,CAAAM,OAAA,mBAAAnD,IAAA;MACA,WAAAuK,kBAAA,EAAA3J,GAAA,CAAAlD,KAAA;IACA,GAAAsC,IAAA;MACAsK,OAAA,CAAArM,OAAA;MACAqM,OAAA,CAAAzH,MAAA,CAAAC,UAAA;IACA,GAAAO,KAAA;EACA,+BAEAlF,eAAA;IAAA,IAAAqM,OAAA;IACA,IAAAC,qBAAA,IAAAzK,IAAA,WAAAC,QAAA;MACAuK,OAAA,CAAAE,MAAA,CAAAC,MAAA,0BAAA1K,QAAA,CAAAC,IAAA;IACA;EACA,oCAEA7B,oBAAA;IAAA,IAAAuM,OAAA;IACA;IACA,IAAAC,kBAAA;MAAAlP,OAAA;MAAAC,QAAA;IAAA,GAAAoE,IAAA,WAAAC,QAAA;MAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAC,IAAA,CAAA5C,MAAA;QACA;QACAsN,OAAA,CAAA3P,gBAAA,OAAA6P,mBAAA,CAAAhO,OAAA,MAAAiO,GAAA,CAAA9K,QAAA,CAAAC,IAAA,CAAA0B,GAAA,WAAApC,IAAA;UAAA,OAAAA,IAAA,CAAAwL,aAAA;QAAA,KACAzN,MAAA,WAAAiC,IAAA;UAAA,OAAAA,IAAA;QAAA,GACAwI,IAAA,WAAA5C,CAAA,EAAAH,CAAA;UAAA,OAAAA,CAAA,GAAAG,CAAA;QAAA;MAEA;QACAlD,OAAA,CAAAC,GAAA;QACAyI,OAAA,CAAAK,0BAAA;MACA;IACA,GAAA5H,KAAA,WAAAkE,KAAA;MACArF,OAAA,CAAAqF,KAAA,yBAAAA,KAAA;MACAqD,OAAA,CAAAK,0BAAA;IACA;EACA,2CAEAA,2BAAA;IAAA,IAAAC,OAAA;IACA;IACA,IAAAzK,oBAAA;MAAA9E,OAAA;MAAAC,QAAA;IAAA,GAAAoE,IAAA,WAAAC,QAAA;MACA,IAAAA,QAAA,IAAAA,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAC,IAAA,CAAA5C,MAAA;QACA;QACA4N,OAAA,CAAAjQ,gBAAA,OAAA6P,mBAAA,CAAAhO,OAAA,MAAAiO,GAAA,CAAA9K,QAAA,CAAAC,IAAA,CAAA0B,GAAA,WAAApC,IAAA;UAAA,OAAAA,IAAA,CAAAlD,YAAA;QAAA,KACAiB,MAAA,WAAAiC,IAAA;UAAA,OAAAA,IAAA;QAAA,GACAwI,IAAA,WAAA5C,CAAA,EAAAH,CAAA;UAAA,OAAAA,CAAA,GAAAG,CAAA;QAAA;QACAlD,OAAA,CAAAC,GAAA,mBAAA+I,OAAA,CAAAjQ,gBAAA;MACA;IACA,GAAAoI,KAAA,WAAAkE,KAAA;MACArF,OAAA,CAAAqF,KAAA,kBAAAA,KAAA;IACA;EACA,gCAEA4D,gBAAA;IAAA,IAAAC,OAAA;IACA,KAAA1Q,WAAA,SAAAA,WAAA;IACA,KAAA0E,SAAA;MACA,IAAAgM,OAAA,CAAA1Q,WAAA;QACA;QACA0Q,OAAA,CAAA3Q,WAAA,CAAAsG,OAAA,WAAAH,GAAA,EAAAnB,KAAA;UACA2L,OAAA,CAAApK,KAAA,CAAAC,YAAA,CAAAC,kBAAA,CAAAN,GAAA;QACA;MACA;QACA;QACAwK,OAAA,CAAA3Q,WAAA,CAAAsG,OAAA,WAAAH,GAAA,EAAAnB,KAAA;UACA2L,OAAA,CAAApK,KAAA,CAAAC,YAAA,CAAAC,kBAAA,CAAAN,GAAA;QACA;MACA;IACA;EACA,iCAEAO,iBAAA;IAAA,IAAAkK,OAAA;IACA,KAAA3Q,WAAA;IACA,KAAA0E,SAAA;MACA;MACAiM,OAAA,CAAA5Q,WAAA,CAAAsG,OAAA,WAAAH,GAAA;QACAyK,OAAA,CAAArK,KAAA,CAAAC,YAAA,CAAAC,kBAAA,CAAAN,GAAA;MACA;IACA;EACA,kCAEA0K,kBAAA;IAAA,IAAAC,OAAA;IACA;IACA,SAAArQ,iBAAA;MACA6D,YAAA,MAAA7D,iBAAA;IACA;IACA,KAAAA,iBAAA,GAAAsO,UAAA;MACA,IAAA+B,OAAA,CAAA7P,WAAA,CAAAW,MAAA,IAAAkP,OAAA,CAAA7P,WAAA,CAAAW,MAAA,CAAAmE,IAAA;QACA+K,OAAA,CAAA/J,WAAA;MACA;IACA;EACA,kCAEAgK,kBAAA;IACA,KAAA9P,WAAA,CAAAW,MAAA;IACA,KAAAmF,WAAA;EACA,6BAEAiK,aAAArG,CAAA,EAAAH,CAAA;IACA,IAAAyG,OAAA,GAAA7N,UAAA,CAAAuH,CAAA,CAAAlJ,SAAA;IACA,IAAAyP,OAAA,GAAA9N,UAAA,CAAAoH,CAAA,CAAA/I,SAAA;IACA,OAAAwP,OAAA,GAAAC,OAAA;EACA;AAEA", "ignoreList": []}]}