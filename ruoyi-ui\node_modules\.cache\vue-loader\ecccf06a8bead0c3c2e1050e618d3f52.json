{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\odds\\index.vue?vue&type=style&index=0&id=233d353a&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\odds\\index.vue", "mtime": 1758866059933}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750942927475}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750942929511}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5vZGRzLWNvbnRhaW5lciB7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOwogIG1pbi1oZWlnaHQ6IDEwMHZoOwp9CgovKiDpobXpnaLmoIfpopjmoLflvI8gKi8KLnBhZ2UtaGVhZGVyIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOwogIGNvbG9yOiB3aGl0ZTsKICBwYWRkaW5nOiAyNHB4IDMwcHg7CiAgYm9yZGVyLXJhZGl1czogMTJweDsKICBtYXJnaW4tYm90dG9tOiAyNHB4OwogIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpOwp9CgoucGFnZS10aXRsZSB7CiAgZm9udC1zaXplOiAyNHB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDhweDsKfQoKLnBhZ2UtdGl0bGUgaSB7CiAgZm9udC1zaXplOiAyOHB4OwogIG1hcmdpbi1yaWdodDogMTJweDsKfQoKLnBhZ2UtZGVzY3JpcHRpb24gewogIGZvbnQtc2l6ZTogMTRweDsKICBvcGFjaXR5OiAwLjk7Cn0KCi8qIOaQnOe0ouWMuuWfn+agt+W8jyAqLwouc2VhcmNoLWNvbnRhaW5lciB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgcGFkZGluZzogMjBweDsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKfQoKLnNlYXJjaC1mb3JtLXdyYXBwZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0OwogIGdhcDogMjBweDsKfQoKLnNlYXJjaC1mb3JtIHsKICBmbGV4OiAxOwogIG1hcmdpbi1ib3R0b206IDA7Cn0KCi5zZWFyY2gtdG9vbGJhciB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMTJweDsKICBmbGV4LXNocmluazogMDsKfQoKLyog5bel5YW35qCP5qC35byPICovCi50b29sYmFyLWNvbnRhaW5lciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBwYWRkaW5nOiAxNnB4IDIwcHg7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7Cn0KCi50b29sYmFyLWxlZnQgewogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiAxMnB4Owp9CgouYWN0aW9uLWJ0biB7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKfQoKLmFjdGlvbi1idG46aG92ZXIgewogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsKICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7Cn0KCi8qIOihqOagvOWuueWZqOagt+W8jyAqLwoudGFibGUtY29udGFpbmVyIHsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKfQoKLm9kZHMtdGFibGUgewogIGJvcmRlci1yYWRpdXM6IDhweDsKfQoKLyog6KGo5qC85YaF5a655qC35byPICovCi5tZXRob2QtbmFtZSB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwp9Cgoub2Rkcy1kaXNwbGF5IHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCi5vZGRzLXZhbHVlIHsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBjb2xvcjogI2U2YTIzYzsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoNDVkZWcsICNmMzljMTIsICNlNjdlMjIpOwogIC13ZWJraXQtYmFja2dyb3VuZC1jbGlwOiB0ZXh0OwogIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDsKICBiYWNrZ3JvdW5kLWNsaXA6IHRleHQ7CiAgcGFkZGluZzogNHB4IDEycHg7CiAgYm9yZGVyLXJhZGl1czogMjBweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmVmN2U2OwogIGJvcmRlcjogMXB4IHNvbGlkICNmNWRhYjE7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrOwp9CgouYWN0aW9uLWJ1dHRvbnMgewogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiA4cHg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7Cn0KCi5hY3Rpb24tYnV0dG9ucyAuZWwtYnV0dG9uIHsKICBtYXJnaW46IDA7Cn0KCi5hY3Rpb24tYnRuLW1pbmkgewogIGJvcmRlci1yYWRpdXM6IDE2cHg7CiAgcGFkZGluZzogNnB4IDEycHg7CiAgZm9udC1zaXplOiAxMnB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKICBib3JkZXI6IG5vbmU7CiAgbWluLXdpZHRoOiA1MHB4OwogIGhlaWdodDogMjhweDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7Cn0KCi5hY3Rpb24tYnRuLW1pbmk6aG92ZXIgewogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjE1KTsKfQoKLmVkaXQtYnRuIHsKICBiYWNrZ3JvdW5kOiAjNDA5RUZGOwogIGNvbG9yOiB3aGl0ZTsKfQoKLmVkaXQtYnRuOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiAjNjZiMWZmOwp9CgouZGVsZXRlLWJ0biB7CiAgYmFja2dyb3VuZDogI0Y1NkM2QzsKICBjb2xvcjogd2hpdGU7Cn0KCi5kZWxldGUtYnRuOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiAjZjc4OTg5Owp9CgovKiDnjrDku6PljJblvLnnqpfmoLflvI8gKi8KLm1vZGVybi1vZGRzLWRpYWxvZyB7CiAgLmVsLWRpYWxvZyB7CiAgICBib3JkZXItcmFkaXVzOiAxNnB4OwogICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIGJveC1zaGFkb3c6IDAgMjBweCA2MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgfQoKICAuZWwtZGlhbG9nX19oZWFkZXIgewogICAgcGFkZGluZzogMDsKICAgIGJvcmRlci1ib3R0b206IG5vbmU7CiAgfQoKICAuZWwtZGlhbG9nX19ib2R5IHsKICAgIHBhZGRpbmc6IDA7CiAgfQoKICAuZWwtZGlhbG9nX19mb290ZXIgewogICAgcGFkZGluZzogMDsKICAgIGJvcmRlci10b3A6IG5vbmU7CiAgfQp9CgovKiDoh6rlrprkuYnlpLTpg6ggKi8KLmRpYWxvZy1oZWFkZXIgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7CiAgY29sb3I6IHdoaXRlOwogIHBhZGRpbmc6IDI0cHg7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLmhlYWRlci1jb250ZW50IHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiAxNnB4Owp9CgouaGVhZGVyLWljb24gewogIHdpZHRoOiA0OHB4OwogIGhlaWdodDogNDhweDsKICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7CiAgYm9yZGVyLXJhZGl1czogMTJweDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgZm9udC1zaXplOiAyNHB4Owp9CgouaGVhZGVyLXRleHQgaDMgewogIG1hcmdpbjogMCAwIDRweCAwOwogIGZvbnQtc2l6ZTogMjBweDsKICBmb250LXdlaWdodDogNjAwOwp9CgouaGVhZGVyLXRleHQgcCB7CiAgbWFyZ2luOiAwOwogIGZvbnQtc2l6ZTogMTRweDsKICBvcGFjaXR5OiAwLjk7Cn0KCi5jbG9zZS1idG4gewogIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50OwogIGZvbnQtc2l6ZTogMjBweDsKICBwYWRkaW5nOiA4cHg7CgogICY6aG92ZXIgewogICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOwogICAgYm9yZGVyLXJhZGl1czogOHB4OwogIH0KfQoKLyog5YaF5a655Yy65Z+fICovCi5tb2Rlcm4tZGlhbG9nLWNvbnRlbnQgewogIHBhZGRpbmc6IDIwcHg7CiAgYmFja2dyb3VuZDogI2ZhZmJmYzsKfQoKLm1vZGVybi1vZGRzLWZvcm0gewogIC5lbC1mb3JtLWl0ZW0gewogICAgbWFyZ2luLWJvdHRvbTogMDsKICB9Cn0KCi8qIOihqOWNleWNoeeJhyAqLwouZm9ybS1jYXJkIHsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiAxMnB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA4KTsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwogIG92ZXJmbG93OiBoaWRkZW47CiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKCiAgJjpob3ZlciB7CiAgICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xMik7CiAgfQp9CgouY2FyZC1oZWFkZXIgewogIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgcGFkZGluZzogMTJweCAxNnB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDhweDsKICBmb250LXdlaWdodDogNjAwOwogIGNvbG9yOiAjNDk1MDU3OwogIGZvbnQtc2l6ZTogMTRweDsKCiAgaSB7CiAgICBjb2xvcjogIzY2N2VlYTsKICAgIGZvbnQtc2l6ZTogMTZweDsKICB9Cn0KCi5tZXRob2QtY291bnQgewogIG1hcmdpbi1sZWZ0OiBhdXRvOwogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogIzkwOTM5OTsKICBiYWNrZ3JvdW5kOiAjZTllY2VmOwogIHBhZGRpbmc6IDJweCA4cHg7CiAgYm9yZGVyLXJhZGl1czogMTJweDsKfQoKLmNhcmQtY29udGVudCB7CiAgcGFkZGluZzogMTZweDsKfQoKLyog546p5rOV6YCJ5oup5Yy65Z+f5qC35byPICovCi5tZXRob2Qtc2VsZWN0LXdyYXBwZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDEycHg7Cn0KCi5zZWxlY3QtbGFiZWwgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDZweDsKICBmb250LXNpemU6IDEzcHg7CiAgY29sb3I6ICM2MDYyNjY7CiAgZm9udC13ZWlnaHQ6IDUwMDsKCiAgaSB7CiAgICBjb2xvcjogIzY2N2VlYTsKICAgIGZvbnQtc2l6ZTogMTRweDsKICB9Cn0KCi5mdWxsLXdpZHRoIHsKICB3aWR0aDogMTAwJTsKfQoKLm1ldGhvZC10aXBzIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiA2cHg7CiAgbWFyZ2luLXRvcDogOHB4Owp9CgoudGlwLXJvdyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogNnB4OwogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogIzkwOTM5OTsKCiAgaSB7CiAgICBjb2xvcjogIzY2N2VlYTsKICAgIGZvbnQtc2l6ZTogMTJweDsKICB9Cn0KCi8qIOeOsOS7o+WMlumAieaLqeWZqCAqLwoubW9kZXJuLXNlbGVjdCB7CiAgLmVsLWlucHV0X19pbm5lciB7CiAgICBoZWlnaHQ6IDQ0cHg7CiAgICBib3JkZXI6IDJweCBzb2xpZCAjZTllY2VmOwogICAgYm9yZGVyLXJhZGl1czogOHB4OwogICAgZm9udC1zaXplOiAxNHB4OwogICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKICAgIHBhZGRpbmctbGVmdDogMTJweDsKICAgIHBhZGRpbmctcmlnaHQ6IDMwcHg7CgogICAgJjpmb2N1cyB7CiAgICAgIGJvcmRlci1jb2xvcjogIzY2N2VlYTsKICAgICAgYm94LXNoYWRvdzogMCAwIDAgM3B4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4xKTsKICAgIH0KCiAgICAmOjpwbGFjZWhvbGRlciB7CiAgICAgIGNvbG9yOiAjYzBjNGNjOwogICAgICBmb250LXNpemU6IDEzcHg7CiAgICB9CiAgfQoKICAuZWwtaW5wdXRfX3N1ZmZpeCB7CiAgICByaWdodDogOHB4OwogIH0KfQoKLm1ldGhvZC1vcHRpb24gewogIHBhZGRpbmc6IDEycHggMTZweDsKICBoZWlnaHQ6IGF1dG87CiAgbGluZS1oZWlnaHQ6IDEuNTsKfQoKLm9wdGlvbi1jb250ZW50IHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHdpZHRoOiAxMDAlOwogIG1pbi1oZWlnaHQ6IDI0cHg7Cn0KCi5vcHRpb24tbmFtZSB7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzJjM2U1MDsKICBmbGV4OiAxOwogIHdoaXRlLXNwYWNlOiBub3JtYWw7CiAgd29yZC13cmFwOiBicmVhay13b3JkOwogIG1hcmdpbi1yaWdodDogOHB4Owp9Cgoub3B0aW9uLWlkIHsKICBjb2xvcjogIzg0OTJhNjsKICBmb250LXNpemU6IDEycHg7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBwYWRkaW5nOiAycHggOHB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIGZsZXgtc2hyaW5rOiAwOwp9CgovKiDnjrDku6PljJbovpPlhaXmoYYgKi8KLm9kZHMtaW5wdXQtd3JhcHBlciB7CiAgbWFyZ2luLWJvdHRvbTogMTJweDsKfQoKLm1vZGVybi1pbnB1dCB7CiAgLmVsLWlucHV0X19pbm5lciB7CiAgICBoZWlnaHQ6IDQwcHg7CiAgICBib3JkZXI6IDJweCBzb2xpZCAjZTllY2VmOwogICAgYm9yZGVyLXJhZGl1czogOHB4OwogICAgZm9udC1zaXplOiAxNHB4OwogICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKCiAgICAmOmZvY3VzIHsKICAgICAgYm9yZGVyLWNvbG9yOiAjNjY3ZWVhOwogICAgICBib3gtc2hhZG93OiAwIDAgMCAzcHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjEpOwogICAgfQogIH0KCiAgLmVsLWlucHV0LWdyb3VwX19wcmVwZW5kIHsKICAgIGJhY2tncm91bmQ6ICM2NjdlZWE7CiAgICBjb2xvcjogd2hpdGU7CiAgICBib3JkZXI6IG5vbmU7CiAgICBib3JkZXItcmFkaXVzOiA4cHggMCAwIDhweDsKCiAgICBpIHsKICAgICAgbWFyZ2luLXJpZ2h0OiA0cHg7CiAgICB9CiAgfQoKICAuZWwtaW5wdXQtZ3JvdXBfX2FwcGVuZCB7CiAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogICAgY29sb3I6ICM0OTUwNTc7CiAgICBib3JkZXI6IDJweCBzb2xpZCAjZTllY2VmOwogICAgYm9yZGVyLWxlZnQ6IG5vbmU7CiAgICBib3JkZXItcmFkaXVzOiAwIDhweCA4cHggMDsKICB9Cn0KCi8qIOi+k+WFpeaPkOekuiAqLwouaW5wdXQtdGlwcyB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDE2cHg7CiAgZmxleC13cmFwOiB3cmFwOwp9CgoudGlwLWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDRweDsKICBmb250LXNpemU6IDEycHg7CiAgY29sb3I6ICM2Yzc1N2Q7CgogIGkgewogICAgY29sb3I6ICM2NjdlZWE7CiAgfQp9CgovKiDpooTop4jljaHniYcgKi8KLnByZXZpZXctY2FyZCB7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmZiAwJSwgI2U4ZjRmZCAxMDAlKTsKICBib3JkZXI6IDJweCBzb2xpZCAjNjY3ZWVhOwp9CgoucHJldmlldy1oZWFkZXIgewogIGJhY2tncm91bmQ6IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4xKTsKICBjb2xvcjogIzY2N2VlYTsKCiAgLmVsLXRhZyB7CiAgICBtYXJnaW4tbGVmdDogYXV0bzsKICB9Cn0KCi5wcmV2aWV3LWdyaWQgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDE2cHg7CiAgbWFyZ2luLWJvdHRvbTogMTJweDsKfQoKLnByZXZpZXctaXRlbSB7CiAgZmxleDogMTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiA4cHg7CiAgcGFkZGluZzogMTJweDsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wNik7Cn0KCi5wcmV2aWV3LWljb24gewogIHdpZHRoOiAzMnB4OwogIGhlaWdodDogMzJweDsKICBiYWNrZ3JvdW5kOiAjZTNmMmZkOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgY29sb3I6ICMxOTc2ZDI7CiAgZm9udC1zaXplOiAxNnB4Owp9CgoucHJvZml0LWljb24gewogIGJhY2tncm91bmQ6ICNlOGY1ZTg7CiAgY29sb3I6ICM0Y2FmNTA7Cn0KCi5wcmV2aWV3LWFycm93IHsKICBjb2xvcjogIzY2N2VlYTsKICBmb250LXNpemU6IDIwcHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5wcmV2aWV3LWluZm8gewogIGZsZXg6IDE7Cn0KCi5wcmV2aWV3LWxhYmVsIHsKICBmb250LXNpemU6IDEycHg7CiAgY29sb3I6ICM2Yzc1N2Q7CiAgbWFyZ2luLWJvdHRvbTogNHB4Owp9CgoucHJldmlldy12YWx1ZSB7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgY29sb3I6ICMyYzNlNTA7Cn0KCi5wcmV2aWV3LXZhbHVlLnByb2ZpdCB7CiAgY29sb3I6ICM0Y2FmNTA7CiAgZm9udC1zaXplOiAxOHB4Owp9CgoucHJvZml0LXJhdGUgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiAxMnB4OwogIGJhY2tncm91bmQ6IHdoaXRlOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBmb250LXNpemU6IDE0cHg7CiAgY29sb3I6ICM0OTUwNTc7Cn0KCi5yYXRlLXZhbHVlIHsKICBmb250LXdlaWdodDogNjAwOwogIGNvbG9yOiAjNGNhZjUwOwogIGZvbnQtc2l6ZTogMTZweDsKfQoKLyog5bqV6YOo5oyJ6ZKuICovCi5tb2Rlcm4tZGlhbG9nLWZvb3RlciB7CiAgcGFkZGluZzogMTZweCAyMHB4OwogIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOwogIGdhcDogMTJweDsKfQoKLmNhbmNlbC1idG4gewogIGhlaWdodDogMzZweDsKICBwYWRkaW5nOiAwIDIwcHg7CiAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwoKICAmOmhvdmVyIHsKICAgIGJvcmRlci1jb2xvcjogI2FkYjViZDsKICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgfQp9Cgouc3VibWl0LWJ0biB7CiAgaGVpZ2h0OiAzNnB4OwogIHBhZGRpbmc6IDAgMjRweDsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOwogIGJvcmRlcjogbm9uZTsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwoKICAmOmhvdmVyIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsKICAgIGJveC1zaGFkb3c6IDAgNHB4IDE1cHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpOwogIH0KfQoKLyog5ZON5bqU5byP6K6+6K6hICovCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgewogIC5vZGRzLWNvbnRhaW5lciB7CiAgICBwYWRkaW5nOiAxMnB4OwogIH0KCiAgLnRvb2xiYXItY29udGFpbmVyIHsKICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICBnYXA6IDE2cHg7CiAgfQoKICAudG9vbGJhci1sZWZ0IHsKICAgIGZsZXgtd3JhcDogd3JhcDsKICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIH0KCiAgLmFjdGlvbi1idXR0b25zIHsKICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICBnYXA6IDRweDsKICB9CgogIC5hY3Rpb24tYnV0dG9ucyAuZWwtYnV0dG9uIHsKICAgIG1hcmdpbjogMCA0cHggNHB4IDA7CiAgfQoKICAuYWN0aW9uLWJ1dHRvbnMgLmVsLWJ1dHRvbjpsYXN0LWNoaWxkIHsKICAgIG1hcmdpbi1yaWdodDogMDsKICB9CgogIC5tb2Rlcm4tZGlhbG9nLWNvbnRlbnQgewogICAgcGFkZGluZzogMjBweDsKICB9CgogIC5wcmV2aWV3LWdyaWQgewogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgIGdhcDogMTJweDsKICB9CgogIC5wcmV2aWV3LWFycm93IHsKICAgIHRyYW5zZm9ybTogcm90YXRlKDkwZGVnKTsKICB9CgogIC5tb2Rlcm4tZGlhbG9nLWZvb3RlciB7CiAgICBwYWRkaW5nOiAyMHB4OwogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICB9CgogIC5jYW5jZWwtYnRuLAogIC5zdWJtaXQtYnRuIHsKICAgIHdpZHRoOiAxMDAlOwogIH0KfQoKLyog546p5a626YCJ5oup5Yy65Z+f5qC35byPICovCi5wbGF5ZXItc2VsZWN0aW9uLWNvbnRhaW5lciB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnBsYXllci1zZWxlY3Rpb24tY2FyZCB7CiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYmFja2dyb3VuZDogI2ZhZmJmYzsKfQoKLnNlbGVjdGlvbi1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgY29sb3I6ICMzMDMxMzM7Cn0KCi5zZWxlY3Rpb24taGVhZGVyIGkgewogIG1hcmdpbi1yaWdodDogOHB4OwogIGNvbG9yOiAjNDA5ZWZmOwogIGZvbnQtc2l6ZTogMTZweDsKfQoKLnNlbGVjdGlvbi10aXRsZSB7CiAgZm9udC1zaXplOiAxNnB4Owp9CgoucGxheWVyLXJhZGlvLWdyb3VwIHsKICBtYXJnaW4tYm90dG9tOiAxNnB4Owp9CgoucGxheWVyLXJhZGlvLWdyb3VwIC5lbC1yYWRpby1ncm91cCB7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogIGZvbnQtc2l6ZTogMDsgLyog5raI6ZmkaW5saW5lLWJsb2Nr5YWD57Sg6Ze055qE56m655m9ICovCiAgbGluZS1oZWlnaHQ6IDA7Cn0KCi5wbGF5ZXItcmFkaW8tZ3JvdXAgLmVsLXJhZGlvLWdyb3VwID4gKiB7CiAgZm9udC1zaXplOiAxNHB4OyAvKiDmgaLlpI3lrZfkvZPlpKflsI8gKi8KICBsaW5lLWhlaWdodDogbm9ybWFsOwp9CgoucGxheWVyLXJhZGlvLWdyb3VwIC5lbC1yYWRpby1idXR0b24gewogIG1hcmdpbjogMCAhaW1wb3J0YW50Owp9CgovKiDnp7vpmaTkuoZtYXJnaW4tcmlnaHTorr7nva7vvIzorqnmjInpkq7lrozlhajotLTlkIggKi8KCi8qIOW8uuWItuenu+mZpEVsZW1lbnQgVUnpu5jorqTnmoTmjInpkq7pl7Tot50gKi8KLnBsYXllci1yYWRpby1ncm91cCAuZWwtcmFkaW8tYnV0dG9uIHsKICBtYXJnaW46IDAgIWltcG9ydGFudDsKICBkaXNwbGF5OiBpbmxpbmUtYmxvY2sgIWltcG9ydGFudDsKICB2ZXJ0aWNhbC1hbGlnbjogdG9wICFpbXBvcnRhbnQ7Cn0KCi5wbGF5ZXItcmFkaW8tZ3JvdXAgLmVsLXJhZGlvLWJ1dHRvbjpub3QoOmZpcnN0LWNoaWxkKSB7CiAgbWFyZ2luLWxlZnQ6IC0xcHggIWltcG9ydGFudDsKfQoKLnBsYXllci1yYWRpby1ncm91cCAuZWwtcmFkaW8tYnV0dG9uOm5vdCg6Zmlyc3QtY2hpbGQpIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsKICBib3JkZXItbGVmdDogMCAhaW1wb3J0YW50Owp9CgoucGxheWVyLXJhZGlvLWdyb3VwIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsKICBib3JkZXItcmFkaXVzOiAwICFpbXBvcnRhbnQ7CiAgbWFyZ2luOiAwICFpbXBvcnRhbnQ7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrICFpbXBvcnRhbnQ7Cn0KCi5wbGF5ZXItcmFkaW8tZ3JvdXAgLmVsLXJhZGlvLWJ1dHRvbjpmaXJzdC1jaGlsZCAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7CiAgYm9yZGVyLXJhZGl1czogNHB4IDAgMCA0cHggIWltcG9ydGFudDsKfQoKLnBsYXllci1yYWRpby1ncm91cCAuZWwtcmFkaW8tYnV0dG9uOmxhc3QtY2hpbGQgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgewogIGJvcmRlci1yYWRpdXM6IDAgNHB4IDRweCAwICFpbXBvcnRhbnQ7Cn0KCi5hbGwtdXNlcnMtYnRuIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOwogIGJvcmRlci1jb2xvcjogIzY2N2VlYTsKICBjb2xvcjogd2hpdGU7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBtYXJnaW46IDA7CiAgYm9yZGVyLXJhZGl1czogNHB4IDAgMCA0cHg7Cn0KCi5hbGwtdXNlcnMtYnRuIC5lbC1yYWRpby1idXR0b25fX2lubmVyIGkgewogIG1hcmdpbi1yaWdodDogNnB4Owp9CgouYWxsLXVzZXJzLWJ0bi5pcy1hY3RpdmUgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM1YTZmZDggMCUsICM2YTQxOTAgMTAwJSk7CiAgYm9yZGVyLWNvbG9yOiAjNWE2ZmQ4OwogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDkwLCAxMTEsIDIxNiwgMC4zKTsKfQoKLmN1c3RvbWVyLWJ0biAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBib3JkZXItY29sb3I6ICNlOWVjZWY7CiAgY29sb3I6ICM0OTUwNTc7CiAgbWFyZ2luOiAwOwogIGJvcmRlci1yYWRpdXM6IDA7Cn0KCi5jdXN0b21lci1idG46bGFzdC1jaGlsZCAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7CiAgYm9yZGVyLXJhZGl1czogMCA0cHggNHB4IDA7Cn0KCi5jdXN0b21lci1idG4gLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgaSB7CiAgbWFyZ2luLXJpZ2h0OiA2cHg7CiAgY29sb3I6ICM2N2MyM2E7Cn0KCi5jdXN0b21lci1idG4uaXMtYWN0aXZlIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsKICBiYWNrZ3JvdW5kOiAjNjdjMjNhOwogIGJvcmRlci1jb2xvcjogIzY3YzIzYTsKICBjb2xvcjogd2hpdGU7CiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMTAzLCAxOTQsIDU4LCAwLjMpOwp9Cgouc2VsZWN0aW9uLXN0YXR1cyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMTZweDsKfQoKLnNlbGVjdGlvbi1zdGF0dXMgLmVsLXRhZyB7CiAgZm9udC1zaXplOiAxMnB4Owp9Cgouc2VsZWN0aW9uLXN0YXR1cyAuZWwtdGFnIGkgewogIG1hcmdpbi1yaWdodDogNHB4Owp9Cgoubm8tb2Rkcy10aXAgewogIGNvbG9yOiAjZTZhMjNjOwogIGZvbnQtc2l6ZTogMTJweDsKfQoKLm5vLW9kZHMtdGlwIGkgewogIG1hcmdpbi1yaWdodDogNHB4Owp9CgovKiDotZTnjoflr7nmr5TmoLflvI8gKi8KLm9kZHMtdmFsdWUubm9ybWFsIHsKICBjb2xvcjogIzMwMzEzMzsKICBmb250LXdlaWdodDogNjAwOwp9Cgoub2Rkcy12YWx1ZS5jdXN0b20gewogIGNvbG9yOiAjNjdjMjNhOwogIGZvbnQtd2VpZ2h0OiA2MDA7Cn0KCi5vZGRzLXZhbHVlLmRlZmF1bHQgewogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtd2VpZ2h0OiA2MDA7Cn0KCi5kZWZhdWx0LW9kZHMgLm9kZHMtdmFsdWUgewogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0KCi5vZGRzLWRpZmYgewogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgZm9udC1zaXplOiAxMnB4Owp9Cgoub2Rkcy1kaWZmLnBvc2l0aXZlIHsKICBjb2xvcjogI2Y1NmM2YzsKfQoKLm9kZHMtZGlmZi5uZWdhdGl2ZSB7CiAgY29sb3I6ICM2N2MyM2E7Cn0KCi5vZGRzLWRpZmYubmV1dHJhbCB7CiAgY29sb3I6ICM5MDkzOTk7Cn0KCi8qIOeOqeWutuWQjeensOWIl+agt+W8jyAqLwouY3VzdG9tZXItbmFtZSB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwp9CgouY3VzdG9tZXItbmFtZS10ZXh0IHsKICBjb2xvcjogIzY3YzIzYTsKICBmb250LXdlaWdodDogNTAwOwp9CgouY3VzdG9tZXItbmFtZS10ZXh0IGkgewogIG1hcmdpbi1yaWdodDogNnB4OwogIGNvbG9yOiAjNjdjMjNhOwp9Cgouc3lzdGVtLWRlZmF1bHQgewogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0KCi5zeXN0ZW0tZGVmYXVsdCBpIHsKICBtYXJnaW4tcmlnaHQ6IDZweDsKICBjb2xvcjogIzQwOWVmZjsKfQoKLyog5YWo5bGA5LiL5ouJ5qGG5qC35byPICovCi5tZXRob2Qtc2VsZWN0LWRyb3Bkb3duIHsKICAuZWwtc2VsZWN0LWRyb3Bkb3duX19pdGVtIHsKICAgIGhlaWdodDogYXV0byAhaW1wb3J0YW50OwogICAgbGluZS1oZWlnaHQ6IDEuNSAhaW1wb3J0YW50OwogICAgcGFkZGluZzogMTJweCAxNnB4ICFpbXBvcnRhbnQ7CiAgICB3aGl0ZS1zcGFjZTogbm9ybWFsICFpbXBvcnRhbnQ7CiAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQgIWltcG9ydGFudDsKICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmxBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/game/odds", "sourcesContent": ["<template>\n  <div class=\"app-container odds-container\">\n   \n \n    <!-- 移动端搜索切换按钮 -->\n    <div v-if=\"isMobile\" class=\"mobile-search-toggle\">\n      <el-button\n        @click=\"toggleMobileSearch\"\n        class=\"mobile-search-btn\"\n        :type=\"showMobileSearch ? 'primary' : 'info'\"\n        icon=\"el-icon-search\"\n      >\n        {{ showMobileSearch ? '收起搜索' : '展开搜索' }}\n      </el-button>\n    </div>\n\n    <!-- 搜索区域 -->\n    <div class=\"search-container\" v-show=\"showSearch && (!isMobile || showMobileSearch)\">\n      <div class=\"search-form-wrapper\" :class=\"{ 'mobile-search-form': isMobile }\">\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"!isMobile\" label-width=\"80px\" class=\"search-form\">\n          <el-form-item label=\"玩法名称\" prop=\"methodName\">\n            <el-input\n              v-model=\"queryParams.methodName\"\n              placeholder=\"请输入玩法名称\"\n              clearable\n              prefix-icon=\"el-icon-search\"\n              @keyup.enter.native=\"handleQuery\"\n              :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\n            />\n          </el-form-item>\n          <el-form-item label=\"赔率范围\" prop=\"odds\">\n            <el-input\n              v-model=\"queryParams.odds\"\n              placeholder=\"请输入赔率\"\n              clearable\n              prefix-icon=\"el-icon-money\"\n              @keyup.enter.native=\"handleQuery\"\n              :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\n            />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n        <div class=\"search-toolbar\" :class=\"{ 'mobile-toolbar': isMobile }\">\n          <el-button\n            type=\"info\"\n            icon=\"el-icon-download\"\n            size=\"small\"\n            @click=\"handleExport\"\n            v-hasPermi=\"['game:odds:export']\"\n            class=\"action-btn\"\n          >导出数据</el-button>\n          <right-toolbar v-if=\"!isMobile\" :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n        </div>\n      </div>\n    </div>\n\n    <!-- 玩家选择区域 -->\n    <div class=\"player-selection-container\">\n      <el-card shadow=\"never\" class=\"player-selection-card\">\n        <div class=\"selection-header\">\n          <i class=\"el-icon-user\"></i>\n          <span class=\"selection-title\">选择玩家：</span>\n        </div>\n\n        <!-- PC端：单选按钮组 -->\n        <div v-if=\"!isMobile\" class=\"player-radio-group\">\n          <el-radio-group v-model=\"selectedCustomerId\" @change=\"handleCustomerChange\" size=\"small\">\n            <el-radio-button :label=\"null\" class=\"all-users-btn\">\n              <i class=\"el-icon-s-home\"></i>\n              全部玩家\n            </el-radio-button>\n            <el-radio-button\n              v-for=\"customer in customerList\"\n              :key=\"customer.userId\"\n              :label=\"customer.userId\"\n              class=\"customer-btn\"\n            >\n              <i class=\"el-icon-user-solid\"></i>\n              {{ customer.name }}\n            </el-radio-button>\n          </el-radio-group>\n        </div>\n\n        <!-- 移动端：下拉选择器 -->\n        <div v-else class=\"mobile-player-select\">\n          <el-select\n            v-model=\"selectedCustomerId\"\n            @change=\"handleCustomerChange\"\n            placeholder=\"请选择玩家\"\n            class=\"mobile-select\"\n            clearable\n          >\n            <el-option :value=\"null\" label=\"全部玩家\">\n              <i class=\"el-icon-s-home\"></i>\n              全部玩家\n            </el-option>\n            <el-option\n              v-for=\"customer in customerList\"\n              :key=\"customer.userId\"\n              :value=\"customer.userId\"\n              :label=\"customer.name\"\n            >\n              <i class=\"el-icon-user-solid\"></i>\n              {{ customer.name }}\n            </el-option>\n          </el-select>\n        </div>\n\n        <div class=\"selection-status\">\n          <el-tag v-if=\"!selectedCustomerId\" type=\"info\" effect=\"dark\" :style=\"isMobile ? 'font-size: 12px;' : 'font-size: 16px;'\" size=\"medium\">\n            <i class=\"el-icon-info\"></i>\n            当前显示：默认赔率模板{{ isMobile ? '' : '，如需修改或恢复默认，请选择具体玩家！' }}\n          </el-tag>\n          <el-tag v-else type=\"success\" size=\"medium\" effect=\"dark\" :style=\"isMobile ? 'font-size: 12px;' : 'font-size: 16px;'\">\n            <i class=\"el-icon-user-solid\"></i>\n            当前显示：{{ getCustomerName(selectedCustomerId) }} 的专属赔率\n          </el-tag>\n          <span v-if=\"selectedCustomerId && !hasCustomerOdds\" class=\"no-odds-tip\">\n            <i class=\"el-icon-warning\"></i>\n            该玩家暂无特殊赔率！\n          </span>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 数据表格 -->\n    <div class=\"table-container\">\n      <el-table\n        v-loading=\"loading\"\n        :data=\"oddsList\"\n        @selection-change=\"handleSelectionChange\"\n        class=\"odds-table\"\n        stripe\n        border\n        :header-cell-style=\"{ background: '#f8f9fa', color: '#606266', fontWeight: 'bold' }\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n\n        <el-table-column label=\"玩法名称\" align=\"center\" prop=\"methodName\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"method-name\">\n              <i class=\"el-icon-trophy\" style=\"color: #409EFF; margin-right: 8px;\"></i>\n              <span style=\"font-weight: 500;font-size: 16px;\">{{ scope.row.methodName }}</span>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"玩家名称\" align=\"center\" min-width=\"120\">\n          <template slot-scope=\"scope\">\n            <div class=\"customer-name\">\n              <span v-if=\"scope.row.customerName\" class=\"customer-name-text\">\n                <i class=\"el-icon-user-solid\"></i>\n                {{ scope.row.customerName }}\n              </span>\n              <span v-else class=\"system-default\">\n                <i class=\"el-icon-s-home\"></i>\n                系统默认\n              </span>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"默认赔率\" align=\"center\" min-width=\"120\" v-if=\"selectedCustomerId\">\n          <template slot-scope=\"scope\">\n            <div class=\"default-odds\">\n              <span class=\"odds-value default\">{{ formatOdds(scope.row.defaultOdds || scope.row.odds) }}</span>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column :label=\"selectedCustomerId ? '当前赔率' : '赔率'\" align=\"center\" prop=\"odds\" min-width=\"150\">\n          <template slot-scope=\"scope\">\n            <div class=\"odds-display\">\n              <span :class=\"['odds-value', getOddsClass(scope.row)]\">{{ formatOdds(scope.row.odds) }}</span>\n              <el-tag\n                v-if=\"selectedCustomerId && scope.row.isCustomerOdds\"\n                size=\"mini\"\n                type=\"success\"\n                style=\"margin-left: 8px;\"\n              >\n                专属\n              </el-tag>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"赔率差异\" align=\"center\" min-width=\"100\" v-if=\"selectedCustomerId\">\n          <template slot-scope=\"scope\">\n            <span :class=\"['odds-diff', getOddsDiffClass(scope.row)]\">\n              {{ getOddsDiff(scope.row) }}\n            </span>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"action-buttons\">\n              <el-button\n                v-if=\"!selectedCustomerId\"\n                size=\"mini\"\n                type=\"primary\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['game:odds:edit']\"\n                class=\"action-btn-mini edit-btn\"\n              >修改</el-button>\n              <el-button\n                v-if=\"selectedCustomerId\"\n                size=\"mini\"\n                type=\"primary\"\n                @click=\"handleUpdateCustomerOdds(scope.row)\"\n                v-hasPermi=\"['game:odds:edit']\"\n                class=\"action-btn-mini edit-btn\"\n              >修改</el-button>\n              <el-button\n                v-if=\"selectedCustomerId && scope.row.isCustomerOdds\"\n                size=\"mini\"\n                type=\"warning\"\n                @click=\"handleResetSingleOdds(scope.row)\"\n                v-hasPermi=\"['game:odds:edit']\"\n                class=\"action-btn-mini reset-btn\"\n              >重置</el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改赔率管理对话框 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"open\"\n      width=\"580px\"\n      append-to-body\n      class=\"modern-odds-dialog\"\n      :close-on-click-modal=\"false\"\n      :show-close=\"false\"\n      custom-class=\"beautiful-dialog\"\n    >\n      <!-- 自定义头部 -->\n      <div slot=\"title\" class=\"dialog-header\">\n        <div class=\"header-content\">\n          <div class=\"header-icon\">\n            <i class=\"el-icon-s-data\"></i>\n          </div>\n          <div class=\"header-text\">\n            <h3>{{ title }}</h3>\n            <p>设置玩法赔率，管理投注收益比例</p>\n          </div>\n        </div>\n        <el-button\n          type=\"text\"\n          @click=\"cancel\"\n          class=\"close-btn\"\n          icon=\"el-icon-close\"\n        ></el-button>\n      </div>\n\n      <div class=\"modern-dialog-content\">\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"0\" class=\"modern-odds-form\">\n          <!-- 玩法选择卡片 -->\n          <div class=\"form-card\">\n            <div class=\"card-header\">\n              <i class=\"el-icon-menu\"></i>\n              <span>选择玩法</span>\n              <span class=\"method-count\" v-if=\"methodList.length\">共 {{ methodList.length }} 种玩法</span>\n            </div>\n            <div class=\"card-content\">\n              <div class=\"method-select-wrapper\">\n                <div class=\"select-label\">\n                  <i class=\"el-icon-s-data\"></i>\n                  <span>玩法类型</span>\n                </div>\n                <el-form-item prop=\"methodId\">\n                  <el-select\n                    v-model=\"form.methodId\"\n                    placeholder=\"请选择要设置赔率的玩法\"\n                    @change=\"handleMethodChange\"\n                    class=\"modern-select full-width\"\n                    filterable\n                    popper-class=\"method-select-dropdown\"\n                  >\n                    <el-option\n                      v-for=\"method in methodList\"\n                      :key=\"method.methodId\"\n                      :label=\"method.methodName\"\n                      :value=\"method.methodId\"\n                      class=\"method-option\"\n                    >\n                      <div class=\"option-content\">\n                        <span class=\"option-name\">{{ method.methodName }}</span>\n                        <span class=\"option-id\">ID: {{ method.methodId }}</span>\n                      </div>\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n                <div class=\"method-tips\">\n                  <div class=\"tip-row\">\n                    <i class=\"el-icon-info\"></i>\n                    <span>支持输入关键词快速搜索玩法</span>\n                  </div>\n                  <div class=\"tip-row\">\n                    <i class=\"el-icon-warning\"></i>\n                    <span>每种玩法只能设置一个赔率</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 赔率设置卡片 -->\n          <div class=\"form-card\">\n            <div class=\"card-header\">\n              <i class=\"el-icon-money\"></i>\n              <span>设置赔率</span>\n            </div>\n            <div class=\"card-content\">\n              <el-form-item prop=\"odds\">\n                <div class=\"odds-input-wrapper\">\n                  <el-input\n                    v-model=\"form.odds\"\n                    placeholder=\"请输入赔率倍数\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0.01\"\n                    max=\"999.99\"\n                    class=\"modern-input\"\n                    size=\"large\"\n                    @input=\"handleOddsChange\"\n                  >\n                    <template slot=\"prepend\">\n                      <i class=\"el-icon-s-finance\"></i>\n                      倍率\n                    </template>\n                    <template slot=\"append\">倍</template>\n                  </el-input>\n                </div>\n                <div class=\"input-tips\">\n                  <div class=\"tip-item\">\n                    <i class=\"el-icon-info\"></i>\n                    <span>赔率范围：0.01 - 999.99</span>\n                  </div>\n                  <div class=\"tip-item\">\n                    <i class=\"el-icon-warning\"></i>\n                    <span>支持小数点后两位精度</span>\n                  </div>\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 预览区域卡片 -->\n          <div class=\"form-card preview-card\" v-if=\"form.odds && parseFloat(form.odds) > 0\">\n            <div class=\"card-header preview-header\">\n              <i class=\"el-icon-view\"></i>\n              <span>收益预览</span>\n              <el-tag type=\"success\" size=\"mini\">实时计算</el-tag>\n            </div>\n            <div class=\"card-content\">\n              <div class=\"preview-grid\">\n                <div class=\"preview-item\">\n                  <div class=\"preview-icon\">\n                    <i class=\"el-icon-wallet\"></i>\n                  </div>\n                  <div class=\"preview-info\">\n                    <div class=\"preview-label\">投注金额</div>\n                    <div class=\"preview-value\">￥100.00</div>\n                  </div>\n                </div>\n                <div class=\"preview-arrow\">\n                  <i class=\"el-icon-right\"></i>\n                </div>\n                <div class=\"preview-item\">\n                  <div class=\"preview-icon profit-icon\">\n                    <i class=\"el-icon-trophy\"></i>\n                  </div>\n                  <div class=\"preview-info\">\n                    <div class=\"preview-label\">预期收益</div>\n                    <div class=\"preview-value profit\">{{ formatProfit(form.odds, 100) }}</div>\n                  </div>\n                </div>\n              </div>\n              <div class=\"profit-rate\">\n                <span>收益率：</span>\n                <span class=\"rate-value\">{{ calculateProfitRate(form.odds) }}%</span>\n              </div>\n            </div>\n          </div>\n        </el-form>\n      </div>\n\n      <div slot=\"footer\" class=\"modern-dialog-footer\">\n        <el-button @click=\"cancel\" class=\"cancel-btn\" size=\"large\">\n          <i class=\"el-icon-close\"></i>\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"submitForm\" class=\"submit-btn\" size=\"large\">\n          <i class=\"el-icon-check\"></i>\n          {{ form.oddsId ? '更新赔率' : '创建赔率' }}\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listOdds, getOdds, delOdds, addOdds, updateOdds, getCustomerOdds, updateCustomerOdds, resetCustomerOdds } from \"@/api/game/odds\";\nimport { listMethod } from \"@/api/game/method\";\nimport { listCustomer } from \"@/api/game/customer\";\n\nexport default {\n  name: \"Odds\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 赔率管理表格数据\n      oddsList: [],\n      // 玩法列表\n      methodList: [],\n      // 玩家列表\n      customerList: [],\n      // 选中的玩家ID\n      selectedCustomerId: null,\n      // 是否有玩家专属赔率\n      hasCustomerOdds: false,\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 移动端相关\n      isMobile: false,\n      showMobileSearch: false,\n      resizeTimer: null,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 50,\n        methodName: null,\n        odds: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        methodId: [\n          { required: true, message: \"玩法不能为空\", trigger: \"change\" }\n        ],\n        odds: [\n          { required: true, message: \"赔率不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getMethodList();\n    this.getCustomerList();\n  },\n  mounted() {\n    // 初始化移动端检测\n    this.initMobileDetection();\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听\n    window.removeEventListener('resize', this.handleResize);\n    // 清理防抖定时器\n    if (this.resizeTimer) {\n      clearTimeout(this.resizeTimer);\n      this.resizeTimer = null;\n    }\n  },\n  methods: {\n    /** 初始化移动端检测 */\n    initMobileDetection() {\n      if (typeof window !== 'undefined') {\n        this.isMobile = window.innerWidth <= 768;\n      }\n    },\n    /** 处理窗口大小变化 */\n    handleResize() {\n      // 使用防抖避免频繁触发\n      if (this.resizeTimer) {\n        clearTimeout(this.resizeTimer);\n      }\n      this.resizeTimer = setTimeout(() => {\n        if (typeof window !== 'undefined') {\n          this.isMobile = window.innerWidth <= 768;\n        }\n      }, 100);\n    },\n    /** 切换移动端搜索显示 */\n    toggleMobileSearch() {\n      this.showMobileSearch = !this.showMobileSearch;\n    },\n    /** 切换移动端卡片展开状态 */\n    toggleMobileCard(item, index) {\n      this.$set(item, 'mobileExpanded', !item.mobileExpanded);\n    },\n    /** 查询赔率管理列表 */\n    getList() {\n      this.loading = true;\n      listOdds(this.queryParams).then(response => {\n        this.oddsList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 查询玩法列表 */\n    getMethodList() {\n      // 设置大的pageSize确保获取所有玩法\n      const params = {\n        pageNum: 1,\n        pageSize: 1000 // 设置足够大的数量\n      };\n      listMethod(params).then(response => {\n        this.methodList = response.rows;\n      });\n    },\n    /** 查询玩家列表 */\n    getCustomerList() {\n      const params = {\n        pageNum: 1,\n        pageSize: 1000\n      };\n      listCustomer(params).then(response => {\n        this.customerList = response.rows || [];\n      }).catch(error => {\n        console.error('获取玩家列表失败:', error);\n        this.customerList = [];\n      });\n    },\n    /** 玩家选择变化 */\n    handleCustomerChange(customerId) {\n      this.selectedCustomerId = customerId;\n      if (customerId) {\n        // 加载玩家专属赔率\n        this.loadCustomerOdds(customerId);\n      } else {\n        // 加载默认赔率\n        this.getList();\n      }\n    },\n    /** 加载玩家专属赔率 */\n    loadCustomerOdds(customerId) {\n      this.loading = true;\n      getCustomerOdds(customerId).then(response => {\n        if (response.code === 200 && response.data) {\n          this.oddsList = response.data;\n          this.hasCustomerOdds = response.data.some(item => item.isCustomerOdds);\n          this.total = response.data.length;\n        } else {\n          this.oddsList = [];\n          this.hasCustomerOdds = false;\n          this.total = 0;\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error('加载玩家赔率失败:', error);\n        this.$message.error('加载玩家赔率失败');\n        this.loading = false;\n      });\n    },\n    /** 获取玩家名称 */\n    getCustomerName(customerId) {\n      const customer = this.customerList.find(c => c.userId === customerId);\n      return customer ? customer.name : '未知玩家';\n    },\n    /** 获取赔率样式类 */\n    getOddsClass(row) {\n      if (!this.selectedCustomerId) return 'normal';\n      return row.isCustomerOdds ? 'custom' : 'default';\n    },\n    /** 计算赔率差异 */\n    getOddsDiff(row) {\n      if (!this.selectedCustomerId || !row.defaultOdds) return '-';\n      const diff = (parseFloat(row.odds) - parseFloat(row.defaultOdds)).toFixed(2);\n      return diff > 0 ? `+${diff}` : diff;\n    },\n    /** 获取赔率差异样式类 */\n    getOddsDiffClass(row) {\n      const diff = parseFloat(this.getOddsDiff(row));\n      if (diff > 0) return 'positive';\n      if (diff < 0) return 'negative';\n      return 'neutral';\n    },\n    /** 修改玩家赔率 */\n    handleUpdateCustomerOdds(row) {\n      // 打开编辑对话框，设置为玩家赔率编辑模式\n      this.reset();\n      this.form = { ...row };\n      this.form.isCustomerEdit = true;\n      this.form.customerId = this.selectedCustomerId;\n      this.title = `修改玩家赔率 - ${this.getCustomerName(this.selectedCustomerId)}`;\n      this.open = true;\n    },\n    /** 重置单个玩家赔率 */\n    handleResetSingleOdds(row) {\n      this.$confirm(`确认将玩家\"${this.getCustomerName(this.selectedCustomerId)}\"的\"${row.methodName}\"赔率重置为默认值吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const updateData = [{\n          oddsId: row.oddsId,\n          odds: row.defaultOdds,\n          customerId: this.selectedCustomerId,\n          methodId: row.methodId\n        }];\n\n        updateCustomerOdds(updateData).then(response => {\n          this.$message.success('重置成功');\n          this.loadCustomerOdds(this.selectedCustomerId);\n        }).catch(error => {\n          this.$message.error('重置失败');\n        });\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        oddsId: null,\n        methodId: null,\n        methodName: null,\n        odds: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.oddsId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加赔率管理\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const oddsId = row.oddsId || this.ids\n      getOdds(oddsId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改赔率管理\";\n      });\n    },\n    /** 玩法选择改变 */\n    handleMethodChange(methodId) {\n      const method = this.methodList.find(item => item.methodId === methodId);\n      if (method) {\n        this.form.methodName = method.methodName;\n      }\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 判断是否为玩家赔率编辑\n          if (this.form.isCustomerEdit && this.form.customerId) {\n            // 玩家赔率编辑\n            const updateData = [{\n              oddsId: this.form.oddsId || null, // 如果没有oddsId则为null，后端会创建新记录\n              odds: this.form.odds,\n              customerId: this.form.customerId,\n              methodId: this.form.methodId,\n              sysUserId: this.form.sysUserId,\n              methodName: this.form.methodName\n            }];\n\n            updateCustomerOdds(updateData).then(() => {\n              this.$modal.msgSuccess(\"修改玩家赔率成功\");\n              this.open = false;\n              this.loadCustomerOdds(this.form.customerId);\n            }).catch(error => {\n              console.error('修改玩家赔率失败:', error);\n              this.$modal.msgError(\"修改玩家赔率失败\");\n            });\n          } else {\n            // 默认赔率编辑\n            if (this.form.oddsId != null) {\n              updateOdds(this.form).then(() => {\n                this.$modal.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n              });\n            } else {\n              addOdds(this.form).then(() => {\n                this.$modal.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n              });\n            }\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const oddsIds = row.oddsId || this.ids;\n      this.$modal.confirm('是否确认删除赔率管理编号为\"' + oddsIds + '\"的数据项？').then(function() {\n        return delOdds(oddsIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('game/odds/export', {\n        ...this.queryParams\n      }, `odds_${new Date().getTime()}.xlsx`)\n    },\n    /** 格式化赔率显示 */\n    formatOdds(odds) {\n      if (!odds) return '0.00';\n      const num = parseFloat(odds);\n      return num.toFixed(2) + '倍';\n    },\n    /** 格式化收益显示 */\n    formatProfit(odds, amount = 1) {\n      if (!odds) return '￥0.00';\n      const num = parseFloat(odds);\n      const profit = (num * amount).toFixed(2);\n      return '￥' + profit;\n    },\n    // 计算收益率\n    calculateProfitRate(odds) {\n      if (!odds) return '0';\n      const rate = ((parseFloat(odds) - 1) * 100).toFixed(1);\n      return rate;\n    },\n    // 处理赔率输入变化\n    handleOddsChange(value) {\n      // 限制小数点后两位\n      if (value && value.toString().includes('.')) {\n        const parts = value.toString().split('.');\n        if (parts[1] && parts[1].length > 2) {\n          this.form.odds = parseFloat(value).toFixed(2);\n        }\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.odds-container {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 页面标题样式 */\n.page-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px 30px;\n  border-radius: 12px;\n  margin-bottom: 24px;\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.page-title i {\n  font-size: 28px;\n  margin-right: 12px;\n}\n\n.page-description {\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n/* 搜索区域样式 */\n.search-container {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.search-form-wrapper {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 20px;\n}\n\n.search-form {\n  flex: 1;\n  margin-bottom: 0;\n}\n\n.search-toolbar {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  flex-shrink: 0;\n}\n\n/* 工具栏样式 */\n.toolbar-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 16px 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 12px;\n}\n\n.action-btn {\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.action-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n/* 表格容器样式 */\n.table-container {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.odds-table {\n  border-radius: 8px;\n}\n\n/* 表格内容样式 */\n.method-name {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.odds-display {\n  text-align: center;\n}\n\n.odds-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #e6a23c;\n  background: linear-gradient(45deg, #f39c12, #e67e22);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  padding: 4px 12px;\n  border-radius: 20px;\n  background-color: #fef7e6;\n  border: 1px solid #f5dab1;\n  display: inline-block;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n  justify-content: center;\n}\n\n.action-buttons .el-button {\n  margin: 0;\n}\n\n.action-btn-mini {\n  border-radius: 16px;\n  padding: 6px 12px;\n  font-size: 12px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  border: none;\n  min-width: 50px;\n  height: 28px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-btn-mini:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n\n.edit-btn {\n  background: #409EFF;\n  color: white;\n}\n\n.edit-btn:hover {\n  background: #66b1ff;\n}\n\n.delete-btn {\n  background: #F56C6C;\n  color: white;\n}\n\n.delete-btn:hover {\n  background: #f78989;\n}\n\n/* 现代化弹窗样式 */\n.modern-odds-dialog {\n  .el-dialog {\n    border-radius: 16px;\n    overflow: hidden;\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\n  }\n\n  .el-dialog__header {\n    padding: 0;\n    border-bottom: none;\n  }\n\n  .el-dialog__body {\n    padding: 0;\n  }\n\n  .el-dialog__footer {\n    padding: 0;\n    border-top: none;\n  }\n}\n\n/* 自定义头部 */\n.dialog-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.header-icon {\n  width: 48px;\n  height: 48px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n}\n\n.header-text h3 {\n  margin: 0 0 4px 0;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.header-text p {\n  margin: 0;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.close-btn {\n  color: white !important;\n  font-size: 20px;\n  padding: 8px;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 8px;\n  }\n}\n\n/* 内容区域 */\n.modern-dialog-content {\n  padding: 20px;\n  background: #fafbfc;\n}\n\n.modern-odds-form {\n  .el-form-item {\n    margin-bottom: 0;\n  }\n}\n\n/* 表单卡片 */\n.form-card {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  margin-bottom: 16px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\n  }\n}\n\n.card-header {\n  background: #f8f9fa;\n  padding: 12px 16px;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: #495057;\n  font-size: 14px;\n\n  i {\n    color: #667eea;\n    font-size: 16px;\n  }\n}\n\n.method-count {\n  margin-left: auto;\n  font-size: 12px;\n  color: #909399;\n  background: #e9ecef;\n  padding: 2px 8px;\n  border-radius: 12px;\n}\n\n.card-content {\n  padding: 16px;\n}\n\n/* 玩法选择区域样式 */\n.method-select-wrapper {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.select-label {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 13px;\n  color: #606266;\n  font-weight: 500;\n\n  i {\n    color: #667eea;\n    font-size: 14px;\n  }\n}\n\n.full-width {\n  width: 100%;\n}\n\n.method-tips {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n  margin-top: 8px;\n}\n\n.tip-row {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 12px;\n  color: #909399;\n\n  i {\n    color: #667eea;\n    font-size: 12px;\n  }\n}\n\n/* 现代化选择器 */\n.modern-select {\n  .el-input__inner {\n    height: 44px;\n    border: 2px solid #e9ecef;\n    border-radius: 8px;\n    font-size: 14px;\n    transition: all 0.3s ease;\n    padding-left: 12px;\n    padding-right: 30px;\n\n    &:focus {\n      border-color: #667eea;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n    }\n\n    &::placeholder {\n      color: #c0c4cc;\n      font-size: 13px;\n    }\n  }\n\n  .el-input__suffix {\n    right: 8px;\n  }\n}\n\n.method-option {\n  padding: 12px 16px;\n  height: auto;\n  line-height: 1.5;\n}\n\n.option-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  min-height: 24px;\n}\n\n.option-name {\n  font-weight: 500;\n  color: #2c3e50;\n  flex: 1;\n  white-space: normal;\n  word-wrap: break-word;\n  margin-right: 8px;\n}\n\n.option-id {\n  color: #8492a6;\n  font-size: 12px;\n  background: #f8f9fa;\n  padding: 2px 8px;\n  border-radius: 4px;\n  white-space: nowrap;\n  flex-shrink: 0;\n}\n\n/* 现代化输入框 */\n.odds-input-wrapper {\n  margin-bottom: 12px;\n}\n\n.modern-input {\n  .el-input__inner {\n    height: 40px;\n    border: 2px solid #e9ecef;\n    border-radius: 8px;\n    font-size: 14px;\n    transition: all 0.3s ease;\n\n    &:focus {\n      border-color: #667eea;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n    }\n  }\n\n  .el-input-group__prepend {\n    background: #667eea;\n    color: white;\n    border: none;\n    border-radius: 8px 0 0 8px;\n\n    i {\n      margin-right: 4px;\n    }\n  }\n\n  .el-input-group__append {\n    background: #f8f9fa;\n    color: #495057;\n    border: 2px solid #e9ecef;\n    border-left: none;\n    border-radius: 0 8px 8px 0;\n  }\n}\n\n/* 输入提示 */\n.input-tips {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.tip-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: #6c757d;\n\n  i {\n    color: #667eea;\n  }\n}\n\n/* 预览卡片 */\n.preview-card {\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);\n  border: 2px solid #667eea;\n}\n\n.preview-header {\n  background: rgba(102, 126, 234, 0.1);\n  color: #667eea;\n\n  .el-tag {\n    margin-left: auto;\n  }\n}\n\n.preview-grid {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  margin-bottom: 12px;\n}\n\n.preview-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.preview-icon {\n  width: 32px;\n  height: 32px;\n  background: #e3f2fd;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #1976d2;\n  font-size: 16px;\n}\n\n.profit-icon {\n  background: #e8f5e8;\n  color: #4caf50;\n}\n\n.preview-arrow {\n  color: #667eea;\n  font-size: 20px;\n  font-weight: bold;\n}\n\n.preview-info {\n  flex: 1;\n}\n\n.preview-label {\n  font-size: 12px;\n  color: #6c757d;\n  margin-bottom: 4px;\n}\n\n.preview-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.preview-value.profit {\n  color: #4caf50;\n  font-size: 18px;\n}\n\n.profit-rate {\n  text-align: center;\n  padding: 12px;\n  background: white;\n  border-radius: 8px;\n  font-size: 14px;\n  color: #495057;\n}\n\n.rate-value {\n  font-weight: 600;\n  color: #4caf50;\n  font-size: 16px;\n}\n\n/* 底部按钮 */\n.modern-dialog-footer {\n  padding: 16px 20px;\n  background: #f8f9fa;\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n\n.cancel-btn {\n  height: 36px;\n  padding: 0 20px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: #adb5bd;\n    background: #f8f9fa;\n  }\n}\n\n.submit-btn {\n  height: 36px;\n  padding: 0 24px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .odds-container {\n    padding: 12px;\n  }\n\n  .toolbar-container {\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .toolbar-left {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n    gap: 4px;\n  }\n\n  .action-buttons .el-button {\n    margin: 0 4px 4px 0;\n  }\n\n  .action-buttons .el-button:last-child {\n    margin-right: 0;\n  }\n\n  .modern-dialog-content {\n    padding: 20px;\n  }\n\n  .preview-grid {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .preview-arrow {\n    transform: rotate(90deg);\n  }\n\n  .modern-dialog-footer {\n    padding: 20px;\n    flex-direction: column;\n  }\n\n  .cancel-btn,\n  .submit-btn {\n    width: 100%;\n  }\n}\n\n/* 玩家选择区域样式 */\n.player-selection-container {\n  margin-bottom: 20px;\n}\n\n.player-selection-card {\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  background: #fafbfc;\n}\n\n.selection-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.selection-header i {\n  margin-right: 8px;\n  color: #409eff;\n  font-size: 16px;\n}\n\n.selection-title {\n  font-size: 16px;\n}\n\n.player-radio-group {\n  margin-bottom: 16px;\n}\n\n.player-radio-group .el-radio-group {\n  display: inline-block;\n  font-size: 0; /* 消除inline-block元素间的空白 */\n  line-height: 0;\n}\n\n.player-radio-group .el-radio-group > * {\n  font-size: 14px; /* 恢复字体大小 */\n  line-height: normal;\n}\n\n.player-radio-group .el-radio-button {\n  margin: 0 !important;\n}\n\n/* 移除了margin-right设置，让按钮完全贴合 */\n\n/* 强制移除Element UI默认的按钮间距 */\n.player-radio-group .el-radio-button {\n  margin: 0 !important;\n  display: inline-block !important;\n  vertical-align: top !important;\n}\n\n.player-radio-group .el-radio-button:not(:first-child) {\n  margin-left: -1px !important;\n}\n\n.player-radio-group .el-radio-button:not(:first-child) .el-radio-button__inner {\n  border-left: 0 !important;\n}\n\n.player-radio-group .el-radio-button__inner {\n  border-radius: 0 !important;\n  margin: 0 !important;\n  display: inline-block !important;\n}\n\n.player-radio-group .el-radio-button:first-child .el-radio-button__inner {\n  border-radius: 4px 0 0 4px !important;\n}\n\n.player-radio-group .el-radio-button:last-child .el-radio-button__inner {\n  border-radius: 0 4px 4px 0 !important;\n}\n\n.all-users-btn .el-radio-button__inner {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-color: #667eea;\n  color: white;\n  font-weight: 500;\n  margin: 0;\n  border-radius: 4px 0 0 4px;\n}\n\n.all-users-btn .el-radio-button__inner i {\n  margin-right: 6px;\n}\n\n.all-users-btn.is-active .el-radio-button__inner {\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n  border-color: #5a6fd8;\n  box-shadow: 0 2px 8px rgba(90, 111, 216, 0.3);\n}\n\n.customer-btn .el-radio-button__inner {\n  background: #f8f9fa;\n  border-color: #e9ecef;\n  color: #495057;\n  margin: 0;\n  border-radius: 0;\n}\n\n.customer-btn:last-child .el-radio-button__inner {\n  border-radius: 0 4px 4px 0;\n}\n\n.customer-btn .el-radio-button__inner i {\n  margin-right: 6px;\n  color: #67c23a;\n}\n\n.customer-btn.is-active .el-radio-button__inner {\n  background: #67c23a;\n  border-color: #67c23a;\n  color: white;\n  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);\n}\n\n.selection-status {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.selection-status .el-tag {\n  font-size: 12px;\n}\n\n.selection-status .el-tag i {\n  margin-right: 4px;\n}\n\n.no-odds-tip {\n  color: #e6a23c;\n  font-size: 12px;\n}\n\n.no-odds-tip i {\n  margin-right: 4px;\n}\n\n/* 赔率对比样式 */\n.odds-value.normal {\n  color: #303133;\n  font-weight: 600;\n}\n\n.odds-value.custom {\n  color: #67c23a;\n  font-weight: 600;\n}\n\n.odds-value.default {\n  color: #909399;\n  font-weight: 600;\n}\n\n.default-odds .odds-value {\n  color: #909399;\n  font-weight: 500;\n}\n\n.odds-diff {\n  font-weight: 600;\n  font-size: 12px;\n}\n\n.odds-diff.positive {\n  color: #f56c6c;\n}\n\n.odds-diff.negative {\n  color: #67c23a;\n}\n\n.odds-diff.neutral {\n  color: #909399;\n}\n\n/* 玩家名称列样式 */\n.customer-name {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.customer-name-text {\n  color: #67c23a;\n  font-weight: 500;\n}\n\n.customer-name-text i {\n  margin-right: 6px;\n  color: #67c23a;\n}\n\n.system-default {\n  color: #909399;\n  font-weight: 500;\n}\n\n.system-default i {\n  margin-right: 6px;\n  color: #409eff;\n}\n\n/* 全局下拉框样式 */\n.method-select-dropdown {\n  .el-select-dropdown__item {\n    height: auto !important;\n    line-height: 1.5 !important;\n    padding: 12px 16px !important;\n    white-space: normal !important;\n    word-wrap: break-word !important;\n  }\n}\n</style>\n"]}]}