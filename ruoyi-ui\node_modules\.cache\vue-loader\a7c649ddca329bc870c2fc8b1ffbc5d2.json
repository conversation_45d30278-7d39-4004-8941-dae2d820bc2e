{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\odds\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\odds\\index.vue", "mtime": 1758866059933}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RPZGRzLCBnZXRPZGRzLCBkZWxPZGRzLCBhZGRPZGRzLCB1cGRhdGVPZGRzLCBnZXRDdXN0b21lck9kZHMsIHVwZGF0ZUN1c3RvbWVyT2RkcywgcmVzZXRDdXN0b21lck9kZHMgfSBmcm9tICJAL2FwaS9nYW1lL29kZHMiOwppbXBvcnQgeyBsaXN0TWV0aG9kIH0gZnJvbSAiQC9hcGkvZ2FtZS9tZXRob2QiOwppbXBvcnQgeyBsaXN0Q3VzdG9tZXIgfSBmcm9tICJAL2FwaS9nYW1lL2N1c3RvbWVyIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiT2RkcyIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDotZTnjofnrqHnkIbooajmoLzmlbDmja4KICAgICAgb2Rkc0xpc3Q6IFtdLAogICAgICAvLyDnjqnms5XliJfooagKICAgICAgbWV0aG9kTGlzdDogW10sCiAgICAgIC8vIOeOqeWutuWIl+ihqAogICAgICBjdXN0b21lckxpc3Q6IFtdLAogICAgICAvLyDpgInkuK3nmoTnjqnlrrZJRAogICAgICBzZWxlY3RlZEN1c3RvbWVySWQ6IG51bGwsCiAgICAgIC8vIOaYr+WQpuacieeOqeWutuS4k+Wxnui1lOeOhwogICAgICBoYXNDdXN0b21lck9kZHM6IGZhbHNlLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOenu+WKqOerr+ebuOWFswogICAgICBpc01vYmlsZTogZmFsc2UsCiAgICAgIHNob3dNb2JpbGVTZWFyY2g6IGZhbHNlLAogICAgICByZXNpemVUaW1lcjogbnVsbCwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogNTAsCiAgICAgICAgbWV0aG9kTmFtZTogbnVsbCwKICAgICAgICBvZGRzOiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgbWV0aG9kSWQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnjqnms5XkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICBvZGRzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6LWU546H5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldE1ldGhvZExpc3QoKTsKICAgIHRoaXMuZ2V0Q3VzdG9tZXJMaXN0KCk7CiAgfSwKICBtb3VudGVkKCkgewogICAgLy8g5Yid5aeL5YyW56e75Yqo56uv5qOA5rWLCiAgICB0aGlzLmluaXRNb2JpbGVEZXRlY3Rpb24oKTsKICAgIC8vIOebkeWQrOeql+WPo+Wkp+Wwj+WPmOWMlgogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKTsKICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICAvLyDnp7vpmaTnqpflj6PlpKflsI/lj5jljJbnm5HlkKwKICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLmhhbmRsZVJlc2l6ZSk7CiAgICAvLyDmuIXnkIbpmLLmipblrprml7blmagKICAgIGlmICh0aGlzLnJlc2l6ZVRpbWVyKSB7CiAgICAgIGNsZWFyVGltZW91dCh0aGlzLnJlc2l6ZVRpbWVyKTsKICAgICAgdGhpcy5yZXNpemVUaW1lciA9IG51bGw7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5Yid5aeL5YyW56e75Yqo56uv5qOA5rWLICovCiAgICBpbml0TW9iaWxlRGV0ZWN0aW9uKCkgewogICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHsKICAgICAgICB0aGlzLmlzTW9iaWxlID0gd2luZG93LmlubmVyV2lkdGggPD0gNzY4OwogICAgICB9CiAgICB9LAogICAgLyoqIOWkhOeQhueql+WPo+Wkp+Wwj+WPmOWMliAqLwogICAgaGFuZGxlUmVzaXplKCkgewogICAgICAvLyDkvb/nlKjpmLLmipbpgb/lhY3popHnuYHop6blj5EKICAgICAgaWYgKHRoaXMucmVzaXplVGltZXIpIHsKICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5yZXNpemVUaW1lcik7CiAgICAgIH0KICAgICAgdGhpcy5yZXNpemVUaW1lciA9IHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykgewogICAgICAgICAgdGhpcy5pc01vYmlsZSA9IHdpbmRvdy5pbm5lcldpZHRoIDw9IDc2ODsKICAgICAgICB9CiAgICAgIH0sIDEwMCk7CiAgICB9LAogICAgLyoqIOWIh+aNouenu+WKqOerr+aQnOe0ouaYvuekuiAqLwogICAgdG9nZ2xlTW9iaWxlU2VhcmNoKCkgewogICAgICB0aGlzLnNob3dNb2JpbGVTZWFyY2ggPSAhdGhpcy5zaG93TW9iaWxlU2VhcmNoOwogICAgfSwKICAgIC8qKiDliIfmjaLnp7vliqjnq6/ljaHniYflsZXlvIDnirbmgIEgKi8KICAgIHRvZ2dsZU1vYmlsZUNhcmQoaXRlbSwgaW5kZXgpIHsKICAgICAgdGhpcy4kc2V0KGl0ZW0sICdtb2JpbGVFeHBhbmRlZCcsICFpdGVtLm1vYmlsZUV4cGFuZGVkKTsKICAgIH0sCiAgICAvKiog5p+l6K+i6LWU546H566h55CG5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0T2Rkcyh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLm9kZHNMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmn6Xor6Lnjqnms5XliJfooaggKi8KICAgIGdldE1ldGhvZExpc3QoKSB7CiAgICAgIC8vIOiuvue9ruWkp+eahHBhZ2VTaXpl56Gu5L+d6I635Y+W5omA5pyJ546p5rOVCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMDAwIC8vIOiuvue9rui2s+Wkn+Wkp+eahOaVsOmHjwogICAgICB9OwogICAgICBsaXN0TWV0aG9kKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5tZXRob2RMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivoueOqeWutuWIl+ihqCAqLwogICAgZ2V0Q3VzdG9tZXJMaXN0KCkgewogICAgICBjb25zdCBwYXJhbXMgPSB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAwMAogICAgICB9OwogICAgICBsaXN0Q3VzdG9tZXIocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmN1c3RvbWVyTGlzdCA9IHJlc3BvbnNlLnJvd3MgfHwgW107CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnjqnlrrbliJfooajlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuY3VzdG9tZXJMaXN0ID0gW107CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDnjqnlrrbpgInmi6nlj5jljJYgKi8KICAgIGhhbmRsZUN1c3RvbWVyQ2hhbmdlKGN1c3RvbWVySWQpIHsKICAgICAgdGhpcy5zZWxlY3RlZEN1c3RvbWVySWQgPSBjdXN0b21lcklkOwogICAgICBpZiAoY3VzdG9tZXJJZCkgewogICAgICAgIC8vIOWKoOi9veeOqeWutuS4k+Wxnui1lOeOhwogICAgICAgIHRoaXMubG9hZEN1c3RvbWVyT2RkcyhjdXN0b21lcklkKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDliqDovb3pu5jorqTotZTnjocKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDliqDovb3njqnlrrbkuJPlsZ7otZTnjocgKi8KICAgIGxvYWRDdXN0b21lck9kZHMoY3VzdG9tZXJJZCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBnZXRDdXN0b21lck9kZHMoY3VzdG9tZXJJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhKSB7CiAgICAgICAgICB0aGlzLm9kZHNMaXN0ID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgIHRoaXMuaGFzQ3VzdG9tZXJPZGRzID0gcmVzcG9uc2UuZGF0YS5zb21lKGl0ZW0gPT4gaXRlbS5pc0N1c3RvbWVyT2Rkcyk7CiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS5sZW5ndGg7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMub2Rkc0xpc3QgPSBbXTsKICAgICAgICAgIHRoaXMuaGFzQ3VzdG9tZXJPZGRzID0gZmFsc2U7CiAgICAgICAgICB0aGlzLnRvdGFsID0gMDsKICAgICAgICB9CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3njqnlrrbotZTnjoflpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veeOqeWutui1lOeOh+Wksei0pScpOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog6I635Y+W546p5a625ZCN56ewICovCiAgICBnZXRDdXN0b21lck5hbWUoY3VzdG9tZXJJZCkgewogICAgICBjb25zdCBjdXN0b21lciA9IHRoaXMuY3VzdG9tZXJMaXN0LmZpbmQoYyA9PiBjLnVzZXJJZCA9PT0gY3VzdG9tZXJJZCk7CiAgICAgIHJldHVybiBjdXN0b21lciA/IGN1c3RvbWVyLm5hbWUgOiAn5pyq55+l546p5a62JzsKICAgIH0sCiAgICAvKiog6I635Y+W6LWU546H5qC35byP57G7ICovCiAgICBnZXRPZGRzQ2xhc3Mocm93KSB7CiAgICAgIGlmICghdGhpcy5zZWxlY3RlZEN1c3RvbWVySWQpIHJldHVybiAnbm9ybWFsJzsKICAgICAgcmV0dXJuIHJvdy5pc0N1c3RvbWVyT2RkcyA/ICdjdXN0b20nIDogJ2RlZmF1bHQnOwogICAgfSwKICAgIC8qKiDorqHnrpfotZTnjoflt67lvIIgKi8KICAgIGdldE9kZHNEaWZmKHJvdykgewogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRDdXN0b21lcklkIHx8ICFyb3cuZGVmYXVsdE9kZHMpIHJldHVybiAnLSc7CiAgICAgIGNvbnN0IGRpZmYgPSAocGFyc2VGbG9hdChyb3cub2RkcykgLSBwYXJzZUZsb2F0KHJvdy5kZWZhdWx0T2RkcykpLnRvRml4ZWQoMik7CiAgICAgIHJldHVybiBkaWZmID4gMCA/IGArJHtkaWZmfWAgOiBkaWZmOwogICAgfSwKICAgIC8qKiDojrflj5botZTnjoflt67lvILmoLflvI/nsbsgKi8KICAgIGdldE9kZHNEaWZmQ2xhc3Mocm93KSB7CiAgICAgIGNvbnN0IGRpZmYgPSBwYXJzZUZsb2F0KHRoaXMuZ2V0T2Rkc0RpZmYocm93KSk7CiAgICAgIGlmIChkaWZmID4gMCkgcmV0dXJuICdwb3NpdGl2ZSc7CiAgICAgIGlmIChkaWZmIDwgMCkgcmV0dXJuICduZWdhdGl2ZSc7CiAgICAgIHJldHVybiAnbmV1dHJhbCc7CiAgICB9LAogICAgLyoqIOS/ruaUueeOqeWutui1lOeOhyAqLwogICAgaGFuZGxlVXBkYXRlQ3VzdG9tZXJPZGRzKHJvdykgewogICAgICAvLyDmiZPlvIDnvJbovpHlr7nor53moYbvvIzorr7nva7kuLrnjqnlrrbotZTnjofnvJbovpHmqKHlvI8KICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLmZvcm0uaXNDdXN0b21lckVkaXQgPSB0cnVlOwogICAgICB0aGlzLmZvcm0uY3VzdG9tZXJJZCA9IHRoaXMuc2VsZWN0ZWRDdXN0b21lcklkOwogICAgICB0aGlzLnRpdGxlID0gYOS/ruaUueeOqeWutui1lOeOhyAtICR7dGhpcy5nZXRDdXN0b21lck5hbWUodGhpcy5zZWxlY3RlZEN1c3RvbWVySWQpfWA7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOmHjee9ruWNleS4queOqeWutui1lOeOhyAqLwogICAgaGFuZGxlUmVzZXRTaW5nbGVPZGRzKHJvdykgewogICAgICB0aGlzLiRjb25maXJtKGDnoa7orqTlsIbnjqnlrrYiJHt0aGlzLmdldEN1c3RvbWVyTmFtZSh0aGlzLnNlbGVjdGVkQ3VzdG9tZXJJZCl9IueahCIke3Jvdy5tZXRob2ROYW1lfSLotZTnjofph43nva7kuLrpu5jorqTlgLzlkJfvvJ9gLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IFt7CiAgICAgICAgICBvZGRzSWQ6IHJvdy5vZGRzSWQsCiAgICAgICAgICBvZGRzOiByb3cuZGVmYXVsdE9kZHMsCiAgICAgICAgICBjdXN0b21lcklkOiB0aGlzLnNlbGVjdGVkQ3VzdG9tZXJJZCwKICAgICAgICAgIG1ldGhvZElkOiByb3cubWV0aG9kSWQKICAgICAgICB9XTsKCiAgICAgICAgdXBkYXRlQ3VzdG9tZXJPZGRzKHVwZGF0ZURhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfph43nva7miJDlip8nKTsKICAgICAgICAgIHRoaXMubG9hZEN1c3RvbWVyT2Rkcyh0aGlzLnNlbGVjdGVkQ3VzdG9tZXJJZCk7CiAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6YeN572u5aSx6LSlJyk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBvZGRzSWQ6IG51bGwsCiAgICAgICAgbWV0aG9kSWQ6IG51bGwsCiAgICAgICAgbWV0aG9kTmFtZTogbnVsbCwKICAgICAgICBvZGRzOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLm9kZHNJZCkKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDotZTnjofnrqHnkIYiOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBvZGRzSWQgPSByb3cub2Rkc0lkIHx8IHRoaXMuaWRzCiAgICAgIGdldE9kZHMob2Rkc0lkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnotZTnjofnrqHnkIYiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog546p5rOV6YCJ5oup5pS55Y+YICovCiAgICBoYW5kbGVNZXRob2RDaGFuZ2UobWV0aG9kSWQpIHsKICAgICAgY29uc3QgbWV0aG9kID0gdGhpcy5tZXRob2RMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLm1ldGhvZElkID09PSBtZXRob2RJZCk7CiAgICAgIGlmIChtZXRob2QpIHsKICAgICAgICB0aGlzLmZvcm0ubWV0aG9kTmFtZSA9IG1ldGhvZC5tZXRob2ROYW1lOwogICAgICB9CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIC8vIOWIpOaWreaYr+WQpuS4uueOqeWutui1lOeOh+e8lui+kQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pc0N1c3RvbWVyRWRpdCAmJiB0aGlzLmZvcm0uY3VzdG9tZXJJZCkgewogICAgICAgICAgICAvLyDnjqnlrrbotZTnjofnvJbovpEKICAgICAgICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IFt7CiAgICAgICAgICAgICAgb2Rkc0lkOiB0aGlzLmZvcm0ub2Rkc0lkIHx8IG51bGwsIC8vIOWmguaenOayoeaciW9kZHNJZOWImeS4um51bGzvvIzlkI7nq6/kvJrliJvlu7rmlrDorrDlvZUKICAgICAgICAgICAgICBvZGRzOiB0aGlzLmZvcm0ub2RkcywKICAgICAgICAgICAgICBjdXN0b21lcklkOiB0aGlzLmZvcm0uY3VzdG9tZXJJZCwKICAgICAgICAgICAgICBtZXRob2RJZDogdGhpcy5mb3JtLm1ldGhvZElkLAogICAgICAgICAgICAgIHN5c1VzZXJJZDogdGhpcy5mb3JtLnN5c1VzZXJJZCwKICAgICAgICAgICAgICBtZXRob2ROYW1lOiB0aGlzLmZvcm0ubWV0aG9kTmFtZQogICAgICAgICAgICB9XTsKCiAgICAgICAgICAgIHVwZGF0ZUN1c3RvbWVyT2Rkcyh1cGRhdGVEYXRhKS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnnjqnlrrbotZTnjofmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmxvYWRDdXN0b21lck9kZHModGhpcy5mb3JtLmN1c3RvbWVySWQpOwogICAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5L+u5pS5546p5a626LWU546H5aSx6LSlOicsIGVycm9yKTsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5L+u5pS5546p5a626LWU546H5aSx6LSlIik7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgLy8g6buY6K6k6LWU546H57yW6L6RCiAgICAgICAgICAgIGlmICh0aGlzLmZvcm0ub2Rkc0lkICE9IG51bGwpIHsKICAgICAgICAgICAgICB1cGRhdGVPZGRzKHRoaXMuZm9ybSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgYWRkT2Rkcyh0aGlzLmZvcm0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IG9kZHNJZHMgPSByb3cub2Rkc0lkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTotZTnjofnrqHnkIbnvJblj7fkuLoiJyArIG9kZHNJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbE9kZHMob2Rkc0lkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLmRvd25sb2FkKCdnYW1lL29kZHMvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYG9kZHNfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApCiAgICB9LAogICAgLyoqIOagvOW8j+WMlui1lOeOh+aYvuekuiAqLwogICAgZm9ybWF0T2RkcyhvZGRzKSB7CiAgICAgIGlmICghb2RkcykgcmV0dXJuICcwLjAwJzsKICAgICAgY29uc3QgbnVtID0gcGFyc2VGbG9hdChvZGRzKTsKICAgICAgcmV0dXJuIG51bS50b0ZpeGVkKDIpICsgJ+WAjSc7CiAgICB9LAogICAgLyoqIOagvOW8j+WMluaUtuebiuaYvuekuiAqLwogICAgZm9ybWF0UHJvZml0KG9kZHMsIGFtb3VudCA9IDEpIHsKICAgICAgaWYgKCFvZGRzKSByZXR1cm4gJ++/pTAuMDAnOwogICAgICBjb25zdCBudW0gPSBwYXJzZUZsb2F0KG9kZHMpOwogICAgICBjb25zdCBwcm9maXQgPSAobnVtICogYW1vdW50KS50b0ZpeGVkKDIpOwogICAgICByZXR1cm4gJ++/pScgKyBwcm9maXQ7CiAgICB9LAogICAgLy8g6K6h566X5pS255uK546HCiAgICBjYWxjdWxhdGVQcm9maXRSYXRlKG9kZHMpIHsKICAgICAgaWYgKCFvZGRzKSByZXR1cm4gJzAnOwogICAgICBjb25zdCByYXRlID0gKChwYXJzZUZsb2F0KG9kZHMpIC0gMSkgKiAxMDApLnRvRml4ZWQoMSk7CiAgICAgIHJldHVybiByYXRlOwogICAgfSwKICAgIC8vIOWkhOeQhui1lOeOh+i+k+WFpeWPmOWMlgogICAgaGFuZGxlT2Rkc0NoYW5nZSh2YWx1ZSkgewogICAgICAvLyDpmZDliLblsI/mlbDngrnlkI7kuKTkvY0KICAgICAgaWYgKHZhbHVlICYmIHZhbHVlLnRvU3RyaW5nKCkuaW5jbHVkZXMoJy4nKSkgewogICAgICAgIGNvbnN0IHBhcnRzID0gdmFsdWUudG9TdHJpbmcoKS5zcGxpdCgnLicpOwogICAgICAgIGlmIChwYXJ0c1sxXSAmJiBwYXJ0c1sxXS5sZW5ndGggPiAyKSB7CiAgICAgICAgICB0aGlzLmZvcm0ub2RkcyA9IHBhcnNlRmxvYXQodmFsdWUpLnRvRml4ZWQoMik7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgaA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/game/odds", "sourcesContent": ["<template>\n  <div class=\"app-container odds-container\">\n   \n \n    <!-- 移动端搜索切换按钮 -->\n    <div v-if=\"isMobile\" class=\"mobile-search-toggle\">\n      <el-button\n        @click=\"toggleMobileSearch\"\n        class=\"mobile-search-btn\"\n        :type=\"showMobileSearch ? 'primary' : 'info'\"\n        icon=\"el-icon-search\"\n      >\n        {{ showMobileSearch ? '收起搜索' : '展开搜索' }}\n      </el-button>\n    </div>\n\n    <!-- 搜索区域 -->\n    <div class=\"search-container\" v-show=\"showSearch && (!isMobile || showMobileSearch)\">\n      <div class=\"search-form-wrapper\" :class=\"{ 'mobile-search-form': isMobile }\">\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"!isMobile\" label-width=\"80px\" class=\"search-form\">\n          <el-form-item label=\"玩法名称\" prop=\"methodName\">\n            <el-input\n              v-model=\"queryParams.methodName\"\n              placeholder=\"请输入玩法名称\"\n              clearable\n              prefix-icon=\"el-icon-search\"\n              @keyup.enter.native=\"handleQuery\"\n              :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\n            />\n          </el-form-item>\n          <el-form-item label=\"赔率范围\" prop=\"odds\">\n            <el-input\n              v-model=\"queryParams.odds\"\n              placeholder=\"请输入赔率\"\n              clearable\n              prefix-icon=\"el-icon-money\"\n              @keyup.enter.native=\"handleQuery\"\n              :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\n            />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n        <div class=\"search-toolbar\" :class=\"{ 'mobile-toolbar': isMobile }\">\n          <el-button\n            type=\"info\"\n            icon=\"el-icon-download\"\n            size=\"small\"\n            @click=\"handleExport\"\n            v-hasPermi=\"['game:odds:export']\"\n            class=\"action-btn\"\n          >导出数据</el-button>\n          <right-toolbar v-if=\"!isMobile\" :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n        </div>\n      </div>\n    </div>\n\n    <!-- 玩家选择区域 -->\n    <div class=\"player-selection-container\">\n      <el-card shadow=\"never\" class=\"player-selection-card\">\n        <div class=\"selection-header\">\n          <i class=\"el-icon-user\"></i>\n          <span class=\"selection-title\">选择玩家：</span>\n        </div>\n\n        <!-- PC端：单选按钮组 -->\n        <div v-if=\"!isMobile\" class=\"player-radio-group\">\n          <el-radio-group v-model=\"selectedCustomerId\" @change=\"handleCustomerChange\" size=\"small\">\n            <el-radio-button :label=\"null\" class=\"all-users-btn\">\n              <i class=\"el-icon-s-home\"></i>\n              全部玩家\n            </el-radio-button>\n            <el-radio-button\n              v-for=\"customer in customerList\"\n              :key=\"customer.userId\"\n              :label=\"customer.userId\"\n              class=\"customer-btn\"\n            >\n              <i class=\"el-icon-user-solid\"></i>\n              {{ customer.name }}\n            </el-radio-button>\n          </el-radio-group>\n        </div>\n\n        <!-- 移动端：下拉选择器 -->\n        <div v-else class=\"mobile-player-select\">\n          <el-select\n            v-model=\"selectedCustomerId\"\n            @change=\"handleCustomerChange\"\n            placeholder=\"请选择玩家\"\n            class=\"mobile-select\"\n            clearable\n          >\n            <el-option :value=\"null\" label=\"全部玩家\">\n              <i class=\"el-icon-s-home\"></i>\n              全部玩家\n            </el-option>\n            <el-option\n              v-for=\"customer in customerList\"\n              :key=\"customer.userId\"\n              :value=\"customer.userId\"\n              :label=\"customer.name\"\n            >\n              <i class=\"el-icon-user-solid\"></i>\n              {{ customer.name }}\n            </el-option>\n          </el-select>\n        </div>\n\n        <div class=\"selection-status\">\n          <el-tag v-if=\"!selectedCustomerId\" type=\"info\" effect=\"dark\" :style=\"isMobile ? 'font-size: 12px;' : 'font-size: 16px;'\" size=\"medium\">\n            <i class=\"el-icon-info\"></i>\n            当前显示：默认赔率模板{{ isMobile ? '' : '，如需修改或恢复默认，请选择具体玩家！' }}\n          </el-tag>\n          <el-tag v-else type=\"success\" size=\"medium\" effect=\"dark\" :style=\"isMobile ? 'font-size: 12px;' : 'font-size: 16px;'\">\n            <i class=\"el-icon-user-solid\"></i>\n            当前显示：{{ getCustomerName(selectedCustomerId) }} 的专属赔率\n          </el-tag>\n          <span v-if=\"selectedCustomerId && !hasCustomerOdds\" class=\"no-odds-tip\">\n            <i class=\"el-icon-warning\"></i>\n            该玩家暂无特殊赔率！\n          </span>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 数据表格 -->\n    <div class=\"table-container\">\n      <el-table\n        v-loading=\"loading\"\n        :data=\"oddsList\"\n        @selection-change=\"handleSelectionChange\"\n        class=\"odds-table\"\n        stripe\n        border\n        :header-cell-style=\"{ background: '#f8f9fa', color: '#606266', fontWeight: 'bold' }\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n\n        <el-table-column label=\"玩法名称\" align=\"center\" prop=\"methodName\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"method-name\">\n              <i class=\"el-icon-trophy\" style=\"color: #409EFF; margin-right: 8px;\"></i>\n              <span style=\"font-weight: 500;font-size: 16px;\">{{ scope.row.methodName }}</span>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"玩家名称\" align=\"center\" min-width=\"120\">\n          <template slot-scope=\"scope\">\n            <div class=\"customer-name\">\n              <span v-if=\"scope.row.customerName\" class=\"customer-name-text\">\n                <i class=\"el-icon-user-solid\"></i>\n                {{ scope.row.customerName }}\n              </span>\n              <span v-else class=\"system-default\">\n                <i class=\"el-icon-s-home\"></i>\n                系统默认\n              </span>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"默认赔率\" align=\"center\" min-width=\"120\" v-if=\"selectedCustomerId\">\n          <template slot-scope=\"scope\">\n            <div class=\"default-odds\">\n              <span class=\"odds-value default\">{{ formatOdds(scope.row.defaultOdds || scope.row.odds) }}</span>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column :label=\"selectedCustomerId ? '当前赔率' : '赔率'\" align=\"center\" prop=\"odds\" min-width=\"150\">\n          <template slot-scope=\"scope\">\n            <div class=\"odds-display\">\n              <span :class=\"['odds-value', getOddsClass(scope.row)]\">{{ formatOdds(scope.row.odds) }}</span>\n              <el-tag\n                v-if=\"selectedCustomerId && scope.row.isCustomerOdds\"\n                size=\"mini\"\n                type=\"success\"\n                style=\"margin-left: 8px;\"\n              >\n                专属\n              </el-tag>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"赔率差异\" align=\"center\" min-width=\"100\" v-if=\"selectedCustomerId\">\n          <template slot-scope=\"scope\">\n            <span :class=\"['odds-diff', getOddsDiffClass(scope.row)]\">\n              {{ getOddsDiff(scope.row) }}\n            </span>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"action-buttons\">\n              <el-button\n                v-if=\"!selectedCustomerId\"\n                size=\"mini\"\n                type=\"primary\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['game:odds:edit']\"\n                class=\"action-btn-mini edit-btn\"\n              >修改</el-button>\n              <el-button\n                v-if=\"selectedCustomerId\"\n                size=\"mini\"\n                type=\"primary\"\n                @click=\"handleUpdateCustomerOdds(scope.row)\"\n                v-hasPermi=\"['game:odds:edit']\"\n                class=\"action-btn-mini edit-btn\"\n              >修改</el-button>\n              <el-button\n                v-if=\"selectedCustomerId && scope.row.isCustomerOdds\"\n                size=\"mini\"\n                type=\"warning\"\n                @click=\"handleResetSingleOdds(scope.row)\"\n                v-hasPermi=\"['game:odds:edit']\"\n                class=\"action-btn-mini reset-btn\"\n              >重置</el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改赔率管理对话框 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"open\"\n      width=\"580px\"\n      append-to-body\n      class=\"modern-odds-dialog\"\n      :close-on-click-modal=\"false\"\n      :show-close=\"false\"\n      custom-class=\"beautiful-dialog\"\n    >\n      <!-- 自定义头部 -->\n      <div slot=\"title\" class=\"dialog-header\">\n        <div class=\"header-content\">\n          <div class=\"header-icon\">\n            <i class=\"el-icon-s-data\"></i>\n          </div>\n          <div class=\"header-text\">\n            <h3>{{ title }}</h3>\n            <p>设置玩法赔率，管理投注收益比例</p>\n          </div>\n        </div>\n        <el-button\n          type=\"text\"\n          @click=\"cancel\"\n          class=\"close-btn\"\n          icon=\"el-icon-close\"\n        ></el-button>\n      </div>\n\n      <div class=\"modern-dialog-content\">\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"0\" class=\"modern-odds-form\">\n          <!-- 玩法选择卡片 -->\n          <div class=\"form-card\">\n            <div class=\"card-header\">\n              <i class=\"el-icon-menu\"></i>\n              <span>选择玩法</span>\n              <span class=\"method-count\" v-if=\"methodList.length\">共 {{ methodList.length }} 种玩法</span>\n            </div>\n            <div class=\"card-content\">\n              <div class=\"method-select-wrapper\">\n                <div class=\"select-label\">\n                  <i class=\"el-icon-s-data\"></i>\n                  <span>玩法类型</span>\n                </div>\n                <el-form-item prop=\"methodId\">\n                  <el-select\n                    v-model=\"form.methodId\"\n                    placeholder=\"请选择要设置赔率的玩法\"\n                    @change=\"handleMethodChange\"\n                    class=\"modern-select full-width\"\n                    filterable\n                    popper-class=\"method-select-dropdown\"\n                  >\n                    <el-option\n                      v-for=\"method in methodList\"\n                      :key=\"method.methodId\"\n                      :label=\"method.methodName\"\n                      :value=\"method.methodId\"\n                      class=\"method-option\"\n                    >\n                      <div class=\"option-content\">\n                        <span class=\"option-name\">{{ method.methodName }}</span>\n                        <span class=\"option-id\">ID: {{ method.methodId }}</span>\n                      </div>\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n                <div class=\"method-tips\">\n                  <div class=\"tip-row\">\n                    <i class=\"el-icon-info\"></i>\n                    <span>支持输入关键词快速搜索玩法</span>\n                  </div>\n                  <div class=\"tip-row\">\n                    <i class=\"el-icon-warning\"></i>\n                    <span>每种玩法只能设置一个赔率</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 赔率设置卡片 -->\n          <div class=\"form-card\">\n            <div class=\"card-header\">\n              <i class=\"el-icon-money\"></i>\n              <span>设置赔率</span>\n            </div>\n            <div class=\"card-content\">\n              <el-form-item prop=\"odds\">\n                <div class=\"odds-input-wrapper\">\n                  <el-input\n                    v-model=\"form.odds\"\n                    placeholder=\"请输入赔率倍数\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0.01\"\n                    max=\"999.99\"\n                    class=\"modern-input\"\n                    size=\"large\"\n                    @input=\"handleOddsChange\"\n                  >\n                    <template slot=\"prepend\">\n                      <i class=\"el-icon-s-finance\"></i>\n                      倍率\n                    </template>\n                    <template slot=\"append\">倍</template>\n                  </el-input>\n                </div>\n                <div class=\"input-tips\">\n                  <div class=\"tip-item\">\n                    <i class=\"el-icon-info\"></i>\n                    <span>赔率范围：0.01 - 999.99</span>\n                  </div>\n                  <div class=\"tip-item\">\n                    <i class=\"el-icon-warning\"></i>\n                    <span>支持小数点后两位精度</span>\n                  </div>\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 预览区域卡片 -->\n          <div class=\"form-card preview-card\" v-if=\"form.odds && parseFloat(form.odds) > 0\">\n            <div class=\"card-header preview-header\">\n              <i class=\"el-icon-view\"></i>\n              <span>收益预览</span>\n              <el-tag type=\"success\" size=\"mini\">实时计算</el-tag>\n            </div>\n            <div class=\"card-content\">\n              <div class=\"preview-grid\">\n                <div class=\"preview-item\">\n                  <div class=\"preview-icon\">\n                    <i class=\"el-icon-wallet\"></i>\n                  </div>\n                  <div class=\"preview-info\">\n                    <div class=\"preview-label\">投注金额</div>\n                    <div class=\"preview-value\">￥100.00</div>\n                  </div>\n                </div>\n                <div class=\"preview-arrow\">\n                  <i class=\"el-icon-right\"></i>\n                </div>\n                <div class=\"preview-item\">\n                  <div class=\"preview-icon profit-icon\">\n                    <i class=\"el-icon-trophy\"></i>\n                  </div>\n                  <div class=\"preview-info\">\n                    <div class=\"preview-label\">预期收益</div>\n                    <div class=\"preview-value profit\">{{ formatProfit(form.odds, 100) }}</div>\n                  </div>\n                </div>\n              </div>\n              <div class=\"profit-rate\">\n                <span>收益率：</span>\n                <span class=\"rate-value\">{{ calculateProfitRate(form.odds) }}%</span>\n              </div>\n            </div>\n          </div>\n        </el-form>\n      </div>\n\n      <div slot=\"footer\" class=\"modern-dialog-footer\">\n        <el-button @click=\"cancel\" class=\"cancel-btn\" size=\"large\">\n          <i class=\"el-icon-close\"></i>\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"submitForm\" class=\"submit-btn\" size=\"large\">\n          <i class=\"el-icon-check\"></i>\n          {{ form.oddsId ? '更新赔率' : '创建赔率' }}\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listOdds, getOdds, delOdds, addOdds, updateOdds, getCustomerOdds, updateCustomerOdds, resetCustomerOdds } from \"@/api/game/odds\";\nimport { listMethod } from \"@/api/game/method\";\nimport { listCustomer } from \"@/api/game/customer\";\n\nexport default {\n  name: \"Odds\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 赔率管理表格数据\n      oddsList: [],\n      // 玩法列表\n      methodList: [],\n      // 玩家列表\n      customerList: [],\n      // 选中的玩家ID\n      selectedCustomerId: null,\n      // 是否有玩家专属赔率\n      hasCustomerOdds: false,\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 移动端相关\n      isMobile: false,\n      showMobileSearch: false,\n      resizeTimer: null,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 50,\n        methodName: null,\n        odds: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        methodId: [\n          { required: true, message: \"玩法不能为空\", trigger: \"change\" }\n        ],\n        odds: [\n          { required: true, message: \"赔率不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getMethodList();\n    this.getCustomerList();\n  },\n  mounted() {\n    // 初始化移动端检测\n    this.initMobileDetection();\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听\n    window.removeEventListener('resize', this.handleResize);\n    // 清理防抖定时器\n    if (this.resizeTimer) {\n      clearTimeout(this.resizeTimer);\n      this.resizeTimer = null;\n    }\n  },\n  methods: {\n    /** 初始化移动端检测 */\n    initMobileDetection() {\n      if (typeof window !== 'undefined') {\n        this.isMobile = window.innerWidth <= 768;\n      }\n    },\n    /** 处理窗口大小变化 */\n    handleResize() {\n      // 使用防抖避免频繁触发\n      if (this.resizeTimer) {\n        clearTimeout(this.resizeTimer);\n      }\n      this.resizeTimer = setTimeout(() => {\n        if (typeof window !== 'undefined') {\n          this.isMobile = window.innerWidth <= 768;\n        }\n      }, 100);\n    },\n    /** 切换移动端搜索显示 */\n    toggleMobileSearch() {\n      this.showMobileSearch = !this.showMobileSearch;\n    },\n    /** 切换移动端卡片展开状态 */\n    toggleMobileCard(item, index) {\n      this.$set(item, 'mobileExpanded', !item.mobileExpanded);\n    },\n    /** 查询赔率管理列表 */\n    getList() {\n      this.loading = true;\n      listOdds(this.queryParams).then(response => {\n        this.oddsList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 查询玩法列表 */\n    getMethodList() {\n      // 设置大的pageSize确保获取所有玩法\n      const params = {\n        pageNum: 1,\n        pageSize: 1000 // 设置足够大的数量\n      };\n      listMethod(params).then(response => {\n        this.methodList = response.rows;\n      });\n    },\n    /** 查询玩家列表 */\n    getCustomerList() {\n      const params = {\n        pageNum: 1,\n        pageSize: 1000\n      };\n      listCustomer(params).then(response => {\n        this.customerList = response.rows || [];\n      }).catch(error => {\n        console.error('获取玩家列表失败:', error);\n        this.customerList = [];\n      });\n    },\n    /** 玩家选择变化 */\n    handleCustomerChange(customerId) {\n      this.selectedCustomerId = customerId;\n      if (customerId) {\n        // 加载玩家专属赔率\n        this.loadCustomerOdds(customerId);\n      } else {\n        // 加载默认赔率\n        this.getList();\n      }\n    },\n    /** 加载玩家专属赔率 */\n    loadCustomerOdds(customerId) {\n      this.loading = true;\n      getCustomerOdds(customerId).then(response => {\n        if (response.code === 200 && response.data) {\n          this.oddsList = response.data;\n          this.hasCustomerOdds = response.data.some(item => item.isCustomerOdds);\n          this.total = response.data.length;\n        } else {\n          this.oddsList = [];\n          this.hasCustomerOdds = false;\n          this.total = 0;\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error('加载玩家赔率失败:', error);\n        this.$message.error('加载玩家赔率失败');\n        this.loading = false;\n      });\n    },\n    /** 获取玩家名称 */\n    getCustomerName(customerId) {\n      const customer = this.customerList.find(c => c.userId === customerId);\n      return customer ? customer.name : '未知玩家';\n    },\n    /** 获取赔率样式类 */\n    getOddsClass(row) {\n      if (!this.selectedCustomerId) return 'normal';\n      return row.isCustomerOdds ? 'custom' : 'default';\n    },\n    /** 计算赔率差异 */\n    getOddsDiff(row) {\n      if (!this.selectedCustomerId || !row.defaultOdds) return '-';\n      const diff = (parseFloat(row.odds) - parseFloat(row.defaultOdds)).toFixed(2);\n      return diff > 0 ? `+${diff}` : diff;\n    },\n    /** 获取赔率差异样式类 */\n    getOddsDiffClass(row) {\n      const diff = parseFloat(this.getOddsDiff(row));\n      if (diff > 0) return 'positive';\n      if (diff < 0) return 'negative';\n      return 'neutral';\n    },\n    /** 修改玩家赔率 */\n    handleUpdateCustomerOdds(row) {\n      // 打开编辑对话框，设置为玩家赔率编辑模式\n      this.reset();\n      this.form = { ...row };\n      this.form.isCustomerEdit = true;\n      this.form.customerId = this.selectedCustomerId;\n      this.title = `修改玩家赔率 - ${this.getCustomerName(this.selectedCustomerId)}`;\n      this.open = true;\n    },\n    /** 重置单个玩家赔率 */\n    handleResetSingleOdds(row) {\n      this.$confirm(`确认将玩家\"${this.getCustomerName(this.selectedCustomerId)}\"的\"${row.methodName}\"赔率重置为默认值吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const updateData = [{\n          oddsId: row.oddsId,\n          odds: row.defaultOdds,\n          customerId: this.selectedCustomerId,\n          methodId: row.methodId\n        }];\n\n        updateCustomerOdds(updateData).then(response => {\n          this.$message.success('重置成功');\n          this.loadCustomerOdds(this.selectedCustomerId);\n        }).catch(error => {\n          this.$message.error('重置失败');\n        });\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        oddsId: null,\n        methodId: null,\n        methodName: null,\n        odds: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.oddsId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加赔率管理\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const oddsId = row.oddsId || this.ids\n      getOdds(oddsId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改赔率管理\";\n      });\n    },\n    /** 玩法选择改变 */\n    handleMethodChange(methodId) {\n      const method = this.methodList.find(item => item.methodId === methodId);\n      if (method) {\n        this.form.methodName = method.methodName;\n      }\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 判断是否为玩家赔率编辑\n          if (this.form.isCustomerEdit && this.form.customerId) {\n            // 玩家赔率编辑\n            const updateData = [{\n              oddsId: this.form.oddsId || null, // 如果没有oddsId则为null，后端会创建新记录\n              odds: this.form.odds,\n              customerId: this.form.customerId,\n              methodId: this.form.methodId,\n              sysUserId: this.form.sysUserId,\n              methodName: this.form.methodName\n            }];\n\n            updateCustomerOdds(updateData).then(() => {\n              this.$modal.msgSuccess(\"修改玩家赔率成功\");\n              this.open = false;\n              this.loadCustomerOdds(this.form.customerId);\n            }).catch(error => {\n              console.error('修改玩家赔率失败:', error);\n              this.$modal.msgError(\"修改玩家赔率失败\");\n            });\n          } else {\n            // 默认赔率编辑\n            if (this.form.oddsId != null) {\n              updateOdds(this.form).then(() => {\n                this.$modal.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n              });\n            } else {\n              addOdds(this.form).then(() => {\n                this.$modal.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n              });\n            }\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const oddsIds = row.oddsId || this.ids;\n      this.$modal.confirm('是否确认删除赔率管理编号为\"' + oddsIds + '\"的数据项？').then(function() {\n        return delOdds(oddsIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('game/odds/export', {\n        ...this.queryParams\n      }, `odds_${new Date().getTime()}.xlsx`)\n    },\n    /** 格式化赔率显示 */\n    formatOdds(odds) {\n      if (!odds) return '0.00';\n      const num = parseFloat(odds);\n      return num.toFixed(2) + '倍';\n    },\n    /** 格式化收益显示 */\n    formatProfit(odds, amount = 1) {\n      if (!odds) return '￥0.00';\n      const num = parseFloat(odds);\n      const profit = (num * amount).toFixed(2);\n      return '￥' + profit;\n    },\n    // 计算收益率\n    calculateProfitRate(odds) {\n      if (!odds) return '0';\n      const rate = ((parseFloat(odds) - 1) * 100).toFixed(1);\n      return rate;\n    },\n    // 处理赔率输入变化\n    handleOddsChange(value) {\n      // 限制小数点后两位\n      if (value && value.toString().includes('.')) {\n        const parts = value.toString().split('.');\n        if (parts[1] && parts[1].length > 2) {\n          this.form.odds = parseFloat(value).toFixed(2);\n        }\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.odds-container {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 页面标题样式 */\n.page-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px 30px;\n  border-radius: 12px;\n  margin-bottom: 24px;\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.page-title i {\n  font-size: 28px;\n  margin-right: 12px;\n}\n\n.page-description {\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n/* 搜索区域样式 */\n.search-container {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.search-form-wrapper {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 20px;\n}\n\n.search-form {\n  flex: 1;\n  margin-bottom: 0;\n}\n\n.search-toolbar {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  flex-shrink: 0;\n}\n\n/* 工具栏样式 */\n.toolbar-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 16px 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 12px;\n}\n\n.action-btn {\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.action-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n/* 表格容器样式 */\n.table-container {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.odds-table {\n  border-radius: 8px;\n}\n\n/* 表格内容样式 */\n.method-name {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.odds-display {\n  text-align: center;\n}\n\n.odds-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #e6a23c;\n  background: linear-gradient(45deg, #f39c12, #e67e22);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  padding: 4px 12px;\n  border-radius: 20px;\n  background-color: #fef7e6;\n  border: 1px solid #f5dab1;\n  display: inline-block;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n  justify-content: center;\n}\n\n.action-buttons .el-button {\n  margin: 0;\n}\n\n.action-btn-mini {\n  border-radius: 16px;\n  padding: 6px 12px;\n  font-size: 12px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  border: none;\n  min-width: 50px;\n  height: 28px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-btn-mini:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n\n.edit-btn {\n  background: #409EFF;\n  color: white;\n}\n\n.edit-btn:hover {\n  background: #66b1ff;\n}\n\n.delete-btn {\n  background: #F56C6C;\n  color: white;\n}\n\n.delete-btn:hover {\n  background: #f78989;\n}\n\n/* 现代化弹窗样式 */\n.modern-odds-dialog {\n  .el-dialog {\n    border-radius: 16px;\n    overflow: hidden;\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\n  }\n\n  .el-dialog__header {\n    padding: 0;\n    border-bottom: none;\n  }\n\n  .el-dialog__body {\n    padding: 0;\n  }\n\n  .el-dialog__footer {\n    padding: 0;\n    border-top: none;\n  }\n}\n\n/* 自定义头部 */\n.dialog-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.header-icon {\n  width: 48px;\n  height: 48px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n}\n\n.header-text h3 {\n  margin: 0 0 4px 0;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.header-text p {\n  margin: 0;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.close-btn {\n  color: white !important;\n  font-size: 20px;\n  padding: 8px;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 8px;\n  }\n}\n\n/* 内容区域 */\n.modern-dialog-content {\n  padding: 20px;\n  background: #fafbfc;\n}\n\n.modern-odds-form {\n  .el-form-item {\n    margin-bottom: 0;\n  }\n}\n\n/* 表单卡片 */\n.form-card {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  margin-bottom: 16px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\n  }\n}\n\n.card-header {\n  background: #f8f9fa;\n  padding: 12px 16px;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: #495057;\n  font-size: 14px;\n\n  i {\n    color: #667eea;\n    font-size: 16px;\n  }\n}\n\n.method-count {\n  margin-left: auto;\n  font-size: 12px;\n  color: #909399;\n  background: #e9ecef;\n  padding: 2px 8px;\n  border-radius: 12px;\n}\n\n.card-content {\n  padding: 16px;\n}\n\n/* 玩法选择区域样式 */\n.method-select-wrapper {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.select-label {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 13px;\n  color: #606266;\n  font-weight: 500;\n\n  i {\n    color: #667eea;\n    font-size: 14px;\n  }\n}\n\n.full-width {\n  width: 100%;\n}\n\n.method-tips {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n  margin-top: 8px;\n}\n\n.tip-row {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 12px;\n  color: #909399;\n\n  i {\n    color: #667eea;\n    font-size: 12px;\n  }\n}\n\n/* 现代化选择器 */\n.modern-select {\n  .el-input__inner {\n    height: 44px;\n    border: 2px solid #e9ecef;\n    border-radius: 8px;\n    font-size: 14px;\n    transition: all 0.3s ease;\n    padding-left: 12px;\n    padding-right: 30px;\n\n    &:focus {\n      border-color: #667eea;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n    }\n\n    &::placeholder {\n      color: #c0c4cc;\n      font-size: 13px;\n    }\n  }\n\n  .el-input__suffix {\n    right: 8px;\n  }\n}\n\n.method-option {\n  padding: 12px 16px;\n  height: auto;\n  line-height: 1.5;\n}\n\n.option-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  min-height: 24px;\n}\n\n.option-name {\n  font-weight: 500;\n  color: #2c3e50;\n  flex: 1;\n  white-space: normal;\n  word-wrap: break-word;\n  margin-right: 8px;\n}\n\n.option-id {\n  color: #8492a6;\n  font-size: 12px;\n  background: #f8f9fa;\n  padding: 2px 8px;\n  border-radius: 4px;\n  white-space: nowrap;\n  flex-shrink: 0;\n}\n\n/* 现代化输入框 */\n.odds-input-wrapper {\n  margin-bottom: 12px;\n}\n\n.modern-input {\n  .el-input__inner {\n    height: 40px;\n    border: 2px solid #e9ecef;\n    border-radius: 8px;\n    font-size: 14px;\n    transition: all 0.3s ease;\n\n    &:focus {\n      border-color: #667eea;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n    }\n  }\n\n  .el-input-group__prepend {\n    background: #667eea;\n    color: white;\n    border: none;\n    border-radius: 8px 0 0 8px;\n\n    i {\n      margin-right: 4px;\n    }\n  }\n\n  .el-input-group__append {\n    background: #f8f9fa;\n    color: #495057;\n    border: 2px solid #e9ecef;\n    border-left: none;\n    border-radius: 0 8px 8px 0;\n  }\n}\n\n/* 输入提示 */\n.input-tips {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.tip-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: #6c757d;\n\n  i {\n    color: #667eea;\n  }\n}\n\n/* 预览卡片 */\n.preview-card {\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);\n  border: 2px solid #667eea;\n}\n\n.preview-header {\n  background: rgba(102, 126, 234, 0.1);\n  color: #667eea;\n\n  .el-tag {\n    margin-left: auto;\n  }\n}\n\n.preview-grid {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  margin-bottom: 12px;\n}\n\n.preview-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.preview-icon {\n  width: 32px;\n  height: 32px;\n  background: #e3f2fd;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #1976d2;\n  font-size: 16px;\n}\n\n.profit-icon {\n  background: #e8f5e8;\n  color: #4caf50;\n}\n\n.preview-arrow {\n  color: #667eea;\n  font-size: 20px;\n  font-weight: bold;\n}\n\n.preview-info {\n  flex: 1;\n}\n\n.preview-label {\n  font-size: 12px;\n  color: #6c757d;\n  margin-bottom: 4px;\n}\n\n.preview-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.preview-value.profit {\n  color: #4caf50;\n  font-size: 18px;\n}\n\n.profit-rate {\n  text-align: center;\n  padding: 12px;\n  background: white;\n  border-radius: 8px;\n  font-size: 14px;\n  color: #495057;\n}\n\n.rate-value {\n  font-weight: 600;\n  color: #4caf50;\n  font-size: 16px;\n}\n\n/* 底部按钮 */\n.modern-dialog-footer {\n  padding: 16px 20px;\n  background: #f8f9fa;\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n\n.cancel-btn {\n  height: 36px;\n  padding: 0 20px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: #adb5bd;\n    background: #f8f9fa;\n  }\n}\n\n.submit-btn {\n  height: 36px;\n  padding: 0 24px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .odds-container {\n    padding: 12px;\n  }\n\n  .toolbar-container {\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .toolbar-left {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n    gap: 4px;\n  }\n\n  .action-buttons .el-button {\n    margin: 0 4px 4px 0;\n  }\n\n  .action-buttons .el-button:last-child {\n    margin-right: 0;\n  }\n\n  .modern-dialog-content {\n    padding: 20px;\n  }\n\n  .preview-grid {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .preview-arrow {\n    transform: rotate(90deg);\n  }\n\n  .modern-dialog-footer {\n    padding: 20px;\n    flex-direction: column;\n  }\n\n  .cancel-btn,\n  .submit-btn {\n    width: 100%;\n  }\n}\n\n/* 玩家选择区域样式 */\n.player-selection-container {\n  margin-bottom: 20px;\n}\n\n.player-selection-card {\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  background: #fafbfc;\n}\n\n.selection-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.selection-header i {\n  margin-right: 8px;\n  color: #409eff;\n  font-size: 16px;\n}\n\n.selection-title {\n  font-size: 16px;\n}\n\n.player-radio-group {\n  margin-bottom: 16px;\n}\n\n.player-radio-group .el-radio-group {\n  display: inline-block;\n  font-size: 0; /* 消除inline-block元素间的空白 */\n  line-height: 0;\n}\n\n.player-radio-group .el-radio-group > * {\n  font-size: 14px; /* 恢复字体大小 */\n  line-height: normal;\n}\n\n.player-radio-group .el-radio-button {\n  margin: 0 !important;\n}\n\n/* 移除了margin-right设置，让按钮完全贴合 */\n\n/* 强制移除Element UI默认的按钮间距 */\n.player-radio-group .el-radio-button {\n  margin: 0 !important;\n  display: inline-block !important;\n  vertical-align: top !important;\n}\n\n.player-radio-group .el-radio-button:not(:first-child) {\n  margin-left: -1px !important;\n}\n\n.player-radio-group .el-radio-button:not(:first-child) .el-radio-button__inner {\n  border-left: 0 !important;\n}\n\n.player-radio-group .el-radio-button__inner {\n  border-radius: 0 !important;\n  margin: 0 !important;\n  display: inline-block !important;\n}\n\n.player-radio-group .el-radio-button:first-child .el-radio-button__inner {\n  border-radius: 4px 0 0 4px !important;\n}\n\n.player-radio-group .el-radio-button:last-child .el-radio-button__inner {\n  border-radius: 0 4px 4px 0 !important;\n}\n\n.all-users-btn .el-radio-button__inner {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-color: #667eea;\n  color: white;\n  font-weight: 500;\n  margin: 0;\n  border-radius: 4px 0 0 4px;\n}\n\n.all-users-btn .el-radio-button__inner i {\n  margin-right: 6px;\n}\n\n.all-users-btn.is-active .el-radio-button__inner {\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n  border-color: #5a6fd8;\n  box-shadow: 0 2px 8px rgba(90, 111, 216, 0.3);\n}\n\n.customer-btn .el-radio-button__inner {\n  background: #f8f9fa;\n  border-color: #e9ecef;\n  color: #495057;\n  margin: 0;\n  border-radius: 0;\n}\n\n.customer-btn:last-child .el-radio-button__inner {\n  border-radius: 0 4px 4px 0;\n}\n\n.customer-btn .el-radio-button__inner i {\n  margin-right: 6px;\n  color: #67c23a;\n}\n\n.customer-btn.is-active .el-radio-button__inner {\n  background: #67c23a;\n  border-color: #67c23a;\n  color: white;\n  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);\n}\n\n.selection-status {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.selection-status .el-tag {\n  font-size: 12px;\n}\n\n.selection-status .el-tag i {\n  margin-right: 4px;\n}\n\n.no-odds-tip {\n  color: #e6a23c;\n  font-size: 12px;\n}\n\n.no-odds-tip i {\n  margin-right: 4px;\n}\n\n/* 赔率对比样式 */\n.odds-value.normal {\n  color: #303133;\n  font-weight: 600;\n}\n\n.odds-value.custom {\n  color: #67c23a;\n  font-weight: 600;\n}\n\n.odds-value.default {\n  color: #909399;\n  font-weight: 600;\n}\n\n.default-odds .odds-value {\n  color: #909399;\n  font-weight: 500;\n}\n\n.odds-diff {\n  font-weight: 600;\n  font-size: 12px;\n}\n\n.odds-diff.positive {\n  color: #f56c6c;\n}\n\n.odds-diff.negative {\n  color: #67c23a;\n}\n\n.odds-diff.neutral {\n  color: #909399;\n}\n\n/* 玩家名称列样式 */\n.customer-name {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.customer-name-text {\n  color: #67c23a;\n  font-weight: 500;\n}\n\n.customer-name-text i {\n  margin-right: 6px;\n  color: #67c23a;\n}\n\n.system-default {\n  color: #909399;\n  font-weight: 500;\n}\n\n.system-default i {\n  margin-right: 6px;\n  color: #409eff;\n}\n\n/* 全局下拉框样式 */\n.method-select-dropdown {\n  .el-select-dropdown__item {\n    height: auto !important;\n    line-height: 1.5 !important;\n    padding: 12px 16px !important;\n    white-space: normal !important;\n    word-wrap: break-word !important;\n  }\n}\n</style>\n"]}]}