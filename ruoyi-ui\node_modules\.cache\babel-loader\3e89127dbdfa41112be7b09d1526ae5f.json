{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\odds\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\odds\\index.vue", "mtime": 1758866059933}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\babel.config.js", "mtime": 1750852368688}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_odds", "require", "_method", "_customer", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "oddsList", "methodList", "customerList", "selectedCustomerId", "hasCustomerOdds", "title", "open", "isMobile", "showMobileSearch", "resizeTimer", "queryParams", "pageNum", "pageSize", "methodName", "odds", "form", "rules", "methodId", "required", "message", "trigger", "created", "getList", "getMethodList", "getCustomerList", "mounted", "initMobileDetection", "window", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "clearTimeout", "methods", "innerWidth", "_this", "setTimeout", "toggleMobileSearch", "toggleMobileCard", "item", "index", "$set", "mobileExpanded", "_this2", "listOdds", "then", "response", "rows", "_this3", "params", "listMethod", "_this4", "listCustomer", "catch", "error", "console", "handleCustomerChange", "customerId", "loadCustomerOdds", "_this5", "getCustomerOdds", "code", "some", "isCustomerOdds", "length", "$message", "getCustomerName", "customer", "find", "c", "userId", "getOddsClass", "row", "getOddsDiff", "defaultOdds", "diff", "parseFloat", "toFixed", "concat", "getOddsDiffClass", "handleUpdateCustomerOdds", "reset", "_objectSpread2", "default", "isCustomerEdit", "handleResetSingleOdds", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "type", "updateData", "oddsId", "updateCustomerOdds", "success", "cancel", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "handleAdd", "handleUpdate", "_this7", "get<PERSON>dds", "handleMethodChange", "method", "submitForm", "_this8", "$refs", "validate", "valid", "sysUserId", "$modal", "msgSuccess", "msgError", "updateOdds", "addOdds", "handleDelete", "_this9", "oddsIds", "confirm", "<PERSON><PERSON><PERSON><PERSON>", "handleExport", "download", "Date", "getTime", "formatOdds", "num", "formatProfit", "amount", "arguments", "undefined", "profit", "calculateProfitRate", "rate", "handleOddsChange", "value", "toString", "includes", "parts", "split"], "sources": ["src/views/game/odds/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container odds-container\">\n   \n \n    <!-- 移动端搜索切换按钮 -->\n    <div v-if=\"isMobile\" class=\"mobile-search-toggle\">\n      <el-button\n        @click=\"toggleMobileSearch\"\n        class=\"mobile-search-btn\"\n        :type=\"showMobileSearch ? 'primary' : 'info'\"\n        icon=\"el-icon-search\"\n      >\n        {{ showMobileSearch ? '收起搜索' : '展开搜索' }}\n      </el-button>\n    </div>\n\n    <!-- 搜索区域 -->\n    <div class=\"search-container\" v-show=\"showSearch && (!isMobile || showMobileSearch)\">\n      <div class=\"search-form-wrapper\" :class=\"{ 'mobile-search-form': isMobile }\">\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"!isMobile\" label-width=\"80px\" class=\"search-form\">\n          <el-form-item label=\"玩法名称\" prop=\"methodName\">\n            <el-input\n              v-model=\"queryParams.methodName\"\n              placeholder=\"请输入玩法名称\"\n              clearable\n              prefix-icon=\"el-icon-search\"\n              @keyup.enter.native=\"handleQuery\"\n              :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\n            />\n          </el-form-item>\n          <el-form-item label=\"赔率范围\" prop=\"odds\">\n            <el-input\n              v-model=\"queryParams.odds\"\n              placeholder=\"请输入赔率\"\n              clearable\n              prefix-icon=\"el-icon-money\"\n              @keyup.enter.native=\"handleQuery\"\n              :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\n            />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n        <div class=\"search-toolbar\" :class=\"{ 'mobile-toolbar': isMobile }\">\n          <el-button\n            type=\"info\"\n            icon=\"el-icon-download\"\n            size=\"small\"\n            @click=\"handleExport\"\n            v-hasPermi=\"['game:odds:export']\"\n            class=\"action-btn\"\n          >导出数据</el-button>\n          <right-toolbar v-if=\"!isMobile\" :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n        </div>\n      </div>\n    </div>\n\n    <!-- 玩家选择区域 -->\n    <div class=\"player-selection-container\">\n      <el-card shadow=\"never\" class=\"player-selection-card\">\n        <div class=\"selection-header\">\n          <i class=\"el-icon-user\"></i>\n          <span class=\"selection-title\">选择玩家：</span>\n        </div>\n\n        <!-- PC端：单选按钮组 -->\n        <div v-if=\"!isMobile\" class=\"player-radio-group\">\n          <el-radio-group v-model=\"selectedCustomerId\" @change=\"handleCustomerChange\" size=\"small\">\n            <el-radio-button :label=\"null\" class=\"all-users-btn\">\n              <i class=\"el-icon-s-home\"></i>\n              全部玩家\n            </el-radio-button>\n            <el-radio-button\n              v-for=\"customer in customerList\"\n              :key=\"customer.userId\"\n              :label=\"customer.userId\"\n              class=\"customer-btn\"\n            >\n              <i class=\"el-icon-user-solid\"></i>\n              {{ customer.name }}\n            </el-radio-button>\n          </el-radio-group>\n        </div>\n\n        <!-- 移动端：下拉选择器 -->\n        <div v-else class=\"mobile-player-select\">\n          <el-select\n            v-model=\"selectedCustomerId\"\n            @change=\"handleCustomerChange\"\n            placeholder=\"请选择玩家\"\n            class=\"mobile-select\"\n            clearable\n          >\n            <el-option :value=\"null\" label=\"全部玩家\">\n              <i class=\"el-icon-s-home\"></i>\n              全部玩家\n            </el-option>\n            <el-option\n              v-for=\"customer in customerList\"\n              :key=\"customer.userId\"\n              :value=\"customer.userId\"\n              :label=\"customer.name\"\n            >\n              <i class=\"el-icon-user-solid\"></i>\n              {{ customer.name }}\n            </el-option>\n          </el-select>\n        </div>\n\n        <div class=\"selection-status\">\n          <el-tag v-if=\"!selectedCustomerId\" type=\"info\" effect=\"dark\" :style=\"isMobile ? 'font-size: 12px;' : 'font-size: 16px;'\" size=\"medium\">\n            <i class=\"el-icon-info\"></i>\n            当前显示：默认赔率模板{{ isMobile ? '' : '，如需修改或恢复默认，请选择具体玩家！' }}\n          </el-tag>\n          <el-tag v-else type=\"success\" size=\"medium\" effect=\"dark\" :style=\"isMobile ? 'font-size: 12px;' : 'font-size: 16px;'\">\n            <i class=\"el-icon-user-solid\"></i>\n            当前显示：{{ getCustomerName(selectedCustomerId) }} 的专属赔率\n          </el-tag>\n          <span v-if=\"selectedCustomerId && !hasCustomerOdds\" class=\"no-odds-tip\">\n            <i class=\"el-icon-warning\"></i>\n            该玩家暂无特殊赔率！\n          </span>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 数据表格 -->\n    <div class=\"table-container\">\n      <el-table\n        v-loading=\"loading\"\n        :data=\"oddsList\"\n        @selection-change=\"handleSelectionChange\"\n        class=\"odds-table\"\n        stripe\n        border\n        :header-cell-style=\"{ background: '#f8f9fa', color: '#606266', fontWeight: 'bold' }\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n\n        <el-table-column label=\"玩法名称\" align=\"center\" prop=\"methodName\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"method-name\">\n              <i class=\"el-icon-trophy\" style=\"color: #409EFF; margin-right: 8px;\"></i>\n              <span style=\"font-weight: 500;font-size: 16px;\">{{ scope.row.methodName }}</span>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"玩家名称\" align=\"center\" min-width=\"120\">\n          <template slot-scope=\"scope\">\n            <div class=\"customer-name\">\n              <span v-if=\"scope.row.customerName\" class=\"customer-name-text\">\n                <i class=\"el-icon-user-solid\"></i>\n                {{ scope.row.customerName }}\n              </span>\n              <span v-else class=\"system-default\">\n                <i class=\"el-icon-s-home\"></i>\n                系统默认\n              </span>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"默认赔率\" align=\"center\" min-width=\"120\" v-if=\"selectedCustomerId\">\n          <template slot-scope=\"scope\">\n            <div class=\"default-odds\">\n              <span class=\"odds-value default\">{{ formatOdds(scope.row.defaultOdds || scope.row.odds) }}</span>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column :label=\"selectedCustomerId ? '当前赔率' : '赔率'\" align=\"center\" prop=\"odds\" min-width=\"150\">\n          <template slot-scope=\"scope\">\n            <div class=\"odds-display\">\n              <span :class=\"['odds-value', getOddsClass(scope.row)]\">{{ formatOdds(scope.row.odds) }}</span>\n              <el-tag\n                v-if=\"selectedCustomerId && scope.row.isCustomerOdds\"\n                size=\"mini\"\n                type=\"success\"\n                style=\"margin-left: 8px;\"\n              >\n                专属\n              </el-tag>\n            </div>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"赔率差异\" align=\"center\" min-width=\"100\" v-if=\"selectedCustomerId\">\n          <template slot-scope=\"scope\">\n            <span :class=\"['odds-diff', getOddsDiffClass(scope.row)]\">\n              {{ getOddsDiff(scope.row) }}\n            </span>\n          </template>\n        </el-table-column>\n\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"action-buttons\">\n              <el-button\n                v-if=\"!selectedCustomerId\"\n                size=\"mini\"\n                type=\"primary\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['game:odds:edit']\"\n                class=\"action-btn-mini edit-btn\"\n              >修改</el-button>\n              <el-button\n                v-if=\"selectedCustomerId\"\n                size=\"mini\"\n                type=\"primary\"\n                @click=\"handleUpdateCustomerOdds(scope.row)\"\n                v-hasPermi=\"['game:odds:edit']\"\n                class=\"action-btn-mini edit-btn\"\n              >修改</el-button>\n              <el-button\n                v-if=\"selectedCustomerId && scope.row.isCustomerOdds\"\n                size=\"mini\"\n                type=\"warning\"\n                @click=\"handleResetSingleOdds(scope.row)\"\n                v-hasPermi=\"['game:odds:edit']\"\n                class=\"action-btn-mini reset-btn\"\n              >重置</el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改赔率管理对话框 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"open\"\n      width=\"580px\"\n      append-to-body\n      class=\"modern-odds-dialog\"\n      :close-on-click-modal=\"false\"\n      :show-close=\"false\"\n      custom-class=\"beautiful-dialog\"\n    >\n      <!-- 自定义头部 -->\n      <div slot=\"title\" class=\"dialog-header\">\n        <div class=\"header-content\">\n          <div class=\"header-icon\">\n            <i class=\"el-icon-s-data\"></i>\n          </div>\n          <div class=\"header-text\">\n            <h3>{{ title }}</h3>\n            <p>设置玩法赔率，管理投注收益比例</p>\n          </div>\n        </div>\n        <el-button\n          type=\"text\"\n          @click=\"cancel\"\n          class=\"close-btn\"\n          icon=\"el-icon-close\"\n        ></el-button>\n      </div>\n\n      <div class=\"modern-dialog-content\">\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"0\" class=\"modern-odds-form\">\n          <!-- 玩法选择卡片 -->\n          <div class=\"form-card\">\n            <div class=\"card-header\">\n              <i class=\"el-icon-menu\"></i>\n              <span>选择玩法</span>\n              <span class=\"method-count\" v-if=\"methodList.length\">共 {{ methodList.length }} 种玩法</span>\n            </div>\n            <div class=\"card-content\">\n              <div class=\"method-select-wrapper\">\n                <div class=\"select-label\">\n                  <i class=\"el-icon-s-data\"></i>\n                  <span>玩法类型</span>\n                </div>\n                <el-form-item prop=\"methodId\">\n                  <el-select\n                    v-model=\"form.methodId\"\n                    placeholder=\"请选择要设置赔率的玩法\"\n                    @change=\"handleMethodChange\"\n                    class=\"modern-select full-width\"\n                    filterable\n                    popper-class=\"method-select-dropdown\"\n                  >\n                    <el-option\n                      v-for=\"method in methodList\"\n                      :key=\"method.methodId\"\n                      :label=\"method.methodName\"\n                      :value=\"method.methodId\"\n                      class=\"method-option\"\n                    >\n                      <div class=\"option-content\">\n                        <span class=\"option-name\">{{ method.methodName }}</span>\n                        <span class=\"option-id\">ID: {{ method.methodId }}</span>\n                      </div>\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n                <div class=\"method-tips\">\n                  <div class=\"tip-row\">\n                    <i class=\"el-icon-info\"></i>\n                    <span>支持输入关键词快速搜索玩法</span>\n                  </div>\n                  <div class=\"tip-row\">\n                    <i class=\"el-icon-warning\"></i>\n                    <span>每种玩法只能设置一个赔率</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 赔率设置卡片 -->\n          <div class=\"form-card\">\n            <div class=\"card-header\">\n              <i class=\"el-icon-money\"></i>\n              <span>设置赔率</span>\n            </div>\n            <div class=\"card-content\">\n              <el-form-item prop=\"odds\">\n                <div class=\"odds-input-wrapper\">\n                  <el-input\n                    v-model=\"form.odds\"\n                    placeholder=\"请输入赔率倍数\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0.01\"\n                    max=\"999.99\"\n                    class=\"modern-input\"\n                    size=\"large\"\n                    @input=\"handleOddsChange\"\n                  >\n                    <template slot=\"prepend\">\n                      <i class=\"el-icon-s-finance\"></i>\n                      倍率\n                    </template>\n                    <template slot=\"append\">倍</template>\n                  </el-input>\n                </div>\n                <div class=\"input-tips\">\n                  <div class=\"tip-item\">\n                    <i class=\"el-icon-info\"></i>\n                    <span>赔率范围：0.01 - 999.99</span>\n                  </div>\n                  <div class=\"tip-item\">\n                    <i class=\"el-icon-warning\"></i>\n                    <span>支持小数点后两位精度</span>\n                  </div>\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 预览区域卡片 -->\n          <div class=\"form-card preview-card\" v-if=\"form.odds && parseFloat(form.odds) > 0\">\n            <div class=\"card-header preview-header\">\n              <i class=\"el-icon-view\"></i>\n              <span>收益预览</span>\n              <el-tag type=\"success\" size=\"mini\">实时计算</el-tag>\n            </div>\n            <div class=\"card-content\">\n              <div class=\"preview-grid\">\n                <div class=\"preview-item\">\n                  <div class=\"preview-icon\">\n                    <i class=\"el-icon-wallet\"></i>\n                  </div>\n                  <div class=\"preview-info\">\n                    <div class=\"preview-label\">投注金额</div>\n                    <div class=\"preview-value\">￥100.00</div>\n                  </div>\n                </div>\n                <div class=\"preview-arrow\">\n                  <i class=\"el-icon-right\"></i>\n                </div>\n                <div class=\"preview-item\">\n                  <div class=\"preview-icon profit-icon\">\n                    <i class=\"el-icon-trophy\"></i>\n                  </div>\n                  <div class=\"preview-info\">\n                    <div class=\"preview-label\">预期收益</div>\n                    <div class=\"preview-value profit\">{{ formatProfit(form.odds, 100) }}</div>\n                  </div>\n                </div>\n              </div>\n              <div class=\"profit-rate\">\n                <span>收益率：</span>\n                <span class=\"rate-value\">{{ calculateProfitRate(form.odds) }}%</span>\n              </div>\n            </div>\n          </div>\n        </el-form>\n      </div>\n\n      <div slot=\"footer\" class=\"modern-dialog-footer\">\n        <el-button @click=\"cancel\" class=\"cancel-btn\" size=\"large\">\n          <i class=\"el-icon-close\"></i>\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"submitForm\" class=\"submit-btn\" size=\"large\">\n          <i class=\"el-icon-check\"></i>\n          {{ form.oddsId ? '更新赔率' : '创建赔率' }}\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listOdds, getOdds, delOdds, addOdds, updateOdds, getCustomerOdds, updateCustomerOdds, resetCustomerOdds } from \"@/api/game/odds\";\nimport { listMethod } from \"@/api/game/method\";\nimport { listCustomer } from \"@/api/game/customer\";\n\nexport default {\n  name: \"Odds\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 赔率管理表格数据\n      oddsList: [],\n      // 玩法列表\n      methodList: [],\n      // 玩家列表\n      customerList: [],\n      // 选中的玩家ID\n      selectedCustomerId: null,\n      // 是否有玩家专属赔率\n      hasCustomerOdds: false,\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 移动端相关\n      isMobile: false,\n      showMobileSearch: false,\n      resizeTimer: null,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 50,\n        methodName: null,\n        odds: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        methodId: [\n          { required: true, message: \"玩法不能为空\", trigger: \"change\" }\n        ],\n        odds: [\n          { required: true, message: \"赔率不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getMethodList();\n    this.getCustomerList();\n  },\n  mounted() {\n    // 初始化移动端检测\n    this.initMobileDetection();\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听\n    window.removeEventListener('resize', this.handleResize);\n    // 清理防抖定时器\n    if (this.resizeTimer) {\n      clearTimeout(this.resizeTimer);\n      this.resizeTimer = null;\n    }\n  },\n  methods: {\n    /** 初始化移动端检测 */\n    initMobileDetection() {\n      if (typeof window !== 'undefined') {\n        this.isMobile = window.innerWidth <= 768;\n      }\n    },\n    /** 处理窗口大小变化 */\n    handleResize() {\n      // 使用防抖避免频繁触发\n      if (this.resizeTimer) {\n        clearTimeout(this.resizeTimer);\n      }\n      this.resizeTimer = setTimeout(() => {\n        if (typeof window !== 'undefined') {\n          this.isMobile = window.innerWidth <= 768;\n        }\n      }, 100);\n    },\n    /** 切换移动端搜索显示 */\n    toggleMobileSearch() {\n      this.showMobileSearch = !this.showMobileSearch;\n    },\n    /** 切换移动端卡片展开状态 */\n    toggleMobileCard(item, index) {\n      this.$set(item, 'mobileExpanded', !item.mobileExpanded);\n    },\n    /** 查询赔率管理列表 */\n    getList() {\n      this.loading = true;\n      listOdds(this.queryParams).then(response => {\n        this.oddsList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 查询玩法列表 */\n    getMethodList() {\n      // 设置大的pageSize确保获取所有玩法\n      const params = {\n        pageNum: 1,\n        pageSize: 1000 // 设置足够大的数量\n      };\n      listMethod(params).then(response => {\n        this.methodList = response.rows;\n      });\n    },\n    /** 查询玩家列表 */\n    getCustomerList() {\n      const params = {\n        pageNum: 1,\n        pageSize: 1000\n      };\n      listCustomer(params).then(response => {\n        this.customerList = response.rows || [];\n      }).catch(error => {\n        console.error('获取玩家列表失败:', error);\n        this.customerList = [];\n      });\n    },\n    /** 玩家选择变化 */\n    handleCustomerChange(customerId) {\n      this.selectedCustomerId = customerId;\n      if (customerId) {\n        // 加载玩家专属赔率\n        this.loadCustomerOdds(customerId);\n      } else {\n        // 加载默认赔率\n        this.getList();\n      }\n    },\n    /** 加载玩家专属赔率 */\n    loadCustomerOdds(customerId) {\n      this.loading = true;\n      getCustomerOdds(customerId).then(response => {\n        if (response.code === 200 && response.data) {\n          this.oddsList = response.data;\n          this.hasCustomerOdds = response.data.some(item => item.isCustomerOdds);\n          this.total = response.data.length;\n        } else {\n          this.oddsList = [];\n          this.hasCustomerOdds = false;\n          this.total = 0;\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error('加载玩家赔率失败:', error);\n        this.$message.error('加载玩家赔率失败');\n        this.loading = false;\n      });\n    },\n    /** 获取玩家名称 */\n    getCustomerName(customerId) {\n      const customer = this.customerList.find(c => c.userId === customerId);\n      return customer ? customer.name : '未知玩家';\n    },\n    /** 获取赔率样式类 */\n    getOddsClass(row) {\n      if (!this.selectedCustomerId) return 'normal';\n      return row.isCustomerOdds ? 'custom' : 'default';\n    },\n    /** 计算赔率差异 */\n    getOddsDiff(row) {\n      if (!this.selectedCustomerId || !row.defaultOdds) return '-';\n      const diff = (parseFloat(row.odds) - parseFloat(row.defaultOdds)).toFixed(2);\n      return diff > 0 ? `+${diff}` : diff;\n    },\n    /** 获取赔率差异样式类 */\n    getOddsDiffClass(row) {\n      const diff = parseFloat(this.getOddsDiff(row));\n      if (diff > 0) return 'positive';\n      if (diff < 0) return 'negative';\n      return 'neutral';\n    },\n    /** 修改玩家赔率 */\n    handleUpdateCustomerOdds(row) {\n      // 打开编辑对话框，设置为玩家赔率编辑模式\n      this.reset();\n      this.form = { ...row };\n      this.form.isCustomerEdit = true;\n      this.form.customerId = this.selectedCustomerId;\n      this.title = `修改玩家赔率 - ${this.getCustomerName(this.selectedCustomerId)}`;\n      this.open = true;\n    },\n    /** 重置单个玩家赔率 */\n    handleResetSingleOdds(row) {\n      this.$confirm(`确认将玩家\"${this.getCustomerName(this.selectedCustomerId)}\"的\"${row.methodName}\"赔率重置为默认值吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const updateData = [{\n          oddsId: row.oddsId,\n          odds: row.defaultOdds,\n          customerId: this.selectedCustomerId,\n          methodId: row.methodId\n        }];\n\n        updateCustomerOdds(updateData).then(response => {\n          this.$message.success('重置成功');\n          this.loadCustomerOdds(this.selectedCustomerId);\n        }).catch(error => {\n          this.$message.error('重置失败');\n        });\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        oddsId: null,\n        methodId: null,\n        methodName: null,\n        odds: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.oddsId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加赔率管理\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const oddsId = row.oddsId || this.ids\n      getOdds(oddsId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改赔率管理\";\n      });\n    },\n    /** 玩法选择改变 */\n    handleMethodChange(methodId) {\n      const method = this.methodList.find(item => item.methodId === methodId);\n      if (method) {\n        this.form.methodName = method.methodName;\n      }\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 判断是否为玩家赔率编辑\n          if (this.form.isCustomerEdit && this.form.customerId) {\n            // 玩家赔率编辑\n            const updateData = [{\n              oddsId: this.form.oddsId || null, // 如果没有oddsId则为null，后端会创建新记录\n              odds: this.form.odds,\n              customerId: this.form.customerId,\n              methodId: this.form.methodId,\n              sysUserId: this.form.sysUserId,\n              methodName: this.form.methodName\n            }];\n\n            updateCustomerOdds(updateData).then(() => {\n              this.$modal.msgSuccess(\"修改玩家赔率成功\");\n              this.open = false;\n              this.loadCustomerOdds(this.form.customerId);\n            }).catch(error => {\n              console.error('修改玩家赔率失败:', error);\n              this.$modal.msgError(\"修改玩家赔率失败\");\n            });\n          } else {\n            // 默认赔率编辑\n            if (this.form.oddsId != null) {\n              updateOdds(this.form).then(() => {\n                this.$modal.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n              });\n            } else {\n              addOdds(this.form).then(() => {\n                this.$modal.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n              });\n            }\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const oddsIds = row.oddsId || this.ids;\n      this.$modal.confirm('是否确认删除赔率管理编号为\"' + oddsIds + '\"的数据项？').then(function() {\n        return delOdds(oddsIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('game/odds/export', {\n        ...this.queryParams\n      }, `odds_${new Date().getTime()}.xlsx`)\n    },\n    /** 格式化赔率显示 */\n    formatOdds(odds) {\n      if (!odds) return '0.00';\n      const num = parseFloat(odds);\n      return num.toFixed(2) + '倍';\n    },\n    /** 格式化收益显示 */\n    formatProfit(odds, amount = 1) {\n      if (!odds) return '￥0.00';\n      const num = parseFloat(odds);\n      const profit = (num * amount).toFixed(2);\n      return '￥' + profit;\n    },\n    // 计算收益率\n    calculateProfitRate(odds) {\n      if (!odds) return '0';\n      const rate = ((parseFloat(odds) - 1) * 100).toFixed(1);\n      return rate;\n    },\n    // 处理赔率输入变化\n    handleOddsChange(value) {\n      // 限制小数点后两位\n      if (value && value.toString().includes('.')) {\n        const parts = value.toString().split('.');\n        if (parts[1] && parts[1].length > 2) {\n          this.form.odds = parseFloat(value).toFixed(2);\n        }\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.odds-container {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 页面标题样式 */\n.page-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px 30px;\n  border-radius: 12px;\n  margin-bottom: 24px;\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.page-title i {\n  font-size: 28px;\n  margin-right: 12px;\n}\n\n.page-description {\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n/* 搜索区域样式 */\n.search-container {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.search-form-wrapper {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 20px;\n}\n\n.search-form {\n  flex: 1;\n  margin-bottom: 0;\n}\n\n.search-toolbar {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  flex-shrink: 0;\n}\n\n/* 工具栏样式 */\n.toolbar-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 16px 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 12px;\n}\n\n.action-btn {\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.action-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n/* 表格容器样式 */\n.table-container {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.odds-table {\n  border-radius: 8px;\n}\n\n/* 表格内容样式 */\n.method-name {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.odds-display {\n  text-align: center;\n}\n\n.odds-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #e6a23c;\n  background: linear-gradient(45deg, #f39c12, #e67e22);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  padding: 4px 12px;\n  border-radius: 20px;\n  background-color: #fef7e6;\n  border: 1px solid #f5dab1;\n  display: inline-block;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n  justify-content: center;\n}\n\n.action-buttons .el-button {\n  margin: 0;\n}\n\n.action-btn-mini {\n  border-radius: 16px;\n  padding: 6px 12px;\n  font-size: 12px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  border: none;\n  min-width: 50px;\n  height: 28px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-btn-mini:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n\n.edit-btn {\n  background: #409EFF;\n  color: white;\n}\n\n.edit-btn:hover {\n  background: #66b1ff;\n}\n\n.delete-btn {\n  background: #F56C6C;\n  color: white;\n}\n\n.delete-btn:hover {\n  background: #f78989;\n}\n\n/* 现代化弹窗样式 */\n.modern-odds-dialog {\n  .el-dialog {\n    border-radius: 16px;\n    overflow: hidden;\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\n  }\n\n  .el-dialog__header {\n    padding: 0;\n    border-bottom: none;\n  }\n\n  .el-dialog__body {\n    padding: 0;\n  }\n\n  .el-dialog__footer {\n    padding: 0;\n    border-top: none;\n  }\n}\n\n/* 自定义头部 */\n.dialog-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.header-icon {\n  width: 48px;\n  height: 48px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n}\n\n.header-text h3 {\n  margin: 0 0 4px 0;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.header-text p {\n  margin: 0;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.close-btn {\n  color: white !important;\n  font-size: 20px;\n  padding: 8px;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 8px;\n  }\n}\n\n/* 内容区域 */\n.modern-dialog-content {\n  padding: 20px;\n  background: #fafbfc;\n}\n\n.modern-odds-form {\n  .el-form-item {\n    margin-bottom: 0;\n  }\n}\n\n/* 表单卡片 */\n.form-card {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  margin-bottom: 16px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\n  }\n}\n\n.card-header {\n  background: #f8f9fa;\n  padding: 12px 16px;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: #495057;\n  font-size: 14px;\n\n  i {\n    color: #667eea;\n    font-size: 16px;\n  }\n}\n\n.method-count {\n  margin-left: auto;\n  font-size: 12px;\n  color: #909399;\n  background: #e9ecef;\n  padding: 2px 8px;\n  border-radius: 12px;\n}\n\n.card-content {\n  padding: 16px;\n}\n\n/* 玩法选择区域样式 */\n.method-select-wrapper {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.select-label {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 13px;\n  color: #606266;\n  font-weight: 500;\n\n  i {\n    color: #667eea;\n    font-size: 14px;\n  }\n}\n\n.full-width {\n  width: 100%;\n}\n\n.method-tips {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n  margin-top: 8px;\n}\n\n.tip-row {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 12px;\n  color: #909399;\n\n  i {\n    color: #667eea;\n    font-size: 12px;\n  }\n}\n\n/* 现代化选择器 */\n.modern-select {\n  .el-input__inner {\n    height: 44px;\n    border: 2px solid #e9ecef;\n    border-radius: 8px;\n    font-size: 14px;\n    transition: all 0.3s ease;\n    padding-left: 12px;\n    padding-right: 30px;\n\n    &:focus {\n      border-color: #667eea;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n    }\n\n    &::placeholder {\n      color: #c0c4cc;\n      font-size: 13px;\n    }\n  }\n\n  .el-input__suffix {\n    right: 8px;\n  }\n}\n\n.method-option {\n  padding: 12px 16px;\n  height: auto;\n  line-height: 1.5;\n}\n\n.option-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  min-height: 24px;\n}\n\n.option-name {\n  font-weight: 500;\n  color: #2c3e50;\n  flex: 1;\n  white-space: normal;\n  word-wrap: break-word;\n  margin-right: 8px;\n}\n\n.option-id {\n  color: #8492a6;\n  font-size: 12px;\n  background: #f8f9fa;\n  padding: 2px 8px;\n  border-radius: 4px;\n  white-space: nowrap;\n  flex-shrink: 0;\n}\n\n/* 现代化输入框 */\n.odds-input-wrapper {\n  margin-bottom: 12px;\n}\n\n.modern-input {\n  .el-input__inner {\n    height: 40px;\n    border: 2px solid #e9ecef;\n    border-radius: 8px;\n    font-size: 14px;\n    transition: all 0.3s ease;\n\n    &:focus {\n      border-color: #667eea;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n    }\n  }\n\n  .el-input-group__prepend {\n    background: #667eea;\n    color: white;\n    border: none;\n    border-radius: 8px 0 0 8px;\n\n    i {\n      margin-right: 4px;\n    }\n  }\n\n  .el-input-group__append {\n    background: #f8f9fa;\n    color: #495057;\n    border: 2px solid #e9ecef;\n    border-left: none;\n    border-radius: 0 8px 8px 0;\n  }\n}\n\n/* 输入提示 */\n.input-tips {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.tip-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: #6c757d;\n\n  i {\n    color: #667eea;\n  }\n}\n\n/* 预览卡片 */\n.preview-card {\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);\n  border: 2px solid #667eea;\n}\n\n.preview-header {\n  background: rgba(102, 126, 234, 0.1);\n  color: #667eea;\n\n  .el-tag {\n    margin-left: auto;\n  }\n}\n\n.preview-grid {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  margin-bottom: 12px;\n}\n\n.preview-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.preview-icon {\n  width: 32px;\n  height: 32px;\n  background: #e3f2fd;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #1976d2;\n  font-size: 16px;\n}\n\n.profit-icon {\n  background: #e8f5e8;\n  color: #4caf50;\n}\n\n.preview-arrow {\n  color: #667eea;\n  font-size: 20px;\n  font-weight: bold;\n}\n\n.preview-info {\n  flex: 1;\n}\n\n.preview-label {\n  font-size: 12px;\n  color: #6c757d;\n  margin-bottom: 4px;\n}\n\n.preview-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.preview-value.profit {\n  color: #4caf50;\n  font-size: 18px;\n}\n\n.profit-rate {\n  text-align: center;\n  padding: 12px;\n  background: white;\n  border-radius: 8px;\n  font-size: 14px;\n  color: #495057;\n}\n\n.rate-value {\n  font-weight: 600;\n  color: #4caf50;\n  font-size: 16px;\n}\n\n/* 底部按钮 */\n.modern-dialog-footer {\n  padding: 16px 20px;\n  background: #f8f9fa;\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n\n.cancel-btn {\n  height: 36px;\n  padding: 0 20px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: #adb5bd;\n    background: #f8f9fa;\n  }\n}\n\n.submit-btn {\n  height: 36px;\n  padding: 0 24px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .odds-container {\n    padding: 12px;\n  }\n\n  .toolbar-container {\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .toolbar-left {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n    gap: 4px;\n  }\n\n  .action-buttons .el-button {\n    margin: 0 4px 4px 0;\n  }\n\n  .action-buttons .el-button:last-child {\n    margin-right: 0;\n  }\n\n  .modern-dialog-content {\n    padding: 20px;\n  }\n\n  .preview-grid {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .preview-arrow {\n    transform: rotate(90deg);\n  }\n\n  .modern-dialog-footer {\n    padding: 20px;\n    flex-direction: column;\n  }\n\n  .cancel-btn,\n  .submit-btn {\n    width: 100%;\n  }\n}\n\n/* 玩家选择区域样式 */\n.player-selection-container {\n  margin-bottom: 20px;\n}\n\n.player-selection-card {\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  background: #fafbfc;\n}\n\n.selection-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.selection-header i {\n  margin-right: 8px;\n  color: #409eff;\n  font-size: 16px;\n}\n\n.selection-title {\n  font-size: 16px;\n}\n\n.player-radio-group {\n  margin-bottom: 16px;\n}\n\n.player-radio-group .el-radio-group {\n  display: inline-block;\n  font-size: 0; /* 消除inline-block元素间的空白 */\n  line-height: 0;\n}\n\n.player-radio-group .el-radio-group > * {\n  font-size: 14px; /* 恢复字体大小 */\n  line-height: normal;\n}\n\n.player-radio-group .el-radio-button {\n  margin: 0 !important;\n}\n\n/* 移除了margin-right设置，让按钮完全贴合 */\n\n/* 强制移除Element UI默认的按钮间距 */\n.player-radio-group .el-radio-button {\n  margin: 0 !important;\n  display: inline-block !important;\n  vertical-align: top !important;\n}\n\n.player-radio-group .el-radio-button:not(:first-child) {\n  margin-left: -1px !important;\n}\n\n.player-radio-group .el-radio-button:not(:first-child) .el-radio-button__inner {\n  border-left: 0 !important;\n}\n\n.player-radio-group .el-radio-button__inner {\n  border-radius: 0 !important;\n  margin: 0 !important;\n  display: inline-block !important;\n}\n\n.player-radio-group .el-radio-button:first-child .el-radio-button__inner {\n  border-radius: 4px 0 0 4px !important;\n}\n\n.player-radio-group .el-radio-button:last-child .el-radio-button__inner {\n  border-radius: 0 4px 4px 0 !important;\n}\n\n.all-users-btn .el-radio-button__inner {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-color: #667eea;\n  color: white;\n  font-weight: 500;\n  margin: 0;\n  border-radius: 4px 0 0 4px;\n}\n\n.all-users-btn .el-radio-button__inner i {\n  margin-right: 6px;\n}\n\n.all-users-btn.is-active .el-radio-button__inner {\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n  border-color: #5a6fd8;\n  box-shadow: 0 2px 8px rgba(90, 111, 216, 0.3);\n}\n\n.customer-btn .el-radio-button__inner {\n  background: #f8f9fa;\n  border-color: #e9ecef;\n  color: #495057;\n  margin: 0;\n  border-radius: 0;\n}\n\n.customer-btn:last-child .el-radio-button__inner {\n  border-radius: 0 4px 4px 0;\n}\n\n.customer-btn .el-radio-button__inner i {\n  margin-right: 6px;\n  color: #67c23a;\n}\n\n.customer-btn.is-active .el-radio-button__inner {\n  background: #67c23a;\n  border-color: #67c23a;\n  color: white;\n  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);\n}\n\n.selection-status {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.selection-status .el-tag {\n  font-size: 12px;\n}\n\n.selection-status .el-tag i {\n  margin-right: 4px;\n}\n\n.no-odds-tip {\n  color: #e6a23c;\n  font-size: 12px;\n}\n\n.no-odds-tip i {\n  margin-right: 4px;\n}\n\n/* 赔率对比样式 */\n.odds-value.normal {\n  color: #303133;\n  font-weight: 600;\n}\n\n.odds-value.custom {\n  color: #67c23a;\n  font-weight: 600;\n}\n\n.odds-value.default {\n  color: #909399;\n  font-weight: 600;\n}\n\n.default-odds .odds-value {\n  color: #909399;\n  font-weight: 500;\n}\n\n.odds-diff {\n  font-weight: 600;\n  font-size: 12px;\n}\n\n.odds-diff.positive {\n  color: #f56c6c;\n}\n\n.odds-diff.negative {\n  color: #67c23a;\n}\n\n.odds-diff.neutral {\n  color: #909399;\n}\n\n/* 玩家名称列样式 */\n.customer-name {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.customer-name-text {\n  color: #67c23a;\n  font-weight: 500;\n}\n\n.customer-name-text i {\n  margin-right: 6px;\n  color: #67c23a;\n}\n\n.system-default {\n  color: #909399;\n  font-weight: 500;\n}\n\n.system-default i {\n  margin-right: 6px;\n  color: #409eff;\n}\n\n/* 全局下拉框样式 */\n.method-select-dropdown {\n  .el-select-dropdown__item {\n    height: auto !important;\n    line-height: 1.5 !important;\n    padding: 12px 16px !important;\n    white-space: normal !important;\n    word-wrap: break-word !important;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAgaA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,YAAA;MACA;MACAC,kBAAA;MACA;MACAC,eAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,QAAA;MACAC,gBAAA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,IAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,IAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,aAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,mBAAA;IACA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,YAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,YAAA;IACA;IACA,SAAApB,WAAA;MACAuB,YAAA,MAAAvB,WAAA;MACA,KAAAA,WAAA;IACA;EACA;EACAwB,OAAA;IACA,eACAP,mBAAA,WAAAA,oBAAA;MACA,WAAAC,MAAA;QACA,KAAApB,QAAA,GAAAoB,MAAA,CAAAO,UAAA;MACA;IACA;IACA,eACAL,YAAA,WAAAA,aAAA;MAAA,IAAAM,KAAA;MACA;MACA,SAAA1B,WAAA;QACAuB,YAAA,MAAAvB,WAAA;MACA;MACA,KAAAA,WAAA,GAAA2B,UAAA;QACA,WAAAT,MAAA;UACAQ,KAAA,CAAA5B,QAAA,GAAAoB,MAAA,CAAAO,UAAA;QACA;MACA;IACA;IACA,gBACAG,kBAAA,WAAAA,mBAAA;MACA,KAAA7B,gBAAA,SAAAA,gBAAA;IACA;IACA,kBACA8B,gBAAA,WAAAA,iBAAAC,IAAA,EAAAC,KAAA;MACA,KAAAC,IAAA,CAAAF,IAAA,qBAAAA,IAAA,CAAAG,cAAA;IACA;IACA,eACApB,OAAA,WAAAA,QAAA;MAAA,IAAAqB,MAAA;MACA,KAAAjD,OAAA;MACA,IAAAkD,cAAA,OAAAlC,WAAA,EAAAmC,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAA3C,QAAA,GAAA8C,QAAA,CAAAC,IAAA;QACAJ,MAAA,CAAA5C,KAAA,GAAA+C,QAAA,CAAA/C,KAAA;QACA4C,MAAA,CAAAjD,OAAA;MACA;IACA;IACA,aACA6B,aAAA,WAAAA,cAAA;MAAA,IAAAyB,MAAA;MACA;MACA,IAAAC,MAAA;QACAtC,OAAA;QACAC,QAAA;MACA;MACA,IAAAsC,kBAAA,EAAAD,MAAA,EAAAJ,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAA/C,UAAA,GAAA6C,QAAA,CAAAC,IAAA;MACA;IACA;IACA,aACAvB,eAAA,WAAAA,gBAAA;MAAA,IAAA2B,MAAA;MACA,IAAAF,MAAA;QACAtC,OAAA;QACAC,QAAA;MACA;MACA,IAAAwC,sBAAA,EAAAH,MAAA,EAAAJ,IAAA,WAAAC,QAAA;QACAK,MAAA,CAAAjD,YAAA,GAAA4C,QAAA,CAAAC,IAAA;MACA,GAAAM,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAH,MAAA,CAAAjD,YAAA;MACA;IACA;IACA,aACAsD,oBAAA,WAAAA,qBAAAC,UAAA;MACA,KAAAtD,kBAAA,GAAAsD,UAAA;MACA,IAAAA,UAAA;QACA;QACA,KAAAC,gBAAA,CAAAD,UAAA;MACA;QACA;QACA,KAAAnC,OAAA;MACA;IACA;IACA,eACAoC,gBAAA,WAAAA,iBAAAD,UAAA;MAAA,IAAAE,MAAA;MACA,KAAAjE,OAAA;MACA,IAAAkE,qBAAA,EAAAH,UAAA,EAAAZ,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAe,IAAA,YAAAf,QAAA,CAAArD,IAAA;UACAkE,MAAA,CAAA3D,QAAA,GAAA8C,QAAA,CAAArD,IAAA;UACAkE,MAAA,CAAAvD,eAAA,GAAA0C,QAAA,CAAArD,IAAA,CAAAqE,IAAA,WAAAvB,IAAA;YAAA,OAAAA,IAAA,CAAAwB,cAAA;UAAA;UACAJ,MAAA,CAAA5D,KAAA,GAAA+C,QAAA,CAAArD,IAAA,CAAAuE,MAAA;QACA;UACAL,MAAA,CAAA3D,QAAA;UACA2D,MAAA,CAAAvD,eAAA;UACAuD,MAAA,CAAA5D,KAAA;QACA;QACA4D,MAAA,CAAAjE,OAAA;MACA,GAAA2D,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAK,MAAA,CAAAM,QAAA,CAAAX,KAAA;QACAK,MAAA,CAAAjE,OAAA;MACA;IACA;IACA,aACAwE,eAAA,WAAAA,gBAAAT,UAAA;MACA,IAAAU,QAAA,QAAAjE,YAAA,CAAAkE,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,MAAA,KAAAb,UAAA;MAAA;MACA,OAAAU,QAAA,GAAAA,QAAA,CAAA3E,IAAA;IACA;IACA,cACA+E,YAAA,WAAAA,aAAAC,GAAA;MACA,UAAArE,kBAAA;MACA,OAAAqE,GAAA,CAAAT,cAAA;IACA;IACA,aACAU,WAAA,WAAAA,YAAAD,GAAA;MACA,UAAArE,kBAAA,KAAAqE,GAAA,CAAAE,WAAA;MACA,IAAAC,IAAA,IAAAC,UAAA,CAAAJ,GAAA,CAAA1D,IAAA,IAAA8D,UAAA,CAAAJ,GAAA,CAAAE,WAAA,GAAAG,OAAA;MACA,OAAAF,IAAA,WAAAG,MAAA,CAAAH,IAAA,IAAAA,IAAA;IACA;IACA,gBACAI,gBAAA,WAAAA,iBAAAP,GAAA;MACA,IAAAG,IAAA,GAAAC,UAAA,MAAAH,WAAA,CAAAD,GAAA;MACA,IAAAG,IAAA;MACA,IAAAA,IAAA;MACA;IACA;IACA,aACAK,wBAAA,WAAAA,yBAAAR,GAAA;MACA;MACA,KAAAS,KAAA;MACA,KAAAlE,IAAA,OAAAmE,cAAA,CAAAC,OAAA,MAAAX,GAAA;MACA,KAAAzD,IAAA,CAAAqE,cAAA;MACA,KAAArE,IAAA,CAAA0C,UAAA,QAAAtD,kBAAA;MACA,KAAAE,KAAA,6CAAAyE,MAAA,MAAAZ,eAAA,MAAA/D,kBAAA;MACA,KAAAG,IAAA;IACA;IACA,eACA+E,qBAAA,WAAAA,sBAAAb,GAAA;MAAA,IAAAc,MAAA;MACA,KAAAC,QAAA,oCAAAT,MAAA,MAAAZ,eAAA,MAAA/D,kBAAA,iBAAA2E,MAAA,CAAAN,GAAA,CAAA3D,UAAA;QACA2E,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA7C,IAAA;QACA,IAAA8C,UAAA;UACAC,MAAA,EAAApB,GAAA,CAAAoB,MAAA;UACA9E,IAAA,EAAA0D,GAAA,CAAAE,WAAA;UACAjB,UAAA,EAAA6B,MAAA,CAAAnF,kBAAA;UACAc,QAAA,EAAAuD,GAAA,CAAAvD;QACA;QAEA,IAAA4E,wBAAA,EAAAF,UAAA,EAAA9C,IAAA,WAAAC,QAAA;UACAwC,MAAA,CAAArB,QAAA,CAAA6B,OAAA;UACAR,MAAA,CAAA5B,gBAAA,CAAA4B,MAAA,CAAAnF,kBAAA;QACA,GAAAkD,KAAA,WAAAC,KAAA;UACAgC,MAAA,CAAArB,QAAA,CAAAX,KAAA;QACA;MACA;IACA;IACA;IACAyC,MAAA,WAAAA,OAAA;MACA,KAAAzF,IAAA;MACA,KAAA2E,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAlE,IAAA;QACA6E,MAAA;QACA3E,QAAA;QACAJ,UAAA;QACAC,IAAA;MACA;MACA,KAAAkF,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAvF,WAAA,CAAAC,OAAA;MACA,KAAAW,OAAA;IACA;IACA,aACA4E,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzG,GAAA,GAAAyG,SAAA,CAAAC,GAAA,WAAA9D,IAAA;QAAA,OAAAA,IAAA,CAAAqD,MAAA;MAAA;MACA,KAAAhG,MAAA,GAAAwG,SAAA,CAAApC,MAAA;MACA,KAAAnE,QAAA,IAAAuG,SAAA,CAAApC,MAAA;IACA;IACA,aACAsC,SAAA,WAAAA,UAAA;MACA,KAAArB,KAAA;MACA,KAAA3E,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAkG,YAAA,WAAAA,aAAA/B,GAAA;MAAA,IAAAgC,MAAA;MACA,KAAAvB,KAAA;MACA,IAAAW,MAAA,GAAApB,GAAA,CAAAoB,MAAA,SAAAjG,GAAA;MACA,IAAA8G,aAAA,EAAAb,MAAA,EAAA/C,IAAA,WAAAC,QAAA;QACA0D,MAAA,CAAAzF,IAAA,GAAA+B,QAAA,CAAArD,IAAA;QACA+G,MAAA,CAAAlG,IAAA;QACAkG,MAAA,CAAAnG,KAAA;MACA;IACA;IACA,aACAqG,kBAAA,WAAAA,mBAAAzF,QAAA;MACA,IAAA0F,MAAA,QAAA1G,UAAA,CAAAmE,IAAA,WAAA7B,IAAA;QAAA,OAAAA,IAAA,CAAAtB,QAAA,KAAAA,QAAA;MAAA;MACA,IAAA0F,MAAA;QACA,KAAA5F,IAAA,CAAAF,UAAA,GAAA8F,MAAA,CAAA9F,UAAA;MACA;IACA;IACA,WACA+F,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAH,MAAA,CAAA9F,IAAA,CAAAqE,cAAA,IAAAyB,MAAA,CAAA9F,IAAA,CAAA0C,UAAA;YACA;YACA,IAAAkC,UAAA;cACAC,MAAA,EAAAiB,MAAA,CAAA9F,IAAA,CAAA6E,MAAA;cAAA;cACA9E,IAAA,EAAA+F,MAAA,CAAA9F,IAAA,CAAAD,IAAA;cACA2C,UAAA,EAAAoD,MAAA,CAAA9F,IAAA,CAAA0C,UAAA;cACAxC,QAAA,EAAA4F,MAAA,CAAA9F,IAAA,CAAAE,QAAA;cACAgG,SAAA,EAAAJ,MAAA,CAAA9F,IAAA,CAAAkG,SAAA;cACApG,UAAA,EAAAgG,MAAA,CAAA9F,IAAA,CAAAF;YACA;YAEA,IAAAgF,wBAAA,EAAAF,UAAA,EAAA9C,IAAA;cACAgE,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAvG,IAAA;cACAuG,MAAA,CAAAnD,gBAAA,CAAAmD,MAAA,CAAA9F,IAAA,CAAA0C,UAAA;YACA,GAAAJ,KAAA,WAAAC,KAAA;cACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;cACAuD,MAAA,CAAAK,MAAA,CAAAE,QAAA;YACA;UACA;YACA;YACA,IAAAP,MAAA,CAAA9F,IAAA,CAAA6E,MAAA;cACA,IAAAyB,gBAAA,EAAAR,MAAA,CAAA9F,IAAA,EAAA8B,IAAA;gBACAgE,MAAA,CAAAK,MAAA,CAAAC,UAAA;gBACAN,MAAA,CAAAvG,IAAA;gBACAuG,MAAA,CAAAvF,OAAA;cACA;YACA;cACA,IAAAgG,aAAA,EAAAT,MAAA,CAAA9F,IAAA,EAAA8B,IAAA;gBACAgE,MAAA,CAAAK,MAAA,CAAAC,UAAA;gBACAN,MAAA,CAAAvG,IAAA;gBACAuG,MAAA,CAAAvF,OAAA;cACA;YACA;UACA;QACA;MACA;IACA;IACA,aACAiG,YAAA,WAAAA,aAAA/C,GAAA;MAAA,IAAAgD,MAAA;MACA,IAAAC,OAAA,GAAAjD,GAAA,CAAAoB,MAAA,SAAAjG,GAAA;MACA,KAAAuH,MAAA,CAAAQ,OAAA,oBAAAD,OAAA,aAAA5E,IAAA;QACA,WAAA8E,aAAA,EAAAF,OAAA;MACA,GAAA5E,IAAA;QACA2E,MAAA,CAAAlG,OAAA;QACAkG,MAAA,CAAAN,MAAA,CAAAC,UAAA;MACA,GAAA9D,KAAA;IACA;IACA,aACAuE,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,yBAAA3C,cAAA,CAAAC,OAAA,MACA,KAAAzE,WAAA,WAAAoE,MAAA,CACA,IAAAgD,IAAA,GAAAC,OAAA;IACA;IACA,cACAC,UAAA,WAAAA,WAAAlH,IAAA;MACA,KAAAA,IAAA;MACA,IAAAmH,GAAA,GAAArD,UAAA,CAAA9D,IAAA;MACA,OAAAmH,GAAA,CAAApD,OAAA;IACA;IACA,cACAqD,YAAA,WAAAA,aAAApH,IAAA;MAAA,IAAAqH,MAAA,GAAAC,SAAA,CAAApE,MAAA,QAAAoE,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,KAAAtH,IAAA;MACA,IAAAmH,GAAA,GAAArD,UAAA,CAAA9D,IAAA;MACA,IAAAwH,MAAA,IAAAL,GAAA,GAAAE,MAAA,EAAAtD,OAAA;MACA,aAAAyD,MAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAzH,IAAA;MACA,KAAAA,IAAA;MACA,IAAA0H,IAAA,KAAA5D,UAAA,CAAA9D,IAAA,cAAA+D,OAAA;MACA,OAAA2D,IAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAC,QAAA,GAAAC,QAAA;QACA,IAAAC,KAAA,GAAAH,KAAA,CAAAC,QAAA,GAAAG,KAAA;QACA,IAAAD,KAAA,OAAAA,KAAA,IAAA7E,MAAA;UACA,KAAAjD,IAAA,CAAAD,IAAA,GAAA8D,UAAA,CAAA8D,KAAA,EAAA7D,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}