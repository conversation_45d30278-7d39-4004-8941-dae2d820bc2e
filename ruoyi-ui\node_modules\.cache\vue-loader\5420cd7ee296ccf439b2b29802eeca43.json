{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\odds\\index.vue?vue&type=template&id=233d353a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\odds\\index.vue", "mtime": 1758866059933}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750942930085}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}