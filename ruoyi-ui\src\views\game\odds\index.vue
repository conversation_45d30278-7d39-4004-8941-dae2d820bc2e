<template>
  <div class="app-container odds-container">
   
 
    <!-- 移动端搜索切换按钮 -->
    <div v-if="isMobile" class="mobile-search-toggle">
      <el-button
        @click="toggleMobileSearch"
        class="mobile-search-btn"
        :type="showMobileSearch ? 'primary' : 'info'"
        icon="el-icon-search"
      >
        {{ showMobileSearch ? '收起搜索' : '展开搜索' }}
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-container" v-show="showSearch && (!isMobile || showMobileSearch)">
      <div class="search-form-wrapper" :class="{ 'mobile-search-form': isMobile }">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="!isMobile" label-width="80px" class="search-form">
          <el-form-item label="玩法名称" prop="methodName">
            <el-input
              v-model="queryParams.methodName"
              placeholder="请输入玩法名称"
              clearable
              prefix-icon="el-icon-search"
              @keyup.enter.native="handleQuery"
              :style="isMobile ? 'width: 100%;' : 'width: 200px;'"
            />
          </el-form-item>
          <el-form-item label="赔率范围" prop="odds">
            <el-input
              v-model="queryParams.odds"
              placeholder="请输入赔率"
              clearable
              prefix-icon="el-icon-money"
              @keyup.enter.native="handleQuery"
              :style="isMobile ? 'width: 100%;' : 'width: 200px;'"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="search-toolbar" :class="{ 'mobile-toolbar': isMobile }">
          <el-button
            type="info"
            icon="el-icon-download"
            size="small"
            @click="handleExport"
            v-hasPermi="['game:odds:export']"
            class="action-btn"
          >导出数据</el-button>
          <right-toolbar v-if="!isMobile" :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </div>
      </div>
    </div>

    <!-- 玩家选择区域 -->
    <div class="player-selection-container">
      <el-card shadow="never" class="player-selection-card">
        <div class="selection-header">
          <i class="el-icon-user"></i>
          <span class="selection-title">选择玩家：</span>
        </div>

        <!-- PC端：单选按钮组 -->
        <div v-if="!isMobile" class="player-radio-group">
          <el-radio-group v-model="selectedCustomerId" @change="handleCustomerChange" size="small">
            <el-radio-button :label="null" class="all-users-btn">
              <i class="el-icon-s-home"></i>
              全部玩家
            </el-radio-button>
            <el-radio-button
              v-for="customer in customerList"
              :key="customer.userId"
              :label="customer.userId"
              class="customer-btn"
            >
              <i class="el-icon-user-solid"></i>
              {{ customer.name }}
            </el-radio-button>
          </el-radio-group>
        </div>

        <!-- 移动端：下拉选择器 -->
        <div v-else class="mobile-player-select">
          <el-select
            v-model="selectedCustomerId"
            @change="handleCustomerChange"
            placeholder="请选择玩家"
            class="mobile-select"
            clearable
          >
            <el-option :value="null" label="全部玩家">
              <i class="el-icon-s-home"></i>
              全部玩家
            </el-option>
            <el-option
              v-for="customer in customerList"
              :key="customer.userId"
              :value="customer.userId"
              :label="customer.name"
            >
              <i class="el-icon-user-solid"></i>
              {{ customer.name }}
            </el-option>
          </el-select>
        </div>

        <div class="selection-status">
          <el-tag v-if="!selectedCustomerId" type="info" effect="dark" :style="isMobile ? 'font-size: 12px;' : 'font-size: 16px;'" size="medium">
            <i class="el-icon-info"></i>
            当前显示：默认赔率模板{{ isMobile ? '' : '，如需修改或恢复默认，请选择具体玩家！' }}
          </el-tag>
          <el-tag v-else type="success" size="medium" effect="dark" :style="isMobile ? 'font-size: 12px;' : 'font-size: 16px;'">
            <i class="el-icon-user-solid"></i>
            当前显示：{{ getCustomerName(selectedCustomerId) }} 的专属赔率
          </el-tag>
          <span v-if="selectedCustomerId && !hasCustomerOdds" class="no-odds-tip">
            <i class="el-icon-warning"></i>
            该玩家暂无特殊赔率！
          </span>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-container" :class="{ 'mobile-table-container': isMobile }">
      <!-- PC端表格 -->
      <el-table
        v-if="!isMobile"
        v-loading="loading"
        :data="oddsList"
        @selection-change="handleSelectionChange"
        class="odds-table"
        stripe
        border
        :header-cell-style="{ background: '#f8f9fa', color: '#606266', fontWeight: 'bold' }"
      >
        <el-table-column type="selection" width="55" align="center" />

        <el-table-column label="玩法名称" align="center" prop="methodName" min-width="200">
          <template slot-scope="scope">
            <div class="method-name">
              <i class="el-icon-trophy" style="color: #409EFF; margin-right: 8px;"></i>
              <span style="font-weight: 500;font-size: 16px;">{{ scope.row.methodName }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="玩家名称" align="center" min-width="120">
          <template slot-scope="scope">
            <div class="customer-name">
              <span v-if="scope.row.customerName" class="customer-name-text">
                <i class="el-icon-user-solid"></i>
                {{ scope.row.customerName }}
              </span>
              <span v-else class="system-default">
                <i class="el-icon-s-home"></i>
                系统默认
              </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="默认赔率" align="center" min-width="120" v-if="selectedCustomerId">
          <template slot-scope="scope">
            <div class="default-odds">
              <span class="odds-value default">{{ formatOdds(scope.row.defaultOdds || scope.row.odds) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column :label="selectedCustomerId ? '当前赔率' : '赔率'" align="center" prop="odds" min-width="150">
          <template slot-scope="scope">
            <div class="odds-display">
              <span :class="['odds-value', getOddsClass(scope.row)]">{{ formatOdds(scope.row.odds) }}</span>
              <el-tag
                v-if="selectedCustomerId && scope.row.isCustomerOdds"
                size="mini"
                type="success"
                style="margin-left: 8px;"
              >
                专属
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="赔率差异" align="center" min-width="100" v-if="selectedCustomerId">
          <template slot-scope="scope">
            <span :class="['odds-diff', getOddsDiffClass(scope.row)]">
              {{ getOddsDiff(scope.row) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="200">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button
                v-if="!selectedCustomerId"
                size="mini"
                type="primary"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['game:odds:edit']"
                class="action-btn-mini edit-btn"
              >修改</el-button>
              <el-button
                v-if="selectedCustomerId"
                size="mini"
                type="primary"
                @click="handleUpdateCustomerOdds(scope.row)"
                v-hasPermi="['game:odds:edit']"
                class="action-btn-mini edit-btn"
              >修改</el-button>
              <el-button
                v-if="selectedCustomerId && scope.row.isCustomerOdds"
                size="mini"
                type="warning"
                @click="handleResetSingleOdds(scope.row)"
                v-hasPermi="['game:odds:edit']"
                class="action-btn-mini reset-btn"
              >重置</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 移动端卡片布局 -->
      <div v-else class="mobile-card-container">
        <div v-for="(item, index) in oddsList" :key="item.oddsId" class="mobile-card" @click="toggleMobileCard(item, index)">
          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="card-header-left">
              <div class="odds-info">
                <div class="method-name">
                  <i class="el-icon-trophy"></i>
                  {{ item.methodName }}
                </div>
                <div class="odds-meta">
                  <span class="customer-info">
                    <i v-if="item.customerName" class="el-icon-user-solid"></i>
                    <i v-else class="el-icon-s-home"></i>
                    {{ item.customerName || '系统默认' }}
                  </span>
                  <span class="odds-value" :class="getOddsClass(item)">
                    {{ formatOdds(item.odds) }}
                    <el-tag v-if="selectedCustomerId && item.isCustomerOdds" size="mini" type="success">专属</el-tag>
                  </span>
                </div>
              </div>
            </div>
            <div class="card-header-right">
              <i class="el-icon-arrow-down" :class="{ 'expanded': item.mobileExpanded }"></i>
            </div>
          </div>

          <!-- 卡片内容（展开时显示） -->
          <div v-if="item.mobileExpanded" class="card-content">
            <div v-if="selectedCustomerId" class="detail-row">
              <span class="detail-label">默认赔率:</span>
              <span class="detail-value">{{ formatOdds(item.defaultOdds || item.odds) }}</span>
            </div>
            <div v-if="selectedCustomerId" class="detail-row">
              <span class="detail-label">赔率差异:</span>
              <span class="detail-value" :class="['odds-diff', getOddsDiffClass(item)]">{{ getOddsDiff(item) }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">操作:</span>
              <div class="detail-value">
                <div class="mobile-action-buttons">
                  <el-button
                    v-if="!selectedCustomerId"
                    size="mini"
                    type="primary"
                    @click.stop="handleUpdate(item)"
                    v-hasPermi="['game:odds:edit']"
                  >修改</el-button>
                  <el-button
                    v-if="selectedCustomerId"
                    size="mini"
                    type="primary"
                    @click.stop="handleUpdateCustomerOdds(item)"
                    v-hasPermi="['game:odds:edit']"
                  >修改</el-button>
                  <el-button
                    v-if="selectedCustomerId && item.isCustomerOdds"
                    size="mini"
                    type="warning"
                    @click.stop="handleResetSingleOdds(item)"
                    v-hasPermi="['game:odds:edit']"
                  >重置</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改赔率管理对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="580px"
      append-to-body
      class="modern-odds-dialog"
      :close-on-click-modal="false"
      :show-close="false"
      custom-class="beautiful-dialog"
    >
      <!-- 自定义头部 -->
      <div slot="title" class="dialog-header">
        <div class="header-content">
          <div class="header-icon">
            <i class="el-icon-s-data"></i>
          </div>
          <div class="header-text">
            <h3>{{ title }}</h3>
            <p>设置玩法赔率，管理投注收益比例</p>
          </div>
        </div>
        <el-button
          type="text"
          @click="cancel"
          class="close-btn"
          icon="el-icon-close"
        ></el-button>
      </div>

      <div class="modern-dialog-content">
        <el-form ref="form" :model="form" :rules="rules" label-width="0" class="modern-odds-form">
          <!-- 玩法选择卡片 -->
          <div class="form-card">
            <div class="card-header">
              <i class="el-icon-menu"></i>
              <span>选择玩法</span>
              <span class="method-count" v-if="methodList.length">共 {{ methodList.length }} 种玩法</span>
            </div>
            <div class="card-content">
              <div class="method-select-wrapper">
                <div class="select-label">
                  <i class="el-icon-s-data"></i>
                  <span>玩法类型</span>
                </div>
                <el-form-item prop="methodId">
                  <el-select
                    v-model="form.methodId"
                    placeholder="请选择要设置赔率的玩法"
                    @change="handleMethodChange"
                    class="modern-select full-width"
                    filterable
                    popper-class="method-select-dropdown"
                  >
                    <el-option
                      v-for="method in methodList"
                      :key="method.methodId"
                      :label="method.methodName"
                      :value="method.methodId"
                      class="method-option"
                    >
                      <div class="option-content">
                        <span class="option-name">{{ method.methodName }}</span>
                        <span class="option-id">ID: {{ method.methodId }}</span>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
                <div class="method-tips">
                  <div class="tip-row">
                    <i class="el-icon-info"></i>
                    <span>支持输入关键词快速搜索玩法</span>
                  </div>
                  <div class="tip-row">
                    <i class="el-icon-warning"></i>
                    <span>每种玩法只能设置一个赔率</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 赔率设置卡片 -->
          <div class="form-card">
            <div class="card-header">
              <i class="el-icon-money"></i>
              <span>设置赔率</span>
            </div>
            <div class="card-content">
              <el-form-item prop="odds">
                <div class="odds-input-wrapper">
                  <el-input
                    v-model="form.odds"
                    placeholder="请输入赔率倍数"
                    type="number"
                    step="0.01"
                    min="0.01"
                    max="999.99"
                    class="modern-input"
                    size="large"
                    @input="handleOddsChange"
                  >
                    <template slot="prepend">
                      <i class="el-icon-s-finance"></i>
                      倍率
                    </template>
                    <template slot="append">倍</template>
                  </el-input>
                </div>
                <div class="input-tips">
                  <div class="tip-item">
                    <i class="el-icon-info"></i>
                    <span>赔率范围：0.01 - 999.99</span>
                  </div>
                  <div class="tip-item">
                    <i class="el-icon-warning"></i>
                    <span>支持小数点后两位精度</span>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 预览区域卡片 -->
          <div class="form-card preview-card" v-if="form.odds && parseFloat(form.odds) > 0">
            <div class="card-header preview-header">
              <i class="el-icon-view"></i>
              <span>收益预览</span>
              <el-tag type="success" size="mini">实时计算</el-tag>
            </div>
            <div class="card-content">
              <div class="preview-grid">
                <div class="preview-item">
                  <div class="preview-icon">
                    <i class="el-icon-wallet"></i>
                  </div>
                  <div class="preview-info">
                    <div class="preview-label">投注金额</div>
                    <div class="preview-value">￥100.00</div>
                  </div>
                </div>
                <div class="preview-arrow">
                  <i class="el-icon-right"></i>
                </div>
                <div class="preview-item">
                  <div class="preview-icon profit-icon">
                    <i class="el-icon-trophy"></i>
                  </div>
                  <div class="preview-info">
                    <div class="preview-label">预期收益</div>
                    <div class="preview-value profit">{{ formatProfit(form.odds, 100) }}</div>
                  </div>
                </div>
              </div>
              <div class="profit-rate">
                <span>收益率：</span>
                <span class="rate-value">{{ calculateProfitRate(form.odds) }}%</span>
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <div slot="footer" class="modern-dialog-footer">
        <el-button @click="cancel" class="cancel-btn" size="large">
          <i class="el-icon-close"></i>
          取消
        </el-button>
        <el-button type="primary" @click="submitForm" class="submit-btn" size="large">
          <i class="el-icon-check"></i>
          {{ form.oddsId ? '更新赔率' : '创建赔率' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOdds, getOdds, delOdds, addOdds, updateOdds, getCustomerOdds, updateCustomerOdds, resetCustomerOdds } from "@/api/game/odds";
import { listMethod } from "@/api/game/method";
import { listCustomer } from "@/api/game/customer";

export default {
  name: "Odds",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 赔率管理表格数据
      oddsList: [],
      // 玩法列表
      methodList: [],
      // 玩家列表
      customerList: [],
      // 选中的玩家ID
      selectedCustomerId: null,
      // 是否有玩家专属赔率
      hasCustomerOdds: false,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 移动端相关
      isMobile: false,
      showMobileSearch: false,
      resizeTimer: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        methodName: null,
        odds: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        methodId: [
          { required: true, message: "玩法不能为空", trigger: "change" }
        ],
        odds: [
          { required: true, message: "赔率不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getMethodList();
    this.getCustomerList();
  },
  mounted() {
    // 初始化移动端检测
    this.initMobileDetection();
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize);
    // 清理防抖定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
      this.resizeTimer = null;
    }
  },
  methods: {
    /** 初始化移动端检测 */
    initMobileDetection() {
      if (typeof window !== 'undefined') {
        this.isMobile = window.innerWidth <= 768;
      }
    },
    /** 处理窗口大小变化 */
    handleResize() {
      // 使用防抖避免频繁触发
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }
      this.resizeTimer = setTimeout(() => {
        if (typeof window !== 'undefined') {
          this.isMobile = window.innerWidth <= 768;
        }
      }, 100);
    },
    /** 切换移动端搜索显示 */
    toggleMobileSearch() {
      this.showMobileSearch = !this.showMobileSearch;
    },
    /** 切换移动端卡片展开状态 */
    toggleMobileCard(item, index) {
      this.$set(item, 'mobileExpanded', !item.mobileExpanded);
    },
    /** 查询赔率管理列表 */
    getList() {
      this.loading = true;
      listOdds(this.queryParams).then(response => {
        this.oddsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询玩法列表 */
    getMethodList() {
      // 设置大的pageSize确保获取所有玩法
      const params = {
        pageNum: 1,
        pageSize: 1000 // 设置足够大的数量
      };
      listMethod(params).then(response => {
        this.methodList = response.rows;
      });
    },
    /** 查询玩家列表 */
    getCustomerList() {
      const params = {
        pageNum: 1,
        pageSize: 1000
      };
      listCustomer(params).then(response => {
        this.customerList = response.rows || [];
      }).catch(error => {
        console.error('获取玩家列表失败:', error);
        this.customerList = [];
      });
    },
    /** 玩家选择变化 */
    handleCustomerChange(customerId) {
      this.selectedCustomerId = customerId;
      if (customerId) {
        // 加载玩家专属赔率
        this.loadCustomerOdds(customerId);
      } else {
        // 加载默认赔率
        this.getList();
      }
    },
    /** 加载玩家专属赔率 */
    loadCustomerOdds(customerId) {
      this.loading = true;
      getCustomerOdds(customerId).then(response => {
        if (response.code === 200 && response.data) {
          this.oddsList = response.data;
          this.hasCustomerOdds = response.data.some(item => item.isCustomerOdds);
          this.total = response.data.length;
        } else {
          this.oddsList = [];
          this.hasCustomerOdds = false;
          this.total = 0;
        }
        this.loading = false;
      }).catch(error => {
        console.error('加载玩家赔率失败:', error);
        this.$message.error('加载玩家赔率失败');
        this.loading = false;
      });
    },
    /** 获取玩家名称 */
    getCustomerName(customerId) {
      const customer = this.customerList.find(c => c.userId === customerId);
      return customer ? customer.name : '未知玩家';
    },
    /** 获取赔率样式类 */
    getOddsClass(row) {
      if (!this.selectedCustomerId) return 'normal';
      return row.isCustomerOdds ? 'custom' : 'default';
    },
    /** 计算赔率差异 */
    getOddsDiff(row) {
      if (!this.selectedCustomerId || !row.defaultOdds) return '-';
      const diff = (parseFloat(row.odds) - parseFloat(row.defaultOdds)).toFixed(2);
      return diff > 0 ? `+${diff}` : diff;
    },
    /** 获取赔率差异样式类 */
    getOddsDiffClass(row) {
      const diff = parseFloat(this.getOddsDiff(row));
      if (diff > 0) return 'positive';
      if (diff < 0) return 'negative';
      return 'neutral';
    },
    /** 修改玩家赔率 */
    handleUpdateCustomerOdds(row) {
      // 打开编辑对话框，设置为玩家赔率编辑模式
      this.reset();
      this.form = { ...row };
      this.form.isCustomerEdit = true;
      this.form.customerId = this.selectedCustomerId;
      this.title = `修改玩家赔率 - ${this.getCustomerName(this.selectedCustomerId)}`;
      this.open = true;
    },
    /** 重置单个玩家赔率 */
    handleResetSingleOdds(row) {
      this.$confirm(`确认将玩家"${this.getCustomerName(this.selectedCustomerId)}"的"${row.methodName}"赔率重置为默认值吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const updateData = [{
          oddsId: row.oddsId,
          odds: row.defaultOdds,
          customerId: this.selectedCustomerId,
          methodId: row.methodId
        }];

        updateCustomerOdds(updateData).then(response => {
          this.$message.success('重置成功');
          this.loadCustomerOdds(this.selectedCustomerId);
        }).catch(error => {
          this.$message.error('重置失败');
        });
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        oddsId: null,
        methodId: null,
        methodName: null,
        odds: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.oddsId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加赔率管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const oddsId = row.oddsId || this.ids
      getOdds(oddsId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改赔率管理";
      });
    },
    /** 玩法选择改变 */
    handleMethodChange(methodId) {
      const method = this.methodList.find(item => item.methodId === methodId);
      if (method) {
        this.form.methodName = method.methodName;
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 判断是否为玩家赔率编辑
          if (this.form.isCustomerEdit && this.form.customerId) {
            // 玩家赔率编辑
            const updateData = [{
              oddsId: this.form.oddsId || null, // 如果没有oddsId则为null，后端会创建新记录
              odds: this.form.odds,
              customerId: this.form.customerId,
              methodId: this.form.methodId,
              sysUserId: this.form.sysUserId,
              methodName: this.form.methodName
            }];

            updateCustomerOdds(updateData).then(() => {
              this.$modal.msgSuccess("修改玩家赔率成功");
              this.open = false;
              this.loadCustomerOdds(this.form.customerId);
            }).catch(error => {
              console.error('修改玩家赔率失败:', error);
              this.$modal.msgError("修改玩家赔率失败");
            });
          } else {
            // 默认赔率编辑
            if (this.form.oddsId != null) {
              updateOdds(this.form).then(() => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addOdds(this.form).then(() => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const oddsIds = row.oddsId || this.ids;
      this.$modal.confirm('是否确认删除赔率管理编号为"' + oddsIds + '"的数据项？').then(function() {
        return delOdds(oddsIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('game/odds/export', {
        ...this.queryParams
      }, `odds_${new Date().getTime()}.xlsx`)
    },
    /** 格式化赔率显示 */
    formatOdds(odds) {
      if (!odds) return '0.00';
      const num = parseFloat(odds);
      return num.toFixed(2) + '倍';
    },
    /** 格式化收益显示 */
    formatProfit(odds, amount = 1) {
      if (!odds) return '￥0.00';
      const num = parseFloat(odds);
      const profit = (num * amount).toFixed(2);
      return '￥' + profit;
    },
    // 计算收益率
    calculateProfitRate(odds) {
      if (!odds) return '0';
      const rate = ((parseFloat(odds) - 1) * 100).toFixed(1);
      return rate;
    },
    // 处理赔率输入变化
    handleOddsChange(value) {
      // 限制小数点后两位
      if (value && value.toString().includes('.')) {
        const parts = value.toString().split('.');
        if (parts[1] && parts[1].length > 2) {
          this.form.odds = parseFloat(value).toFixed(2);
        }
      }
    }
  }
};
</script>

<style scoped>
.odds-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 30px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.page-title i {
  font-size: 28px;
  margin-right: 12px;
}

.page-description {
  font-size: 14px;
  opacity: 0.9;
}

/* 搜索区域样式 */
.search-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-form-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.search-form {
  flex: 1;
  margin-bottom: 0;
}

.search-toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

/* 工具栏样式 */
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.action-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 表格容器样式 */
.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.odds-table {
  border-radius: 8px;
}

/* 表格内容样式 */
.method-name {
  display: flex;
  align-items: center;
  justify-content: center;
}

.odds-display {
  text-align: center;
}

.odds-value {
  font-size: 16px;
  font-weight: 600;
  color: #e6a23c;
  background: linear-gradient(45deg, #f39c12, #e67e22);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  padding: 4px 12px;
  border-radius: 20px;
  background-color: #fef7e6;
  border: 1px solid #f5dab1;
  display: inline-block;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 0;
}

.action-btn-mini {
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  min-width: 50px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn-mini:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.edit-btn {
  background: #409EFF;
  color: white;
}

.edit-btn:hover {
  background: #66b1ff;
}

.delete-btn {
  background: #F56C6C;
  color: white;
}

.delete-btn:hover {
  background: #f78989;
}

/* 现代化弹窗样式 */
.modern-odds-dialog {
  .el-dialog {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }

  .el-dialog__header {
    padding: 0;
    border-bottom: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 0;
    border-top: none;
  }
}

/* 自定义头部 */
.dialog-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.header-text h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
}

.header-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.close-btn {
  color: white !important;
  font-size: 20px;
  padding: 8px;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
  }
}

/* 内容区域 */
.modern-dialog-content {
  padding: 20px;
  background: #fafbfc;
}

.modern-odds-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

/* 表单卡片 */
.form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  }
}

.card-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #495057;
  font-size: 14px;

  i {
    color: #667eea;
    font-size: 16px;
  }
}

.method-count {
  margin-left: auto;
  font-size: 12px;
  color: #909399;
  background: #e9ecef;
  padding: 2px 8px;
  border-radius: 12px;
}

.card-content {
  padding: 16px;
}

/* 玩法选择区域样式 */
.method-select-wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.select-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #606266;
  font-weight: 500;

  i {
    color: #667eea;
    font-size: 14px;
  }
}

.full-width {
  width: 100%;
}

.method-tips {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.tip-row {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #909399;

  i {
    color: #667eea;
    font-size: 12px;
  }
}

/* 现代化选择器 */
.modern-select {
  .el-input__inner {
    height: 44px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    padding-left: 12px;
    padding-right: 30px;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    &::placeholder {
      color: #c0c4cc;
      font-size: 13px;
    }
  }

  .el-input__suffix {
    right: 8px;
  }
}

.method-option {
  padding: 12px 16px;
  height: auto;
  line-height: 1.5;
}

.option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-height: 24px;
}

.option-name {
  font-weight: 500;
  color: #2c3e50;
  flex: 1;
  white-space: normal;
  word-wrap: break-word;
  margin-right: 8px;
}

.option-id {
  color: #8492a6;
  font-size: 12px;
  background: #f8f9fa;
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 现代化输入框 */
.odds-input-wrapper {
  margin-bottom: 12px;
}

.modern-input {
  .el-input__inner {
    height: 40px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }

  .el-input-group__prepend {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px 0 0 8px;

    i {
      margin-right: 4px;
    }
  }

  .el-input-group__append {
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #e9ecef;
    border-left: none;
    border-radius: 0 8px 8px 0;
  }
}

/* 输入提示 */
.input-tips {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;

  i {
    color: #667eea;
  }
}

/* 预览卡片 */
.preview-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border: 2px solid #667eea;
}

.preview-header {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;

  .el-tag {
    margin-left: auto;
  }
}

.preview-grid {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.preview-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.preview-icon {
  width: 32px;
  height: 32px;
  background: #e3f2fd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1976d2;
  font-size: 16px;
}

.profit-icon {
  background: #e8f5e8;
  color: #4caf50;
}

.preview-arrow {
  color: #667eea;
  font-size: 20px;
  font-weight: bold;
}

.preview-info {
  flex: 1;
}

.preview-label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.preview-value {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.preview-value.profit {
  color: #4caf50;
  font-size: 18px;
}

.profit-rate {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  color: #495057;
}

.rate-value {
  font-weight: 600;
  color: #4caf50;
  font-size: 16px;
}

/* 底部按钮 */
.modern-dialog-footer {
  padding: 16px 20px;
  background: #f8f9fa;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-btn {
  height: 36px;
  padding: 0 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    border-color: #adb5bd;
    background: #f8f9fa;
  }
}

.submit-btn {
  height: 36px;
  padding: 0 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }
}

/* 移动端搜索按钮样式 */
.mobile-search-toggle {
  margin-bottom: 10px;
  text-align: center;
}

.mobile-search-btn {
  width: 100%;
  max-width: 200px;
}

/* 移动端搜索表单样式 */
.mobile-search-form {
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.mobile-search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 0;
  width: 100%;
}

.mobile-toolbar {
  margin-top: 10px;
  text-align: center;
}

/* 移动端玩家选择样式 */
.mobile-player-select {
  margin-bottom: 16px;
}

.mobile-select {
  width: 100%;
}

/* 移动端卡片容器 */
.mobile-card-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mobile-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.mobile-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.mobile-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafbfc;
  border-bottom: 1px solid #e9ecef;
}

.mobile-card .card-header-left {
  flex: 1;
}

.mobile-card .odds-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-card .method-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-card .method-name i {
  color: #409EFF;
  font-size: 18px;
}

.mobile-card .odds-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.mobile-card .customer-info {
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.mobile-card .customer-info i {
  color: #67c23a;
}

.mobile-card .odds-value {
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.mobile-card .odds-value.normal {
  color: #e6a23c;
}

.mobile-card .odds-value.custom {
  color: #67c23a;
}

.mobile-card .odds-value.default {
  color: #909399;
}

.mobile-card .card-header-right {
  padding: 4px;
}

.mobile-card .card-header-right i {
  font-size: 16px;
  color: #909399;
  transition: transform 0.3s ease;
}

.mobile-card .card-header-right i.expanded {
  transform: rotate(180deg);
}

.mobile-card .card-content {
  padding: 12px 16px;
  background: white;
}

.mobile-card .detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.mobile-card .detail-row:last-child {
  border-bottom: none;
}

.mobile-card .detail-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.mobile-card .detail-value {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 600;
}

.mobile-action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.mobile-action-buttons .el-button {
  margin: 0;
  flex: 1;
  min-width: 60px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .odds-container {
    padding: 12px;
  }

  .toolbar-container {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar-left {
    flex-wrap: wrap;
    justify-content: center;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons .el-button {
    margin: 0 4px 4px 0;
  }

  .action-buttons .el-button:last-child {
    margin-right: 0;
  }

  .modern-dialog-content {
    padding: 20px;
  }

  .preview-grid {
    flex-direction: column;
    gap: 12px;
  }

  .preview-arrow {
    transform: rotate(90deg);
  }

  .modern-dialog-footer {
    padding: 20px;
    flex-direction: column;
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
  }

  .search-form-wrapper {
    flex-direction: column;
    gap: 10px;
  }

  .search-toolbar {
    justify-content: center;
  }
}

/* 玩家选择区域样式 */
.player-selection-container {
  margin-bottom: 20px;
}

.player-selection-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafbfc;
}

.selection-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  color: #303133;
}

.selection-header i {
  margin-right: 8px;
  color: #409eff;
  font-size: 16px;
}

.selection-title {
  font-size: 16px;
}

.player-radio-group {
  margin-bottom: 16px;
}

.player-radio-group .el-radio-group {
  display: inline-block;
  font-size: 0; /* 消除inline-block元素间的空白 */
  line-height: 0;
}

.player-radio-group .el-radio-group > * {
  font-size: 14px; /* 恢复字体大小 */
  line-height: normal;
}

.player-radio-group .el-radio-button {
  margin: 0 !important;
}

/* 移除了margin-right设置，让按钮完全贴合 */

/* 强制移除Element UI默认的按钮间距 */
.player-radio-group .el-radio-button {
  margin: 0 !important;
  display: inline-block !important;
  vertical-align: top !important;
}

.player-radio-group .el-radio-button:not(:first-child) {
  margin-left: -1px !important;
}

.player-radio-group .el-radio-button:not(:first-child) .el-radio-button__inner {
  border-left: 0 !important;
}

.player-radio-group .el-radio-button__inner {
  border-radius: 0 !important;
  margin: 0 !important;
  display: inline-block !important;
}

.player-radio-group .el-radio-button:first-child .el-radio-button__inner {
  border-radius: 4px 0 0 4px !important;
}

.player-radio-group .el-radio-button:last-child .el-radio-button__inner {
  border-radius: 0 4px 4px 0 !important;
}

.all-users-btn .el-radio-button__inner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  font-weight: 500;
  margin: 0;
  border-radius: 4px 0 0 4px;
}

.all-users-btn .el-radio-button__inner i {
  margin-right: 6px;
}

.all-users-btn.is-active .el-radio-button__inner {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  border-color: #5a6fd8;
  box-shadow: 0 2px 8px rgba(90, 111, 216, 0.3);
}

.customer-btn .el-radio-button__inner {
  background: #f8f9fa;
  border-color: #e9ecef;
  color: #495057;
  margin: 0;
  border-radius: 0;
}

.customer-btn:last-child .el-radio-button__inner {
  border-radius: 0 4px 4px 0;
}

.customer-btn .el-radio-button__inner i {
  margin-right: 6px;
  color: #67c23a;
}

.customer-btn.is-active .el-radio-button__inner {
  background: #67c23a;
  border-color: #67c23a;
  color: white;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

.selection-status {
  display: flex;
  align-items: center;
  gap: 16px;
}

.selection-status .el-tag {
  font-size: 12px;
}

.selection-status .el-tag i {
  margin-right: 4px;
}

.no-odds-tip {
  color: #e6a23c;
  font-size: 12px;
}

.no-odds-tip i {
  margin-right: 4px;
}

/* 赔率对比样式 */
.odds-value.normal {
  color: #303133;
  font-weight: 600;
}

.odds-value.custom {
  color: #67c23a;
  font-weight: 600;
}

.odds-value.default {
  color: #909399;
  font-weight: 600;
}

.default-odds .odds-value {
  color: #909399;
  font-weight: 500;
}

.odds-diff {
  font-weight: 600;
  font-size: 12px;
}

.odds-diff.positive {
  color: #f56c6c;
}

.odds-diff.negative {
  color: #67c23a;
}

.odds-diff.neutral {
  color: #909399;
}

/* 玩家名称列样式 */
.customer-name {
  display: flex;
  align-items: center;
  justify-content: center;
}

.customer-name-text {
  color: #67c23a;
  font-weight: 500;
}

.customer-name-text i {
  margin-right: 6px;
  color: #67c23a;
}

.system-default {
  color: #909399;
  font-weight: 500;
}

.system-default i {
  margin-right: 6px;
  color: #409eff;
}

/* 全局下拉框样式 */
.method-select-dropdown {
  .el-select-dropdown__item {
    height: auto !important;
    line-height: 1.5 !important;
    padding: 12px 16px !important;
    white-space: normal !important;
    word-wrap: break-word !important;
  }
}
</style>
