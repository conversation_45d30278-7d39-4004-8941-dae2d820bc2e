{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\winning\\index.vue?vue&type=style&index=0&id=a257dc60&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\winning\\index.vue", "mtime": 1758864119600}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750942927475}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750942929511}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLyog6aG16Z2i5aS06YOo5qC35byPICovDQoucGFnZS1oZWFkZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KICBjb2xvcjogd2hpdGU7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQogIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpOw0KfQ0KDQouaGVhZGVyLWNvbnRlbnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDE2cHg7DQp9DQoNCi5oZWFkZXItaWNvbiB7DQogIHdpZHRoOiA2MHB4Ow0KICBoZWlnaHQ6IDYwcHg7DQogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAyOHB4Ow0KfQ0KDQouaGVhZGVyLWluZm8gaDIgew0KICBtYXJnaW46IDAgMCA4cHggMDsNCiAgZm9udC1zaXplOiAyNHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQouaGVhZGVyLWluZm8gcCB7DQogIG1hcmdpbjogMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBvcGFjaXR5OiAwLjk7DQp9DQoNCi8qIOaQnOe0ouWMuuWfn+agt+W8jyAqLw0KLnNlYXJjaC1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgcGFkZGluZzogMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi5zZWFyY2gtdGlwIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCiAgbWFyZ2luLXRvcDogNHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDRweDsNCn0NCg0KLnNlYXJjaC1idG4gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KICBib3JkZXI6IG5vbmU7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCn0NCg0KLnNlYXJjaC1idG46aG92ZXIgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7DQogIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpOw0KfQ0KDQoucmVzZXQtYnRuIHsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQoucmVzZXQtYnRuOmhvdmVyIHsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpOw0KfQ0KDQovKiDlt6XlhbfmoI/moLflvI8gKi8NCi50b29sYmFyLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmOGY5ZmEgMCUsICNmZmZmZmYgMTAwJSk7DQogIGJvcmRlci1yYWRpdXM6IDE2cHg7DQogIHBhZGRpbmc6IDIwcHggMjRweDsNCiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMDgpOw0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMDUpOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi50b29sYmFyLWNvbnRhaW5lcjo6YmVmb3JlIHsNCiAgY29udGVudDogJyc7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOiAwOw0KICBsZWZ0OiAwOw0KICByaWdodDogMDsNCiAgaGVpZ2h0OiAycHg7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiA1MCUsICM2NjdlZWEgMTAwJSk7DQp9DQoNCi8qIOW3peWFt+agj+W4g+WxgCAqLw0KLnRvb2xiYXItY29udGVudCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi50b29sYmFyLWxlZnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGdhcDogMTJweDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLnRvb2xiYXItcmlnaHQgew0KICBtYXJnaW4tbGVmdDogYXV0bzsNCiAgZmxleC1zaHJpbms6IDA7DQp9DQoNCi8qIOW3peWFt+agj+aMiemSrue+juWMliAqLw0KLnRvb2xiYXItYnRuIHsNCiAgYm9yZGVyLXJhZGl1czogOHB4ICFpbXBvcnRhbnQ7DQogIGZvbnQtd2VpZ2h0OiA1MDAgIWltcG9ydGFudDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKSAhaW1wb3J0YW50Ow0KICBib3JkZXItd2lkdGg6IDEuNXB4ICFpbXBvcnRhbnQ7DQogIHBhZGRpbmc6IDhweCAxNnB4ICFpbXBvcnRhbnQ7DQogIGZvbnQtc2l6ZTogMTNweCAhaW1wb3J0YW50Ow0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi50b29sYmFyLWJ0bjo6YmVmb3JlIHsNCiAgY29udGVudDogJyc7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOiAwOw0KICBsZWZ0OiAtMTAwJTsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpLCB0cmFuc3BhcmVudCk7DQogIHRyYW5zaXRpb246IGxlZnQgMC41czsNCn0NCg0KLnRvb2xiYXItYnRuOmhvdmVyOjpiZWZvcmUgew0KICBsZWZ0OiAxMDAlOw0KfQ0KDQoudG9vbGJhci1idG46aG92ZXIgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCkgIWltcG9ydGFudDsNCiAgYm94LXNoYWRvdzogMCA2cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMikgIWltcG9ydGFudDsNCn0NCg0KLyog5bGV5byA5oyJ6ZKu5qC35byPICovDQouZXhwYW5kLWJ0bi5lbC1idXR0b24tLXByaW1hcnkgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDA5ZWZmIDAlLCAjNjZiMWZmIDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlci1jb2xvcjogIzQwOWVmZiAhaW1wb3J0YW50Ow0KICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDsNCn0NCg0KLmV4cGFuZC1idG4uZWwtYnV0dG9uLS1wcmltYXJ5OmhvdmVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2YjFmZiAwJSwgIzQwOWVmZiAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXItY29sb3I6ICM2NmIxZmYgIWltcG9ydGFudDsNCiAgY29sb3I6IHdoaXRlICFpbXBvcnRhbnQ7DQogIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuNSkgIWltcG9ydGFudDsNCn0NCg0KLyog5Yi35paw5oyJ6ZKu5qC35byPICovDQoucmVmcmVzaC1idG4uZWwtYnV0dG9uLS1pbmZvIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzkwOTM5OSAwJSwgI2IxYjNiOCAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXItY29sb3I6ICM5MDkzOTkgIWltcG9ydGFudDsNCiAgY29sb3I6IHdoaXRlICFpbXBvcnRhbnQ7DQp9DQoNCi5yZWZyZXNoLWJ0bi5lbC1idXR0b24tLWluZm86aG92ZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjYjFiM2I4IDAlLCAjOTA5Mzk5IDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlci1jb2xvcjogI2IxYjNiOCAhaW1wb3J0YW50Ow0KICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDsNCiAgYm94LXNoYWRvdzogMCA2cHggMjBweCByZ2JhKDE0NCwgMTQ3LCAxNTMsIDAuNSkgIWltcG9ydGFudDsNCn0NCg0KLyog5a+85Ye65oyJ6ZKu5qC35byPICovDQouZXhwb3J0LWJ0bi5lbC1idXR0b24tLXdhcm5pbmcuaXMtcGxhaW4gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZTZhMjNjIDAlLCAjZWJiNTYzIDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlci1jb2xvcjogI2U2YTIzYyAhaW1wb3J0YW50Ow0KICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDsNCn0NCg0KLmV4cG9ydC1idG4uZWwtYnV0dG9uLS13YXJuaW5nLmlzLXBsYWluOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ViYjU2MyAwJSwgI2U2YTIzYyAxMDAlKSAhaW1wb3J0YW50Ow0KICBib3JkZXItY29sb3I6ICNlYmI1NjMgIWltcG9ydGFudDsNCiAgY29sb3I6IHdoaXRlICFpbXBvcnRhbnQ7DQogIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgyMzAsIDE2MiwgNjAsIDAuNSkgIWltcG9ydGFudDsNCn0NCg0KLyog5a+56LSm5oyJ6ZKu5qC35byPICovDQoucmVjb25jaWxpYXRpb24tYnRuLmVsLWJ1dHRvbi0tc3VjY2VzcyB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2N2MyM2EgMCUsICM4NWNlNjEgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyLWNvbG9yOiAjNjdjMjNhICFpbXBvcnRhbnQ7DQogIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50Ow0KfQ0KDQoucmVjb25jaWxpYXRpb24tYnRuLmVsLWJ1dHRvbi0tc3VjY2Vzczpob3ZlciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM4NWNlNjEgMCUsICM2N2MyM2EgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyLWNvbG9yOiAjODVjZTYxICFpbXBvcnRhbnQ7DQogIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50Ow0KICBib3gtc2hhZG93OiAwIDZweCAyMHB4IHJnYmEoMTAzLCAxOTQsIDU4LCAwLjUpICFpbXBvcnRhbnQ7DQp9DQoNCi5yZWNvbmNpbGlhdGlvbi1idG4uZWwtYnV0dG9uLS1kYW5nZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjU2YzZjIDAlLCAjZjc4OTg5IDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlci1jb2xvcjogI2Y1NmM2YyAhaW1wb3J0YW50Ow0KICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDsNCn0NCg0KLnJlY29uY2lsaWF0aW9uLWJ0bi5lbC1idXR0b24tLWRhbmdlcjpob3ZlciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmNzg5ODkgMCUsICNmNTZjNmMgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyLWNvbG9yOiAjZjc4OTg5ICFpbXBvcnRhbnQ7DQogIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50Ow0KICBib3gtc2hhZG93OiAwIDZweCAyMHB4IHJnYmEoMjQ1LCAxMDgsIDEwOCwgMC41KSAhaW1wb3J0YW50Ow0KfQ0KDQovKiDnpoHnlKjnirbmgIHmoLflvI8gKi8NCi50b29sYmFyLWJ0bi5pcy1kaXNhYmxlZCB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmNWY3ZmEgMCUsICNjM2NkZDcgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyLWNvbG9yOiAjZGNkZmU2ICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjYzBjNGNjICFpbXBvcnRhbnQ7DQogIGN1cnNvcjogbm90LWFsbG93ZWQgIWltcG9ydGFudDsNCiAgdHJhbnNmb3JtOiBub25lICFpbXBvcnRhbnQ7DQogIGJveC1zaGFkb3c6IG5vbmUgIWltcG9ydGFudDsNCn0NCg0KLnRvb2xiYXItYnRuLmlzLWRpc2FibGVkOjpiZWZvcmUgew0KICBkaXNwbGF5OiBub25lOw0KfQ0KDQovKiDmjInpkq7lm77moIfmoLflvI/kvJjljJYgKi8NCi50b29sYmFyLWJ0biBbY2xhc3MqPSJlbC1pY29uLSJdIHsNCiAgbWFyZ2luLXJpZ2h0OiA2cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLyog5ZON5bqU5byP6LCD5pW0ICovDQpAbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7DQogIC50b29sYmFyLWNvbnRhaW5lciB7DQogICAgcGFkZGluZzogMTZweCAyMHB4Ow0KICB9DQoNCiAgLnRvb2xiYXItbGVmdCB7DQogICAgZ2FwOiA4cHg7DQogIH0NCg0KICAudG9vbGJhci1idG4gew0KICAgIHBhZGRpbmc6IDZweCAxMnB4ICFpbXBvcnRhbnQ7DQogICAgZm9udC1zaXplOiAxMnB4ICFpbXBvcnRhbnQ7DQogIH0NCn0NCg0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC50b29sYmFyLWNvbnRhaW5lciB7DQogICAgcGFkZGluZzogMTJweCAxNnB4Ow0KICB9DQoNCiAgLnRvb2xiYXItY29udGVudCB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDEycHg7DQogICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7DQogIH0NCg0KICAudG9vbGJhci1sZWZ0IHsNCiAgICBnYXA6IDZweDsNCiAgfQ0KDQogIC50b29sYmFyLXJpZ2h0IHsNCiAgICBtYXJnaW4tbGVmdDogMDsNCiAgICBhbGlnbi1zZWxmOiBmbGV4LWVuZDsNCiAgfQ0KDQogIC50b29sYmFyLWJ0biB7DQogICAgcGFkZGluZzogNHB4IDhweCAhaW1wb3J0YW50Ow0KICAgIGZvbnQtc2l6ZTogMTFweCAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLnRvb2xiYXItYnRuIFtjbGFzcyo9ImVsLWljb24tIl0gew0KICAgIG1hcmdpbi1yaWdodDogNHB4Ow0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgfQ0KfQ0KDQovKiDooajmoLzlrrnlmajmoLflvI8gKi8NCi50YWJsZS1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgcGFkZGluZzogMjBweDsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi8qIOWxleW8gOWGheWuueagt+W8jyAqLw0KLmV4cGFuZC1jb250ZW50IHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBtYXJnaW46IDEwcHggMDsNCn0NCg0KLmV4cGFuZC1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzJjM2U1MDsNCn0NCg0KLmV4cGFuZC1oZWFkZXIgaSB7DQogIGNvbG9yOiAjNjY3ZWVhOw0KfQ0KDQoudGFibGUtZXhwYW5kIHsNCiAgZm9udC1zaXplOiAwOw0KfQ0KDQoudGFibGUtZXhwYW5kIGxhYmVsIHsNCiAgd2lkdGg6IDkwcHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQoudGFibGUtZXhwYW5kIC5lbC1mb3JtLWl0ZW0gew0KICBtYXJnaW4tcmlnaHQ6IDA7DQogIG1hcmdpbi1ib3R0b206IDEycHg7DQogIHdpZHRoOiA1MCU7DQp9DQoNCi50YWJsZS1leHBhbmQgLmVsLWZvcm0taXRlbS5mdWxsLXdpZHRoIHsNCiAgd2lkdGg6IDEwMCU7DQogIG1hcmdpbi10b3A6IDA7DQp9DQoNCi5leHBhbmQtdmFsdWUgew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMmMzZTUwOw0KfQ0KDQoubnVtYmVyLXRhZ3Mgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGdhcDogNnB4Ow0KfQ0KDQoubnVtYmVyLXRhZyB7DQogIG1hcmdpbjogMDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KfQ0KDQouYmV0LXRhZyB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM0MDlFRkYgMCUsICM2NmIxZmYgMTAwJSk7DQogIGJvcmRlci1jb2xvcjogIzQwOUVGRjsNCn0NCg0KLndpbm5pbmctdGFnIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY3QzIzQSAwJSwgIzg1Y2U2MSAxMDAlKTsNCiAgYm9yZGVyLWNvbG9yOiAjNjdDMjNBOw0KfQ0KDQoubm8tZGF0YSB7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBmb250LXN0eWxlOiBpdGFsaWM7DQp9DQoNCi8qIOihqOagvOWIl+agt+W8jyAqLw0KLnVzZXItaW5mbywgLm1ldGhvZC1pbmZvIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGdhcDogNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQoudXNlci1pbmZvIHsNCiAgY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5tZXRob2QtaW5mbyB7DQogIGNvbG9yOiAjNjdDMjNBOw0KfQ0KDQouc2VyaWFsLW51bWJlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQouc2VyaWFsLXZhbHVlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMyYzNlNTA7DQp9DQoNCi5sb3R0ZXJ5LXR5cGUgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCn0NCg0KLmxvdHRlcnktdGFnIHsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KfQ0KDQoubG90dGVyeS11bmtub3duIHsNCiAgY29sb3I6ICM5MDkzOTk7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5hbW91bnQtc2ltcGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICNGNTZDNkM7DQp9DQoNCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5wYWdlLWhlYWRlciB7DQogICAgcGFkZGluZzogMTZweDsNCiAgfQ0KDQogIC5oZWFkZXItY29udGVudCB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgZ2FwOiAxMnB4Ow0KICB9DQoNCiAgLnNlYXJjaC1jb250YWluZXIgew0KICAgIHBhZGRpbmc6IDE2cHg7DQogIH0NCg0KICAudGFibGUtZXhwYW5kIC5lbC1mb3JtLWl0ZW0gew0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQoNCiAgLm51bWJlci10YWdzIHsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgfQ0KDQogIC8qIOenu+WKqOerr+aQnOe0ouaMiemSruagt+W8jyAqLw0KICAubW9iaWxlLXNlYXJjaC10b2dnbGUgew0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICB9DQoNCiAgLm1vYmlsZS1zZWFyY2gtYnRuIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBtYXgtd2lkdGg6IDIwMHB4Ow0KICB9DQoNCiAgLyog56e75Yqo56uv5pCc57Si6KGo5Y2V5qC35byPICovDQogIC5tb2JpbGUtc2VhcmNoLWZvcm0gew0KICAgIHBhZGRpbmc6IDEwcHg7DQogICAgYmFja2dyb3VuZDogI2Y1ZjdmYTsNCiAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogIH0NCg0KICAubW9iaWxlLXNlYXJjaC1mb3JtIC5lbC1mb3JtLWl0ZW0gew0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgbWFyZ2luLXJpZ2h0OiAwOw0KICAgIHdpZHRoOiAxMDAlOw0KICB9DQoNCiAgLm1vYmlsZS1zZWFyY2gtZm9ybSAuZWwtZm9ybS1pdGVtX19sYWJlbCB7DQogICAgd2lkdGg6IDYwcHggIWltcG9ydGFudDsNCiAgICBmb250LXNpemU6IDEycHg7DQogIH0NCg0KICAubW9iaWxlLXNlYXJjaC1mb3JtIC5lbC1pbnB1dCwNCiAgLm1vYmlsZS1zZWFyY2gtZm9ybSAuZWwtc2VsZWN0IHsNCiAgICB3aWR0aDogMTAwJSAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLyog56e75Yqo56uv6K+G5Yir5qGG6L6T5YWl5Yy65Z+f5LyY5YyWICovDQogIC5tb2JpbGUtc2VhcmNoLWZvcm0gLmVsLWZvcm0taXRlbSAuZWwtdGV4dGFyZWEgew0KICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogIH0NCg0KICAubW9iaWxlLXNlYXJjaC1mb3JtIC5lbC1mb3JtLWl0ZW0gLmVsLXRleHRhcmVhIC5lbC10ZXh0YXJlYV9faW5uZXIgew0KICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgbWF4LXdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgbWluLWhlaWdodDogNjBweCAhaW1wb3J0YW50Ow0KICAgIG1heC1oZWlnaHQ6IDgwcHggIWltcG9ydGFudDsNCiAgICByZXNpemU6IHZlcnRpY2FsOw0KICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAxLjQ7DQogIH0NCg0KICAvKiDnp7vliqjnq6/lt6XlhbfmoI/moLflvI8gKi8NCiAgLm1vYmlsZS10b29sYmFyIC50b29sYmFyLWNvbnRlbnQgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZ2FwOiAxMnB4Ow0KICAgIGFsaWduLWl0ZW1zOiBzdHJldGNoOw0KICB9DQoNCiAgLm1vYmlsZS10b29sYmFyLWxlZnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC13cmFwOiBub3dyYXA7DQogICAgZ2FwOiAzcHg7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICB9DQoNCiAgLm1vYmlsZS10b29sYmFyLWxlZnQgLnRvb2xiYXItYnRuIHsNCiAgICBmbGV4OiAxOw0KICAgIG1pbi13aWR0aDogY2FsYygyNSUgLSA2cHgpOw0KICAgIG1heC13aWR0aDogY2FsYygyNSUgLSA2cHgpOw0KICAgIHBhZGRpbmc6IDRweCAycHggIWltcG9ydGFudDsNCiAgICBmb250LXNpemU6IDlweCAhaW1wb3J0YW50Ow0KICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgfQ0KDQogIC8qIOenu+WKqOerr+WNoeeJh+W4g+WxgCAqLw0KICAubW9iaWxlLWNhcmQtY29udGFpbmVyIHsNCiAgICBwYWRkaW5nOiAxMHB4Ow0KICB9DQoNCiAgLm1vYmlsZS1jYXJkIHsNCiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgfQ0KDQogIC5tb2JpbGUtY2FyZC5yZWNvbmNpbGVkIHsNCiAgICBib3JkZXI6IDJweCBzb2xpZCAjNjdjMjNhOw0KICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmMGY5ZmYgMCUsICNlNmY3ZmYgMTAwJSk7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLmNhcmQtaGVhZGVyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHBhZGRpbmc6IDEycHg7DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLmNhcmQtaGVhZGVyLWxlZnQgew0KICAgIGZsZXg6IDE7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICB9DQoNCiAgLm1vYmlsZS1jYXJkIC53aW5uaW5nLWluZm8gew0KICAgIGZsZXg6IDE7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLnVzZXItbmFtZSB7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgIGNvbG9yOiAjNDA5ZWZmOw0KICAgIG1hcmdpbi1ib3R0b206IDRweDsNCiAgfQ0KDQogIC5tb2JpbGUtY2FyZCAud2lubmluZy1tZXRhIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgZ2FwOiA4cHg7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KICAgIGNvbG9yOiAjNjY2Ow0KICAgIGZsZXgtd3JhcDogd3JhcDsNCiAgfQ0KDQogIC5tb2JpbGUtY2FyZCAuc2VyaWFsLW51bWJlciB7DQogICAgY29sb3I6ICMyYzNlNTA7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICBmb250LXNpemU6IDEzcHg7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLm1ldGhvZC1uYW1lIHsNCiAgICBjb2xvcjogIzY3YzIzYTsNCiAgICBmb250LXdlaWdodDogNTAwOw0KICB9DQoNCiAgLm1vYmlsZS1jYXJkIC5jYXJkLWhlYWRlci1yaWdodCB7DQogICAgdGV4dC1hbGlnbjogcmlnaHQ7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLndpbi1hbW91bnQgew0KICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICBjb2xvcjogI2Y1NmM2YzsNCiAgICBtYXJnaW4tYm90dG9tOiA0cHg7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLnJlY29uY2lsaWF0aW9uLXN0YXR1cyB7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KICAgIHBhZGRpbmc6IDJweCA2cHg7DQogICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgIGJhY2tncm91bmQ6ICNlNmY3ZmY7DQogICAgY29sb3I6ICMxODkwZmY7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLnJlY29uY2lsaWF0aW9uLXN0YXR1cy5yZWNvbmNpbGVkIHsNCiAgICBiYWNrZ3JvdW5kOiAjZjZmZmVkOw0KICAgIGNvbG9yOiAjNTJjNDFhOw0KICB9DQoNCiAgLm1vYmlsZS1jYXJkIC5jYXJkLWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDEycHg7DQogICAgYmFja2dyb3VuZDogI2ZhZmFmYTsNCiAgfQ0KDQogIC5tb2JpbGUtY2FyZCAuZGV0YWlsLXJvdyB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBtYXJnaW4tYm90dG9tOiA4cHg7DQogICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLmRldGFpbC1yb3c6bGFzdC1jaGlsZCB7DQogICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgfQ0KDQogIC5tb2JpbGUtY2FyZCAuZGV0YWlsLWxhYmVsIHsNCiAgICB3aWR0aDogNzBweDsNCiAgICBmb250LXNpemU6IDEycHg7DQogICAgY29sb3I6ICM2NjY7DQogICAgZmxleC1zaHJpbms6IDA7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLmRldGFpbC12YWx1ZSB7DQogICAgZmxleDogMTsNCiAgICBmb250LXNpemU6IDEycHg7DQogICAgY29sb3I6ICMzMzM7DQogICAgd29yZC1icmVhazogYnJlYWstYWxsOw0KICB9DQoNCiAgLm1vYmlsZS1jYXJkIC5kZXRhaWwtdmFsdWUgLmVsLXRhZyB7DQogICAgZm9udC1zaXplOiAxMHB4Ow0KICAgIHBhZGRpbmc6IDAgNHB4Ow0KICAgIG1hcmdpbjogMXB4Ow0KICB9DQoNCiAgLyog56e75Yqo56uv5a+56LSm5qih5byP5o+Q56S6ICovDQogIC5tb2JpbGUtcmVjb25jaWxpYXRpb24tbm90aWNlIHsNCiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZTZmN2ZmIDAlLCAjZjBmOWZmIDEwMCUpOw0KICAgIGJvcmRlcjogMXB4IHNvbGlkICNiM2Q4ZmY7DQogICAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICAgIHBhZGRpbmc6IDhweCAxMnB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7DQogIH0NCg0KICAucmVjb25jaWxpYXRpb24tY29tcGFjdCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBmb250LXNpemU6IDEzcHg7DQogICAgY29sb3I6ICMxOTc2ZDI7DQogIH0NCg0KICAucmVjb25jaWxpYXRpb24tbGVmdCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGZsZXg6IDE7DQogIH0NCg0KICAucmVjb25jaWxpYXRpb24tbGVmdCBpIHsNCiAgICBjb2xvcjogIzQwOUVGRjsNCiAgICBtYXJnaW4tcmlnaHQ6IDZweDsNCiAgICBmb250LXNpemU6IDE0cHg7DQogIH0NCg0KICAubW9kZS10ZXh0IHsNCiAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgfQ0KDQogIC5zdGF0cy1jb21wYWN0IHsNCiAgICBmb250LXNpemU6IDExcHg7DQogICAgY29sb3I6ICM2NjY7DQogICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpOw0KICAgIHBhZGRpbmc6IDJweCA2cHg7DQogICAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICB9DQoNCiAgLmV4aXQtYnRuLWNvbXBhY3Qgew0KICAgIGNvbG9yOiAjZjU2YzZjOw0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBwYWRkaW5nOiAwOw0KICAgIG1hcmdpbi1sZWZ0OiA4cHg7DQogIH0NCg0KICAvKiDnp7vliqjnq6/lr7notKbmj5DnpLrmoYblkLjpmYTnirbmgIEgKi8NCiAgLm1vYmlsZS1yZWNvbmNpbGlhdGlvbi1maXhlZCB7DQogICAgcG9zaXRpb246IGZpeGVkICFpbXBvcnRhbnQ7DQogICAgdG9wOiAwICFpbXBvcnRhbnQ7DQogICAgbGVmdDogMCAhaW1wb3J0YW50Ow0KICAgIHJpZ2h0OiAwICFpbXBvcnRhbnQ7DQogICAgei1pbmRleDogMTAwMCAhaW1wb3J0YW50Ow0KICAgIG1hcmdpbjogMCAhaW1wb3J0YW50Ow0KICAgIGJvcmRlci1yYWRpdXM6IDAgIWltcG9ydGFudDsNCiAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjE1KSAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLyog56e75Yqo56uv5Y2h54mH54K55Ye75qC35byPICovDQogIC5tb2JpbGUtY2FyZC1jb250YWluZXI6bm90KC5yZWNvbmNpbGlhdGlvbi1tb2RlKSAubW9iaWxlLWNhcmQgew0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgfQ0KDQogIC5tb2JpbGUtY2FyZC1jb250YWluZXI6bm90KC5yZWNvbmNpbGlhdGlvbi1tb2RlKSAubW9iaWxlLWNhcmQ6aG92ZXIgew0KICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjA1KTsNCiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7DQogICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIH0NCg0KICAucmVjb25jaWxpYXRpb24tbW9kZSAubW9iaWxlLWNhcmQgLmNhcmQtaGVhZGVyLWxlZnQgew0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMnMgZWFzZTsNCiAgfQ0KDQogIC5yZWNvbmNpbGlhdGlvbi1tb2RlIC5tb2JpbGUtY2FyZCAuY2FyZC1oZWFkZXItbGVmdDpob3ZlciB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSg2NCwgMTU4LCAyNTUsIDAuMSk7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLmNhcmQtaGVhZGVyLXJpZ2h0LnJlY29uY2lsaWF0aW9uLWNsaWNrYWJsZSB7DQogICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4ycyBlYXNlOw0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICBwYWRkaW5nOiA0cHg7DQogIH0NCg0KICAubW9iaWxlLWNhcmQgLmNhcmQtaGVhZGVyLXJpZ2h0LnJlY29uY2lsaWF0aW9uLWNsaWNrYWJsZTpob3ZlciB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNDUsIDEwOCwgMTA4LCAwLjEpOw0KICB9DQp9DQovKiDlr7notKbmqKHlvI/mj5DnpLrmoLflvI8gKi8NCi5yZWNvbmNpbGlhdGlvbi1ub3RpY2Ugew0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQoucmVjb25jaWxpYXRpb24tbm90aWNlLnJlY29uY2lsaWF0aW9uLWZpeGVkIHsNCiAgcG9zaXRpb246IGZpeGVkOw0KICB0b3A6IDEwcHg7DQogIGxlZnQ6IDUwJTsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpOw0KICB6LWluZGV4OiAxMDAxOw0KICB3aWR0aDogY2FsYygxMDAlIC0gNDBweCk7DQogIG1heC13aWR0aDogMTIwMHB4Ow0KICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7DQogIGFuaW1hdGlvbjogc2xpZGVEb3duIDAuM3MgZWFzZS1vdXQ7DQogIGJhY2tkcm9wLWZpbHRlcjogYmx1cig4cHgpOw0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpOw0KfQ0KDQpAa2V5ZnJhbWVzIHNsaWRlRG93biB7DQogIGZyb20gew0KICAgIG9wYWNpdHk6IDA7DQogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpIHRyYW5zbGF0ZVkoLTIwcHgpOw0KICB9DQogIHRvIHsNCiAgICBvcGFjaXR5OiAxOw0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKSB0cmFuc2xhdGVZKDApOw0KICB9DQp9DQoNCi5yZWNvbmNpbGlhdGlvbi1hbGVydCB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlNmY3ZmYgMCUsICNiYWU3ZmYgMTAwJSk7DQogIGJvcmRlcjogMnB4IHNvbGlkICMxODkwZmY7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIHBhZGRpbmc6IDE2cHggMjBweDsNCiAgYm94LXNoYWRvdzogMCA0cHggMTZweCByZ2JhKDI0LCAxNDQsIDI1NSwgMC4yKTsNCn0NCg0KLnJlY29uY2lsaWF0aW9uLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDEycHg7DQp9DQoNCi5yZWNvbmNpbGlhdGlvbi1oZWFkZXIgaSB7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgY29sb3I6ICMxODkwZmY7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KfQ0KDQoucmVjb25jaWxpYXRpb24tdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzE4OTBmZjsNCn0NCg0KLnJlY29uY2lsaWF0aW9uLWNvbnRlbnQgew0KICBtYXJnaW46IDA7DQp9DQoNCi5yZWNvbmNpbGlhdGlvbi1pbnN0cnVjdGlvbiB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmY3ZTYgMCUsICNmZmU3YmEgMTAwJSk7DQogIGJvcmRlcjogMnB4IHNvbGlkICNmZmE5NDA7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgcGFkZGluZzogMTJweCAxNnB4Ow0KICBtYXJnaW46IDEycHggMCAhaW1wb3J0YW50Ow0KICBmb250LXNpemU6IDE1cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjY7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDI1NSwgMTY5LCA2NCwgMC4yKTsNCn0NCg0KLmhpZ2hsaWdodC1hbW91bnQgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMDA4MEZGRkYgMCUsICMwNjdFRkZGRiAxMDAlKTsNCiAgY29sb3I6IHdoaXRlOw0KICBwYWRkaW5nOiAycHggOHB4Ow0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBmb250LXNpemU6IDE2cHg7DQogIGJvcmRlcjogMnB4IGRhc2hlZCB3aGl0ZTsNCiAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjMpOw0KICBhbmltYXRpb246IGhpZ2hsaWdodFB1bHNlIDJzIGluZmluaXRlOw0KfQ0KDQpAa2V5ZnJhbWVzIGhpZ2hsaWdodFB1bHNlIHsNCiAgMCUsIDEwMCUgew0KICAgIHRyYW5zZm9ybTogc2NhbGUoMSk7DQogICAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjMpOw0KICB9DQogIDUwJSB7DQogICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsNCiAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjUpOw0KICB9DQp9DQoNCi5oaWdobGlnaHQtYW1vdW50OjphZnRlciB7DQogIGNvbnRlbnQ6ICIg8J+RhiI7DQogIGFuaW1hdGlvbjogYm91bmNlIDEuNXMgaW5maW5pdGU7DQp9DQoNCkBrZXlmcmFtZXMgYm91bmNlIHsNCiAgMCUsIDIwJSwgNTAlLCA4MCUsIDEwMCUgew0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsNCiAgfQ0KICA0MCUgew0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTsNCiAgfQ0KICA2MCUgew0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KTsNCiAgfQ0KfQ0KDQoucmVjb25jaWxpYXRpb24tc3RhdHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGZvbnQtc2l6ZTogMTVweDsNCn0NCg0KLnN0YXRzLXRleHQgew0KICBmbGV4OiAxOw0KfQ0KDQoucmVjb25jaWxlZC1jb3VudCB7DQogIGNvbG9yOiAjMTg5MGZmOw0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQoucmVjb25jaWxlZC1hbW91bnQgew0KICBjb2xvcjogIzUyYzQxYTsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLnRvdGFsLWFtb3VudCB7DQogIGNvbG9yOiAjOGM4YzhjOw0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQoudG90YWwtY291bnQgew0KICBjb2xvcjogIzUyYzQxYTsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLmV4aXQtcmVjb25jaWxpYXRpb24tYnRuIHsNCiAgbWFyZ2luLWxlZnQ6IDE2cHg7DQogIHBhZGRpbmc6IDRweCAxMnB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoMjQ1LCAxMDgsIDEwOCwgMC4zKTsNCn0NCg0KLmV4aXQtcmVjb25jaWxpYXRpb24tYnRuOmhvdmVyIHsNCiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsNCiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDI0NSwgMTA4LCAxMDgsIDAuNSk7DQp9DQoNCi8qIOWvuei0puaooeW8j+S4reWllumHkemineagt+W8jyAqLw0KLnJlY29uY2lsaWF0aW9uLWFtb3VudC1jb250YWluZXIgew0KICBib3JkZXI6IDJweCBkYXNoZWQgIzE4OTBmZjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjBmOWZmIDAlLCAjZTZmN2ZmIDEwMCUpOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG1pbi1oZWlnaHQ6IDQwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQoucmVjb25jaWxpYXRpb24tYW1vdW50LWNvbnRhaW5lcjpob3ZlciB7DQogIGJvcmRlci1jb2xvcjogIzQwYTlmZjsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2U2ZjdmZiAwJSwgI2JhZTdmZiAxMDAlKTsNCiAgdHJhbnNmb3JtOiBzY2FsZSgxLjAyKTsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjMpOw0KfQ0KDQoucmVjb25jaWxpYXRpb24tYW1vdW50LWNvbnRhaW5lci5yZWNvbmNpbGVkIHsNCiAgYm9yZGVyLWNvbG9yOiAjNTJjNDFhOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjZmZmVkIDAlLCAjZDlmN2JlIDEwMCUpOw0KICBib3JkZXItc3R5bGU6IHNvbGlkOw0KfQ0KDQoucmVjb25jaWxpYXRpb24tYW1vdW50LWNvbnRhaW5lci5yZWNvbmNpbGVkOmhvdmVyIHsNCiAgYm9yZGVyLWNvbG9yOiAjNzNkMTNkOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZDlmN2JlIDAlLCAjYjdlYjhmIDEwMCUpOw0KfQ0KDQoucmVjb25jaWxpYXRpb24tYW1vdW50LXRleHQgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogI0ZGMDAzN0ZGOw0KfQ0KDQoucmVjb25jaWxpYXRpb24tYW1vdW50LWNvbnRhaW5lci5yZWNvbmNpbGVkIC5yZWNvbmNpbGlhdGlvbi1hbW91bnQtdGV4dCB7DQogIGNvbG9yOiAjRkYwMDAwRkY7DQp9DQoNCi5yZWNvbmNpbGVkLWljb24gew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogMnB4Ow0KICByaWdodDogMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjRUFGRkVBRkYgIWltcG9ydGFudDsNCiAgY29sb3I6ICM1MmM0MWE7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi5yZWNvbmNpbGlhdGlvbi1zdGF0dXMtYmFkZ2Ugew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogLTFweDsNCiAgcmlnaHQ6IC0xcHg7DQogIGJhY2tncm91bmQ6ICNmZjRkNGY7DQogIGNvbG9yOiB3aGl0ZTsNCiAgZm9udC1zaXplOiAxMHB4Ow0KICBwYWRkaW5nOiAycHggNnB4Ow0KICBib3JkZXItcmFkaXVzOiAwIDZweCAwIDhweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGxpbmUtaGVpZ2h0OiAxOw0KICB6LWluZGV4OiAxMDsNCiAgYm94LXNoYWRvdzogMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4yKTsNCn0NCg0KLnJlY29uY2lsaWF0aW9uLXN0YXR1cy1iYWRnZS5yZWNvbmNpbGVkIHsNCiAgYmFja2dyb3VuZDogIzUyYzQxYTsNCn0NCg0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAih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file": "index.vue", "sourceRoot": "src/views/game/winning", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 移动端搜索按钮 -->\r\n    <div v-if=\"isMobile\" class=\"mobile-search-toggle\">\r\n      <el-button\r\n        type=\"primary\"\r\n        icon=\"el-icon-search\"\r\n        size=\"small\"\r\n        @click=\"toggleMobileSearch\"\r\n        class=\"mobile-search-btn\">\r\n        {{ showMobileSearch ? '收起搜索' : '展开搜索' }}\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-container\" v-show=\"isMobile ? showMobileSearch : showSearch\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"!isMobile\" label-width=\"80px\" :class=\"['search-form', { 'mobile-search-form': isMobile }]\">\r\n        <el-form-item label=\"中奖用户\" prop=\"userId\">\r\n          <el-select\r\n            v-model=\"queryParams.userId\"\r\n            placeholder=\"请选择中奖用户\"\r\n            clearable\r\n            filterable\r\n            @change=\"handleQuery\"\r\n            prefix-icon=\"el-icon-user\"\r\n            :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in userList\"\r\n              :key=\"item.userId\"\r\n              :label=\"item.name\"\r\n              :value=\"item.userId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"流水号\" prop=\"serialNumber\">\r\n          <el-select\r\n            v-model=\"queryParams.serialNumber\"\r\n            placeholder=\"请选择流水号\"\r\n            clearable\r\n            filterable\r\n            @change=\"handleQuery\"\r\n            prefix-icon=\"el-icon-document\"\r\n            :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in serialNumberList\"\r\n              :key=\"item\"\r\n              :label=\"item\"\r\n              :value=\"item\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n      <!-- <el-form-item label=\"彩种名称\" prop=\"lotteryId\">\r\n        <el-select\r\n          v-model=\"queryParams.lotteryId\"\r\n          placeholder=\"请选择彩种\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option label=\"福彩3D\" value=\"1\" />\r\n          <el-option label=\"体彩排三\" value=\"2\" />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"玩法名称\" prop=\"methodId\">\r\n        <el-select\r\n          v-model=\"queryParams.methodId\"\r\n          placeholder=\"请选择玩法名称\"\r\n          clearable\r\n          filterable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in gameMethodsData\"\r\n            :key=\"item.methodId\"\r\n            :label=\"item.methodName\"\r\n            :value=\"item.methodId\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"中奖期号\" prop=\"issueNumber\">\r\n        <el-select\r\n          v-model=\"queryParams.issueNumber\"\r\n          placeholder=\"请选择中奖期号\"\r\n          clearable\r\n          filterable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in issueList\"\r\n            :key=\"item.qihao\"\r\n            :label=\"item.qihao\"\r\n            :value=\"item.qihao\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n\r\n        <el-form-item label=\"识别框\" prop=\"shibie\">\r\n          <el-input\r\n            v-model=\"queryParams.shibie\"\r\n            placeholder=\"请输入识别内容（支持模糊搜索）\"\r\n            clearable\r\n            type=\"textarea\"\r\n            prefix-icon=\"el-icon-search\"\r\n            :style=\"isMobile ? 'width: 100%;' : 'width: 350px;'\"\r\n            @input=\"handleShiBieInput\"\r\n            @clear=\"handleShiBieClear\"\r\n          />\r\n          <div v-if=\"queryParams.shibie && queryParams.shibie.trim()\" class=\"search-tip\">\r\n            <i class=\"el-icon-info\"></i>\r\n            模糊搜索模式：将显示所有包含\"{{ queryParams.shibie.trim() }}\"的记录\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\" class=\"search-btn\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" class=\"reset-btn\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n   <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n       <el-col :span=\"1.5\">\r\n        <el-popconfirm\r\n          title=\"确认要批量赔付选中的奖金吗？\"\r\n          @confirm=\"handleBatchPay\"\r\n        >\r\n          <el-button\r\n            slot=\"reference\"\r\n            type=\"primary\"\r\n            plain\r\n            icon=\"el-icon-s-finance\"\r\n            size=\"mini\"\r\n            :disabled=\"multiple\"\r\n            v-hasPermi=\"['game:winning:edit']\"\r\n          >批量赔付</el-button>\r\n        </el-popconfirm>\r\n      </el-col>\r\n   \r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['game:winning:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['game:winning:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row> -->\r\n\r\n    <!-- 工具栏 -->\r\n    <div class=\"toolbar-container\" :class=\"{ 'mobile-toolbar': isMobile }\">\r\n      <div class=\"toolbar-content\">\r\n        <div class=\"toolbar-left\" :class=\"{ 'mobile-toolbar-left': isMobile }\">\r\n          <el-button\r\n            type=\"primary\"\r\n            :icon=\"allExpanded ? 'el-icon-minus' : 'el-icon-plus'\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"toggleExpandAll\"\r\n            class=\"toolbar-btn expand-btn\"\r\n          >{{ isMobile ? (allExpanded ? '收起' : '展开') : (allExpanded ? '收起全部' : '展开全部') }}</el-button>\r\n          <el-button\r\n            type=\"info\"\r\n            icon=\"el-icon-refresh\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"getList\"\r\n            class=\"toolbar-btn refresh-btn\"\r\n          >{{ isMobile ? '刷新' : '刷新数据' }}</el-button>\r\n          <el-button\r\n            type=\"warning\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"handleExport\"\r\n            v-hasPermi=\"['game:winning:export']\"\r\n            class=\"toolbar-btn export-btn\"\r\n          >导出</el-button>\r\n          <el-button\r\n            :type=\"isReconciliationMode ? 'danger' : 'success'\"\r\n            :icon=\"isReconciliationMode ? 'el-icon-close' : 'el-icon-check'\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"toggleReconciliationMode\"\r\n            :loading=\"reconciliationLoading\"\r\n            class=\"toolbar-btn reconciliation-btn\"\r\n          >{{ isReconciliationMode ? '退出对账' : '对账' }}</el-button>\r\n        </div>\r\n        <div class=\"toolbar-right\">\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 对账模式提示框 (仅PC端显示) -->\r\n    <div v-if=\"isReconciliationMode && !isMobile\" ref=\"reconciliationNotice\" class=\"reconciliation-notice\" :class=\"{ 'reconciliation-fixed': isReconciliationFixed }\">\r\n      <div class=\"reconciliation-alert\">\r\n        <div class=\"reconciliation-header\">\r\n          <i class=\"el-icon-info\"></i>\r\n          <span class=\"reconciliation-title\">对账模式</span>\r\n        </div>\r\n        <div class=\"reconciliation-content\">\r\n          <p class=\"reconciliation-instruction\">\r\n            <strong style=\"color: #ff4d4f;\">操作提示：</strong>点击表格中蓝色虚线框的\r\n            <span class=\"highlight-amount\">\"中奖金额: ￥xxx.xx\"</span>\r\n            区域即可标记该记录为已对账！\r\n          </p>\r\n          <div class=\"reconciliation-stats\">\r\n            <span class=\"stats-text\">\r\n              已对账中奖记录数量：\r\n              <span class=\"reconciled-count\">{{ reconciledWinningIds.length }}</span> /\r\n              <span class=\"total-count\">{{ winningList.length }}</span>\r\n            </span>\r\n            <span class=\"stats-text\" style=\"margin-left: 20px;\">\r\n              已对账中奖金额：\r\n              <span class=\"reconciled-amount\">￥{{ formatAmount(reconciledWinningAmount) }}</span> /\r\n              <span class=\"total-amount\">￥{{ formatAmount(totalWinningAmount) }}</span>\r\n            </span>\r\n            <el-button\r\n              v-if=\"isReconciliationFixed\"\r\n              type=\"danger\"\r\n              size=\"mini\"\r\n              icon=\"el-icon-close\"\r\n              @click=\"exitReconciliationMode\"\r\n              class=\"exit-reconciliation-btn\">\r\n              退出对账\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格 -->\r\n    <div class=\"table-container\" :class=\"{ 'mobile-table-container': isMobile }\">\r\n      <!-- 移动端卡片式布局 -->\r\n      <div v-if=\"isMobile\" class=\"mobile-card-container\" :class=\"{ 'reconciliation-mode': isReconciliationMode }\">\r\n        <!-- 移动端对账模式提示 -->\r\n        <div v-if=\"isReconciliationMode\" ref=\"mobileReconciliationNotice\" class=\"mobile-reconciliation-notice\" :class=\"{ 'mobile-reconciliation-fixed': isReconciliationFixed }\">\r\n          <div class=\"reconciliation-compact\">\r\n            <div class=\"reconciliation-left\">\r\n              <i class=\"el-icon-info\"></i>\r\n              <span class=\"mode-text\">对账模式</span>\r\n              <span class=\"stats-compact\">{{ reconciledWinningIds.length }}/{{ winningList.length }} | ¥{{ reconciledWinningAmount.toFixed(2) }}</span>\r\n            </div>\r\n            <el-button type=\"text\" @click=\"exitReconciliationMode\" class=\"exit-btn-compact\">\r\n              <i class=\"el-icon-close\"></i>\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 移动端卡片列表 -->\r\n        <div v-for=\"(item, index) in winningList\" :key=\"item.winId\" class=\"mobile-card\"\r\n             :class=\"{ 'reconciled': isReconciliationMode && isWinningReconciled(item.winId) }\"\r\n             @click=\"!isReconciliationMode ? toggleMobileCard(item, index) : null\">\r\n\r\n          <!-- 卡片头部 -->\r\n          <div class=\"card-header\">\r\n            <!-- 左侧点击区域：展开/收起卡片 -->\r\n            <div class=\"card-header-left\" @click=\"isReconciliationMode ? toggleMobileCard(item, index) : null\">\r\n              <div class=\"winning-info\">\r\n                <div class=\"user-name\">{{ getUserName(item.userId) }}</div>\r\n                <div class=\"winning-meta\">\r\n                  <span class=\"serial-number\">流水号: {{ item.serialNumber }}</span>\r\n                  <span class=\"lottery-type\">\r\n                    <el-tag v-if=\"item.lotteryId === 1\" type=\"danger\" size=\"mini\">福彩</el-tag>\r\n                    <el-tag v-else-if=\"item.lotteryId === 2\" type=\"primary\" size=\"mini\">体彩</el-tag>\r\n                    <span v-else>{{ item.lotteryId }}</span>\r\n                  </span>\r\n                  <span class=\"method-name\">{{ getMethodName(item.methodId) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右侧点击区域：对账操作（仅在对账模式下可点击） -->\r\n            <div class=\"card-header-right\"\r\n                 :class=\"{ 'reconciliation-clickable': isReconciliationMode }\"\r\n                 @click=\"isReconciliationMode ? toggleWinningReconciliation(item.winId) : null\">\r\n              <div class=\"amount-info\">\r\n                <div class=\"win-amount\">{{ formatAmount(item.winAmount) }}</div>\r\n                <div v-if=\"isReconciliationMode\" class=\"reconciliation-status\"\r\n                     :class=\"{ 'reconciled': isWinningReconciled(item.winId) }\">\r\n                  {{ isWinningReconciled(item.winId) ? '已对账' : '待对账' }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 卡片内容（展开时显示） -->\r\n          <div v-if=\"item.mobileExpanded\" class=\"card-content\">\r\n            <div v-if=\"item.shibie\" class=\"detail-row\">\r\n              <span class=\"detail-label\">识别框:</span>\r\n              <span class=\"detail-value\">{{ item.shibie }}</span>\r\n            </div>\r\n            <div class=\"detail-row\">\r\n              <span class=\"detail-label\">下注号码:</span>\r\n              <div class=\"detail-value\">\r\n                <div v-if=\"item.betNumber\">\r\n                  <!-- 胆拖玩法显示 -->\r\n                  <div v-if=\"item.methodId >= 44 && item.methodId <= 59\">\r\n                    <template v-if=\"JSON.parse(item.betNumber).numbers.length > 0\">\r\n                      <el-tag type=\"danger\" size=\"mini\" style=\"margin: 2px;\">\r\n                        胆{{ JSON.parse(item.betNumber).numbers[0].danma }}\r\n                      </el-tag>\r\n                      <el-tag size=\"mini\" style=\"margin: 2px;\">\r\n                        拖{{ JSON.parse(item.betNumber).numbers[0].tuoma }}\r\n                      </el-tag>\r\n                    </template>\r\n                  </div>\r\n                  <!-- 跨度玩法显示 -->\r\n                  <div v-else-if=\"item.methodId >= 60 && item.methodId <= 69\">\r\n                    <el-tag size=\"mini\" style=\"margin: 2px;\">跨度{{ item.methodId - 60 }}</el-tag>\r\n                  </div>\r\n                  <!-- 其他玩法显示 -->\r\n                  <div v-else>\r\n                    <template v-if=\"typeof item.betNumber === 'string'\">\r\n                      <div v-for=\"(num, numIndex) in JSON.parse(item.betNumber).numbers\" :key=\"numIndex\" style=\"display: inline-block; margin: 2px;\">\r\n                        <el-tag type=\"primary\" size=\"mini\" style=\"margin: 1px; font-weight: bold;\">\r\n                          {{ Object.values(num).join('.') }}\r\n                        </el-tag>\r\n                      </div>\r\n                    </template>\r\n                    <span v-else>{{ item.betNumbers }}</span>\r\n                  </div>\r\n                </div>\r\n                <span v-else>{{ item.betNumbers }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"detail-row\">\r\n              <span class=\"detail-label\">中奖号码:</span>\r\n              <div class=\"detail-value\">\r\n                <div v-if=\"item.winningNumbers\">\r\n                  <template v-if=\"typeof item.winningNumbers === 'string'\">\r\n                    <div v-for=\"(winItem, winIndex) in JSON.parse(item.winningNumbers).winning\" :key=\"winIndex\">\r\n                      <el-tag type=\"success\" size=\"mini\" style=\"margin: 1px;\">\r\n                        <template v-if=\"winItem.b === undefined && winItem.c === undefined\">\r\n                          {{ winItem.a }}\r\n                        </template>\r\n                        <template v-else-if=\"winItem.c === undefined\">\r\n                          {{ winItem.a }}.{{ winItem.b }}\r\n                        </template>\r\n                        <template v-else>\r\n                          {{ winItem.a }}.{{ winItem.b }}.{{ winItem.c }}\r\n                        </template>\r\n                      </el-tag>\r\n                    </div>\r\n                  </template>\r\n                  <span v-else>{{ item.winningNumbers }}</span>\r\n                </div>\r\n                <span v-else>{{ item.winningNumbers }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 桌面端表格 -->\r\n      <el-table v-else\r\n        v-loading=\"loading\"\r\n        :data=\"winningList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        @row-click=\"handleRowClick\"\r\n        ref=\"winningTable\"\r\n        border\r\n        :header-cell-style=\"{ background: '#f8f9fa', color: '#606266', fontWeight: 'bold' }\"\r\n      >\r\n        <el-table-column type=\"expand\" width=\"60\">\r\n          <template slot-scope=\"props\">\r\n            <div class=\"expand-content\">\r\n              <div class=\"expand-header\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>详细信息</span>\r\n              </div>\r\n              <el-form label-position=\"left\" inline class=\"table-expand\">\r\n                <el-form-item label=\"号码识别\" class=\"full-width\">\r\n                  <div class=\"expand-value\">{{ props.row.shibie || '无' }}</div>\r\n                </el-form-item>\r\n                <el-form-item label=\"下注号码\" class=\"full-width\">\r\n                  <div class=\"expand-value\">\r\n                    <div v-if=\"props.row.betNumber\" class=\"number-tags\">\r\n                      <!-- 胆拖玩法详情显示 -->\r\n                      <div v-if=\"props.row.methodId >= 44 && props.row.methodId <= 59\">\r\n                        <template v-if=\"JSON.parse(props.row.betNumber).numbers.length > 0\">\r\n                          <el-tag type=\"danger\" effect=\"dark\" size=\"medium\" style=\"margin: 4px; font-size: 16px; font-weight: bold;\">\r\n                            胆码: {{ JSON.parse(props.row.betNumber).numbers[0].danma }}\r\n                          </el-tag>\r\n                          <el-tag type=\"info\" effect=\"dark\" size=\"medium\" style=\"margin: 4px; font-size: 16px; font-weight: bold;\">\r\n                            拖码: {{ JSON.parse(props.row.betNumber).numbers[0].tuoma }}\r\n                          </el-tag>\r\n                        </template>\r\n                      </div>\r\n\r\n                      <!-- 跨度玩法详情显示 -->\r\n                      <div v-else-if=\"props.row.methodId >= 60 && props.row.methodId <= 69\">\r\n                        <el-tag type=\"warning\" effect=\"dark\" size=\"medium\" style=\"margin: 4px; font-size: 16px; font-weight: bold;\">\r\n                          跨度值: {{ props.row.methodId - 60 }}\r\n                        </el-tag>\r\n                      </div>\r\n\r\n                      <!-- 原有玩法详情显示 -->\r\n                      <div v-else>\r\n                        <el-tag\r\n                          v-for=\"(item, index) in JSON.parse(props.row.betNumber).numbers\"\r\n                          :key=\"index\"\r\n                          type=\"primary\"\r\n                          effect=\"dark\"\r\n                          size=\"small\"\r\n                          class=\"number-tag bet-tag\"\r\n                        >\r\n                          {{ Object.values(item).join('.') }}\r\n                        </el-tag>\r\n                      </div>\r\n                    </div>\r\n                    <span v-else class=\"no-data\">{{ props.row.betNumbers || '无' }}</span>\r\n                  </div>\r\n                </el-form-item>\r\n                <el-form-item label=\"中奖号码\" class=\"full-width\">\r\n                  <div class=\"expand-value\">\r\n                    <div v-if=\"props.row.winningNumbers\" class=\"number-tags\">\r\n                      <template v-if=\"typeof props.row.winningNumbers === 'string'\">\r\n                        <el-tag\r\n                          v-for=\"(item, index) in JSON.parse(props.row.winningNumbers).winning\"\r\n                          :key=\"index\"\r\n                          type=\"success\"\r\n                          effect=\"dark\"\r\n                          size=\"small\"\r\n                          class=\"number-tag winning-tag\"\r\n                        >\r\n                          <template v-if=\"item.b === undefined && item.c === undefined\">\r\n                            {{ item.a }}\r\n                          </template>\r\n                          <template v-else-if=\"item.c === undefined\">\r\n                            {{ item.a }}.{{ item.b }}\r\n                          </template>\r\n                          <template v-else>\r\n                            {{ item.a }}.{{ item.b }}.{{ item.c }}\r\n                          </template>\r\n                        </el-tag>\r\n                      </template>\r\n                      <template v-else>\r\n                        <span class=\"no-data\">{{ props.row.winningNumbers }}</span>\r\n                      </template>\r\n                    </div>\r\n                    <span v-else class=\"no-data\">{{ props.row.winningNumbers || '无' }}</span>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </template>\r\n      </el-table-column>\r\n        <el-table-column label=\"中奖用户\" align=\"center\" prop=\"userId\" min-width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"user-info\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>{{ getUserName(scope.row.userId) }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"彩种\" align=\"center\" prop=\"lotteryId\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"lottery-type\">\r\n              <el-tag v-if=\"scope.row.lotteryId === 1\" type=\"danger\" effect=\"dark\" size=\"small\" class=\"lottery-tag\">福彩</el-tag>\r\n              <el-tag v-else-if=\"scope.row.lotteryId === 2\" type=\"primary\" effect=\"dark\" size=\"small\" class=\"lottery-tag\">体彩</el-tag>\r\n              <span v-else class=\"lottery-unknown\">{{ scope.row.lotteryId }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"玩法名称\" align=\"center\" prop=\"methodId\" min-width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"method-info\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n              <span>{{ getMethodName(scope.row.methodId) }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      <el-table-column label=\"下注号码\" align=\"center\" prop=\"betNumber\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.betNumber\">\r\n            <!-- 胆拖玩法显示 (method_id 44-59) -->\r\n            <div v-if=\"scope.row.methodId >= 44 && scope.row.methodId <= 59\" style=\"white-space: nowrap;\">\r\n              <template v-if=\"JSON.parse(scope.row.betNumber).numbers.length > 0\">\r\n                <el-tag type=\"danger\" effect=\"dark\" size=\"mini\" style=\"margin: 1px; font-weight: bold;\">\r\n                  胆{{ JSON.parse(scope.row.betNumber).numbers[0].danma }}\r\n                </el-tag>\r\n                <el-tag effect=\"dark\" size=\"mini\" style=\"margin: 1px; font-weight: bold;\">\r\n                  拖{{ JSON.parse(scope.row.betNumber).numbers[0].tuoma }}\r\n                </el-tag>\r\n              </template>\r\n            </div>\r\n\r\n            <!-- 跨度玩法显示 (method_id 60-69) -->\r\n            <div v-else-if=\"scope.row.methodId >= 60 && scope.row.methodId <= 69\" style=\"white-space: nowrap;\">\r\n              <el-tag  effect=\"dark\" size=\"mini\" style=\"margin: 1px; font-weight: bold;\">\r\n                跨度{{ scope.row.methodId - 60 }}\r\n              </el-tag>\r\n            </div>\r\n\r\n            <!-- 原有玩法显示保持不变 -->\r\n            <div v-else style=\"white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">\r\n              <div v-for=\"(item, index) in JSON.parse(scope.row.betNumber).numbers\" :key=\"index\" style=\"display: inline-block;\">\r\n                <el-tag\r\n                  type=\"primary\"\r\n                  effect=\"dark\"\r\n                  size=\"small\"\r\n                  style=\"margin: 2px; font-weight: bold;\"\r\n                >\r\n                  {{ Object.values(item).join('.') }}\r\n                </el-tag>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <span v-else style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.betNumbers }}</span>\r\n        </template>\r\n      </el-table-column>\r\n         <el-table-column label=\"中奖号码\" align=\"center\" prop=\"winningNumbers\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.winningNumbers\" style=\"white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">\r\n            <template v-if=\"typeof scope.row.winningNumbers === 'string'\">\r\n              <div v-for=\"(item, index) in JSON.parse(scope.row.winningNumbers).winning\" :key=\"index\"\r\n                style=\"display: inline-block;\">\r\n                <el-tag type=\"success\" effect=\"dark\" size=\"small\" style=\"margin: 2px; font-weight: bold;\">\r\n                  <template v-if=\"item.b === undefined && item.c === undefined\">\r\n                    {{ item.a }}\r\n                  </template>\r\n                  <template v-else-if=\"item.c === undefined\">\r\n                    {{ item.a }}.{{ item.b }}\r\n                  </template>\r\n                  <template v-else>\r\n                    {{ item.a }}.{{ item.b }}.{{ item.c }}\r\n                  </template>\r\n                </el-tag>\r\n              </div>\r\n            </template>\r\n            <template v-else>\r\n              <span style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.winningNumbers }}</span>\r\n            </template>\r\n          </div>\r\n          <span v-else style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.winningNumbers }}</span>\r\n        </template>\r\n      </el-table-column>\r\n    \r\n        <el-table-column label=\"流水号\" align=\"center\" prop=\"serialNumber\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"serial-number\">\r\n              <span class=\"serial-value\">{{ scope.row.serialNumber }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"中奖金额\" align=\"center\" prop=\"winAmount\" min-width=\"120\" sortable :sort-method=\"sortByAmount\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"isReconciliationMode\"\r\n                 class=\"reconciliation-amount-container\"\r\n                 :class=\"{ 'reconciled': isWinningReconciled(scope.row.winId) }\"\r\n                 @click.stop=\"toggleWinningReconciliation(scope.row.winId)\">\r\n              <span class=\"reconciliation-amount-text\">\r\n                中奖金额: {{ formatAmount(scope.row.winAmount) }}\r\n              </span>\r\n              <span class=\"reconciliation-status-badge\"\r\n                    :class=\"{ 'reconciled': isWinningReconciled(scope.row.winId) }\">\r\n                {{ isWinningReconciled(scope.row.winId) ? '已对账' : '待对账' }}\r\n              </span>\r\n            </div>\r\n            <span v-else class=\"amount-simple\">{{ formatAmount(scope.row.winAmount) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      <!-- <el-table-column label=\"是否赔付\" align=\"center\" prop=\"isPaid\">\r\n        <template slot-scope=\"scope\">\r\n          <span style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.isPaid === 1 ? '是' : '否' }}</span>\r\n        </template>\r\n      </el-table-column> -->\r\n   \r\n      <!-- <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popconfirm\r\n            title=\"确认要赔付该笔奖金吗？\"\r\n            @confirm=\"handlePay(scope.row)\"\r\n            v-if=\"scope.row.isPaid === 0\"\r\n          >\r\n            <el-button\r\n              slot=\"reference\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-s-finance\"\r\n              style=\"font-size: 14px; font-weight: bold;\"\r\n              v-hasPermi=\"['game:winning:edit']\"\r\n            >赔付</el-button>\r\n          </el-popconfirm>\r\n          <span v-else style=\"color: #56575AFF; font-size: 14px; font-weight: 700;\">已赔付</span>\r\n        </template>\r\n      </el-table-column> -->\r\n      </el-table>\r\n    </div>\r\n\r\n    <pagination\r\n      v-show=\"total > 0 && !isReconciliationMode\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改中奖管理对话框 -->\r\n    <!-- <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body :close-on-click-modal=\"false\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"中奖用户\" prop=\"userId\">\r\n          <el-input v-model=\"form.userId\" placeholder=\"请输入中奖用户\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"玩法名称\" prop=\"methodId\">\r\n          <el-input v-model=\"form.methodId\" placeholder=\"请输入玩法名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"下注号码\" prop=\"betNumber\">\r\n          <el-input v-model=\"form.betNumber\" placeholder=\"请输入下注号码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"彩种名称\" prop=\"lotteryId\">\r\n          <el-input v-model=\"form.lotteryId\" placeholder=\"请输入彩种名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"中奖期号\" prop=\"issueNumber\">\r\n          <el-input v-model=\"form.issueNumber\" placeholder=\"请输入中奖期号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"中奖金额\" prop=\"winAmount\">\r\n          <el-input v-model=\"form.winAmount\" placeholder=\"请输入中奖金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否赔付\" prop=\"isPaid\">\r\n          <el-input v-model=\"form.isPaid\" placeholder=\"请输入是否赔付\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"中奖号码\" prop=\"winningNumbers\">\r\n          <el-input v-model=\"form.winningNumbers\" placeholder=\"请输入中奖号码\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listWinning, getWinning, delWinning, addWinning, updateWinning, exportWinning } from \"@/api/game/winning\"\r\nimport { listCustomer } from \"@/api/game/customer\"\r\nimport { mapState } from 'vuex'\r\nimport { listUser } from \"@/api/system/user\"\r\nimport { getAllMethods } from \"@/api/game/method\"\r\nimport { listDraw } from \"@/api/game/draw\"\r\nimport { parseTime } from '@/utils/ruoyi'\r\nimport { cancelPay } from \"@/api/game/winning\"\r\nimport { listSerial } from \"@/api/game/serial\"\r\n\r\nexport default {\r\n  name: \"Winning\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 中奖管理表格数据\r\n      winningList: [],\r\n      // 是否全部展开\r\n      allExpanded: false,\r\n      // 移动端相关\r\n      isMobile: false,\r\n      showMobileSearch: false,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 用户列表\r\n      userList: [],\r\n      // 期号列表\r\n      issueList: [],\r\n      // 流水号列表\r\n      serialNumberList: [],\r\n      // 识别框搜索防抖定时器\r\n      shiBieSearchTimer: null,\r\n      // 对账模式相关\r\n      isReconciliationMode: false,\r\n      reconciliationLoading: false,\r\n      reconciledWinningIds: [], // 已对账的中奖记录ID数组（响应式）\r\n      originalQueryParams: null, // 保存原始查询参数\r\n      isReconciliationFixed: false, // 对账提示框是否固定在顶部\r\n      reconciliationOriginalTop: 0, // 对账提示框原始位置\r\n      scrollDebounceTimer: null, // 滚动防抖定时器\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        userId: null,\r\n        methodId: null,\r\n        betNumbers: null,\r\n        lotteryId: null,\r\n        issueNumber: null,\r\n        winAmount: null,\r\n        isPaid: null,\r\n        winningNumbers: null,\r\n        shibie: null,\r\n        serialNumber: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userId: [\r\n          { required: true, message: \"中奖用户不能为空\", trigger: \"blur\" }\r\n        ],\r\n        methodId: [\r\n          { required: true, message: \"玩法名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        lotteryId: [\r\n          { required: true, message: \"彩种名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        issueNumber: [\r\n          { required: true, message: \"中奖期号不能为空\", trigger: \"blur\" }\r\n        ],\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      gameMethodsData: state => state.game.methodsData\r\n    }),\r\n    /** 计算已对账的中奖金额总和 */\r\n    reconciledWinningAmount() {\r\n      if (!this.isReconciliationMode || !this.winningList || this.winningList.length === 0) {\r\n        return 0;\r\n      }\r\n\r\n      return this.winningList\r\n        .filter(winning => this.isWinningReconciled(winning.winId))\r\n        .reduce((total, winning) => {\r\n          const amount = parseFloat(winning.winAmount) || 0;\r\n          return total + amount;\r\n        }, 0);\r\n    },\r\n    /** 计算所有中奖金额总和 */\r\n    totalWinningAmount() {\r\n      if (!this.winningList || this.winningList.length === 0) {\r\n        return 0;\r\n      }\r\n\r\n      return this.winningList.reduce((total, winning) => {\r\n        const amount = parseFloat(winning.winAmount) || 0;\r\n        return total + amount;\r\n      }, 0);\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化移动端检测\r\n    this.initMobileDetection();\r\n\r\n    this.getList()\r\n    this.getCustomerList()\r\n    this.getGameMethods()\r\n    this.getIssueList()\r\n    this.getSerialNumberList()\r\n    // 恢复对账模式状态\r\n    this.restoreReconciliationState()\r\n  },\r\n  mounted() {\r\n    // 监听滚动事件\r\n    window.addEventListener('scroll', this.handleScroll);\r\n    // 监听一键清空事件\r\n    window.addEventListener('clearAllData', this.handleClearAllData);\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize);\r\n  },\r\n  beforeDestroy() {\r\n    // 移除滚动监听\r\n    window.removeEventListener('scroll', this.handleScroll);\r\n    // 移除一键清空事件监听\r\n    window.removeEventListener('clearAllData', this.handleClearAllData);\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.handleResize);\r\n\r\n    // 清理防抖定时器\r\n    if (this.scrollDebounceTimer) {\r\n      clearTimeout(this.scrollDebounceTimer);\r\n      this.scrollDebounceTimer = null;\r\n    }\r\n  },\r\n  methods: {\r\n    /** 初始化移动端检测 */\r\n    initMobileDetection() {\r\n      if (typeof window !== 'undefined') {\r\n        this.isMobile = window.innerWidth <= 768;\r\n      }\r\n    },\r\n\r\n    /** 处理窗口大小变化 */\r\n    handleResize() {\r\n      if (typeof window !== 'undefined') {\r\n        const newIsMobile = window.innerWidth <= 768;\r\n        if (this.isMobile !== newIsMobile) {\r\n          this.isMobile = newIsMobile;\r\n          // 在移动端和桌面端切换时，重置搜索框状态\r\n          if (!newIsMobile && this.showMobileSearch) {\r\n            this.showMobileSearch = false;\r\n          }\r\n          // 强制重新渲染以适应新的屏幕尺寸\r\n          this.$nextTick(() => {\r\n            this.$forceUpdate();\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 切换移动端搜索框显示 */\r\n    toggleMobileSearch() {\r\n      this.showMobileSearch = !this.showMobileSearch;\r\n    },\r\n\r\n    /** 切换移动端卡片展开状态 */\r\n    toggleMobileCard(item, index) {\r\n      this.$set(item, 'mobileExpanded', !item.mobileExpanded);\r\n    },\r\n\r\n    /** 退出对账模式 */\r\n    exitReconciliationMode() {\r\n      this.toggleReconciliationMode();\r\n    },\r\n\r\n    /** 获取期号列表 */\r\n    getIssueList() {\r\n      listDraw().then(response => {\r\n        this.issueList = response.rows\r\n      })\r\n    },\r\n    /** 获取用户列表 */\r\n    getCustomerList() {\r\n      listCustomer().then(response => {\r\n        this.userList = response.rows\r\n      })\r\n    },\r\n    /** 查询中奖管理列表 */\r\n    getList() {\r\n      this.loading = true\r\n      const params = { ...this.queryParams };\r\n\r\n      // 如果是对账模式，获取所有数据\r\n      if (this.isReconciliationMode) {\r\n        params.pageNum = 1;\r\n        params.pageSize = 999999;\r\n      }\r\n\r\n      // 记录是否是识别框搜索\r\n      const isShiBieSearch = params.shibie && params.shibie.trim() !== '';\r\n\r\n      // 如果有识别框搜索内容\r\n      if (isShiBieSearch) {\r\n        // 去除首尾空格\r\n        params.shibie = params.shibie.trim();\r\n        // 对于识别框搜索，不传递给后端，而是获取所有数据进行前端过滤\r\n        delete params.shibie;\r\n      }\r\n\r\n      listWinning(params).then(response => {\r\n        let filteredRows = response.rows;\r\n\r\n        // 如果有识别框搜索内容且有返回结果，进行前端过滤\r\n        if (isShiBieSearch && filteredRows.length > 0) {\r\n          const searchText = this.queryParams.shibie.trim();\r\n\r\n          // 模糊匹配：包含搜索文本的所有记录\r\n          filteredRows = filteredRows.filter(row =>\r\n            row.shibie && row.shibie.includes(searchText)\r\n          );\r\n\r\n          // 更新总数为过滤后的数量\r\n          response.total = filteredRows.length;\r\n        }\r\n\r\n        this.winningList = filteredRows;\r\n        this.total = response.total;\r\n\r\n        // 如果是对账模式，强制正序排序\r\n        if (this.isReconciliationMode) {\r\n          this.sortWinningListForReconciliation();\r\n        }\r\n\r\n        this.loading = false;\r\n\r\n        // 如果是识别框搜索，自动展开所有搜索结果\r\n        if (isShiBieSearch) {\r\n          this.$nextTick(() => {\r\n            this.allExpanded = true;\r\n            this.winningList.forEach((row) => {\r\n              this.$refs.winningTable.toggleRowExpansion(row, true);\r\n            });\r\n          });\r\n        } else {\r\n          // 非识别框搜索，重置展开状态\r\n          this.resetExpandState();\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        winId: null,\r\n        userId: null,\r\n        methodId: null,\r\n        betNumber: null,\r\n        lotteryId: null,\r\n        issueNumber: null,\r\n        winAmount: null,\r\n        isPaid: null,\r\n        winningNumbers: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.winId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 处理表格行点击 */\r\n    handleRowClick(row, column, event) {\r\n      // 避免点击操作按钮时触发行展开\r\n      if (column && column.type === 'selection') {\r\n        return;\r\n      }\r\n\r\n      // 在对账模式下，如果点击的是中奖金额列，不展开行\r\n      if (this.isReconciliationMode && column && column.property === 'winAmount') {\r\n        return;\r\n      }\r\n\r\n      console.log('中奖记录表格行点击:', row.winId || 'unknown');\r\n\r\n      // 切换行的展开状态\r\n      if (this.$refs.winningTable) {\r\n        this.$refs.winningTable.toggleRowExpansion(row);\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加中奖管理\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const winId = row.winId || this.ids\r\n      getWinning(winId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改中奖管理\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.winId != null) {\r\n            updateWinning(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addWinning(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const winIds = row.winId || this.ids\r\n      this.$modal.confirm('是否确认删除中奖管理编号为\"' + winIds + '\"的数据项？').then(function() {\r\n        return delWinning(winIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出所有中奖管理数据项？').then(() => {\r\n        this.exportLoading = true\r\n        // 获取所有数据用于导出\r\n        const exportParams = {\r\n          ...this.queryParams,\r\n          pageNum: 1,\r\n          pageSize: 10000 // 获取大量数据\r\n        }\r\n\r\n        listWinning(exportParams).then(response => {\r\n          const data = response.rows\r\n          const formattedData = this.formatDataForExport(data)\r\n          this.exportToExcel(formattedData)\r\n        }).catch(() => {\r\n          this.$modal.msgError(\"导出失败\")\r\n        }).finally(() => {\r\n          this.exportLoading = false\r\n        })\r\n      }).catch(() => {})\r\n    },\r\n\r\n    /** 格式化导出数据，与页面显示保持一致 */\r\n    formatDataForExport(data) {\r\n      return data.map(item => {\r\n        // 格式化下注号码\r\n        let betNumbersFormatted = ''\r\n        if (item.betNumber) {\r\n          try {\r\n            const betData = JSON.parse(item.betNumber)\r\n            if (betData.numbers && Array.isArray(betData.numbers)) {\r\n              // 胆拖玩法格式化 (method_id 44-59)\r\n              if (item.methodId >= 44 && item.methodId <= 59) {\r\n                if (betData.numbers.length > 0) {\r\n                  const firstNum = betData.numbers[0]\r\n                  betNumbersFormatted = `胆${firstNum.danma} 拖${firstNum.tuoma}`\r\n                }\r\n              }\r\n              // 跨度玩法格式化 (method_id 60-69)\r\n              else if (item.methodId >= 60 && item.methodId <= 69) {\r\n                betNumbersFormatted = `跨度${item.methodId - 60}`\r\n              }\r\n              // 原有玩法格式化\r\n              else {\r\n                betNumbersFormatted = betData.numbers.map(num => Object.values(num).join('.')).join(', ')\r\n              }\r\n            }\r\n          } catch (e) {\r\n            betNumbersFormatted = item.betNumbers || ''\r\n          }\r\n        } else {\r\n          betNumbersFormatted = item.betNumbers || ''\r\n        }\r\n\r\n        // 格式化中奖号码\r\n        let winningNumbersFormatted = ''\r\n        if (item.winningNumbers) {\r\n          try {\r\n            if (typeof item.winningNumbers === 'string') {\r\n              const winData = JSON.parse(item.winningNumbers)\r\n              if (winData.winning && Array.isArray(winData.winning)) {\r\n                winningNumbersFormatted = winData.winning.map(num => {\r\n                  if (num.b === undefined && num.c === undefined) {\r\n                    return num.a\r\n                  } else if (num.c === undefined) {\r\n                    return `${num.a}.${num.b}`\r\n                  } else {\r\n                    return `${num.a}.${num.b}.${num.c}`\r\n                  }\r\n                }).join(', ')\r\n              }\r\n            } else {\r\n              winningNumbersFormatted = item.winningNumbers\r\n            }\r\n          } catch (e) {\r\n            winningNumbersFormatted = item.winningNumbers || ''\r\n          }\r\n        }\r\n\r\n        // 格式化中奖金额（去掉￥符号）\r\n        const formatAmountForExport = (amount) => {\r\n          if (!amount && amount !== 0) return '0.00'\r\n          const num = parseFloat(amount)\r\n          return num.toFixed(2)\r\n        }\r\n\r\n        return {\r\n          '中奖用户': this.getUserName(item.userId),\r\n          '彩种': this.getLotteryName(item.lotteryId),\r\n          '玩法名称': this.getMethodName(item.methodId),\r\n          '下注号码': betNumbersFormatted,\r\n          '中奖号码': winningNumbersFormatted,\r\n          '流水号': item.serialNumber,\r\n          '中奖金额': formatAmountForExport(item.winAmount),\r\n          '号码识别': item.shibie || ''\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出到Excel */\r\n    exportToExcel(data) {\r\n      import('@/utils/Export2Excel').then(excel => {\r\n        const tHeader = ['中奖用户', '彩种', '玩法名称', '下注号码', '中奖号码', '流水号', '中奖金额', '号码识别']\r\n        const filterVal = ['中奖用户', '彩种', '玩法名称', '下注号码', '中奖号码', '流水号', '中奖金额', '号码识别']\r\n        const exportData = data.map(v => filterVal.map(j => v[j]))\r\n\r\n        excel.export_json_to_excel({\r\n          header: tHeader,\r\n          data: exportData,\r\n          filename: `中奖管理_${this.parseTime(new Date(), '{y}{m}{d}_{h}{i}{s}')}`,\r\n          autoWidth: true,\r\n          bookType: 'xlsx'\r\n        })\r\n\r\n        this.$modal.msgSuccess(\"导出成功\")\r\n      }).catch(() => {\r\n        this.$modal.msgError(\"导出失败\")\r\n      })\r\n    },\r\n    /** 获取玩法名称 */\r\n    getMethodName(methodId) {\r\n      if (!this.gameMethodsData || !this.gameMethodsData.length) {\r\n        return methodId\r\n      }\r\n      const method = this.gameMethodsData.find(item => Number(item.methodId) === Number(methodId))\r\n      return method ? method.methodName : methodId\r\n    },\r\n    /** 获取用户名称 */\r\n    getUserName(userId) {\r\n      if (!this.userList || !this.userList.length) {\r\n        return userId\r\n      }\r\n      const user = this.userList.find(item => Number(item.userId) === Number(userId))\r\n      return user ? user.name : userId\r\n    },\r\n    /** 获取彩种名称 */\r\n    getLotteryName(lotteryId) {\r\n      const lotteryMap = {\r\n        '1': '福彩3D',\r\n        '2': '体彩排三'\r\n      }\r\n      return lotteryMap[lotteryId] || lotteryId\r\n    },\r\n    /** 处理赔付 */\r\n    handlePay(row) {\r\n      updateWinning({\r\n        winId: row.winId,\r\n        isPaid: 1\r\n      }).then(response => {\r\n        this.$modal.msgSuccess(\"赔付成功\")\r\n        this.getList()\r\n      })\r\n    },\r\n    /** 批量赔付 */\r\n    handleBatchPay() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请选择要赔付的记录\")\r\n        return\r\n      }\r\n      this.loading = true\r\n      const promises = this.ids.map(winId => \r\n        updateWinning({\r\n          winId: winId,\r\n          isPaid: 1\r\n        })\r\n      )\r\n      Promise.all(promises).then(() => {\r\n        this.$modal.msgSuccess(\"批量赔付成功\")\r\n        this.getList()\r\n      }).catch(error => {\r\n        this.$modal.msgError(\"批量赔付失败：\" + (error.message || \"未知错误\"))\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    formatAmount(amount) {\r\n      if (amount === null || amount === undefined) {\r\n        return '￥0.00'\r\n      }\r\n      return '￥' + parseFloat(amount).toFixed(2)\r\n    },\r\n    /** 切换对账模式 */\r\n    toggleReconciliationMode() {\r\n      if (!this.isReconciliationMode) {\r\n        // 进入对账模式\r\n        this.enterReconciliationMode();\r\n      } else {\r\n        // 退出对账模式\r\n        this.exitReconciliationMode();\r\n      }\r\n    },\r\n    /** 进入对账模式 */\r\n    enterReconciliationMode() {\r\n      // 保存原始查询参数\r\n      this.originalQueryParams = { ...this.queryParams };\r\n\r\n      // 清空已对账的中奖记录ID数组\r\n      this.reconciledWinningIds = [];\r\n\r\n      // 重置固定状态和防抖定时器\r\n      this.isReconciliationFixed = false;\r\n      this.reconciliationOriginalTop = 0;\r\n      if (this.scrollDebounceTimer) {\r\n        clearTimeout(this.scrollDebounceTimer);\r\n        this.scrollDebounceTimer = null;\r\n      }\r\n\r\n      // 设置为获取所有数据（不分页）\r\n      const allDataParams = {\r\n        ...this.queryParams,\r\n        pageNum: 1,\r\n        pageSize: 999999 // 设置一个很大的数值来获取所有数据\r\n      };\r\n\r\n      this.loading = true;\r\n\r\n      listWinning(allDataParams).then(response => {\r\n        this.winningList = response.rows;\r\n        this.total = response.rows.length;\r\n\r\n        // 强制正序排序（对账模式下按winId正序）\r\n        this.sortWinningListForReconciliation();\r\n\r\n        // 设置对账模式\r\n        this.isReconciliationMode = true;\r\n\r\n        // 保存对账状态\r\n        this.saveReconciliationState();\r\n\r\n        this.loading = false;\r\n        this.reconciliationLoading = false;\r\n\r\n        this.$modal.msgSuccess(`已进入对账模式，共加载 ${response.rows.length} 条中奖记录，按流水号正序排列显示`);\r\n      }).catch(error => {\r\n        this.loading = false;\r\n        this.reconciliationLoading = false;\r\n        this.$modal.msgError('进入对账模式失败：' + error.message);\r\n      });\r\n    },\r\n    /** 退出对账模式 */\r\n    exitReconciliationMode() {\r\n      // 恢复原始查询参数\r\n      if (this.originalQueryParams) {\r\n        this.queryParams = { ...this.originalQueryParams };\r\n      }\r\n\r\n      // 清空已对账的中奖记录ID数组\r\n      this.reconciledWinningIds = [];\r\n\r\n      // 重置固定状态和清理定时器\r\n      this.isReconciliationFixed = false;\r\n      this.reconciliationOriginalTop = 0;\r\n      if (this.scrollDebounceTimer) {\r\n        clearTimeout(this.scrollDebounceTimer);\r\n        this.scrollDebounceTimer = null;\r\n      }\r\n\r\n      // 设置对账模式为false\r\n      this.isReconciliationMode = false;\r\n\r\n      // 清除对账状态\r\n      this.clearReconciliationState();\r\n\r\n      // 重新加载数据\r\n      this.getList();\r\n\r\n      this.$modal.msgSuccess('已退出对账模式');\r\n    },\r\n    /** 对账模式下的排序（强制正序） */\r\n    sortWinningListForReconciliation() {\r\n      if (!this.winningList || this.winningList.length === 0) {\r\n   \r\n        return;\r\n      }\r\n\r\n   \r\n\r\n      // 对中奖记录按流水号正序排序\r\n      this.winningList.sort((a, b) => {\r\n        const aSerialNumber = a.serialNumber || 0;\r\n        const bSerialNumber = b.serialNumber || 0;\r\n        return aSerialNumber - bSerialNumber; // 强制按流水号正序\r\n      });\r\n\r\n \r\n    \r\n\r\n      // 强制触发Vue的响应式更新\r\n      this.$forceUpdate();\r\n\r\n      // 确保表格重新渲染\r\n      this.$nextTick(() => {\r\n        if (this.$refs.winningTable) {\r\n          this.$refs.winningTable.doLayout();\r\n        }\r\n      });\r\n    },\r\n    /** 标记中奖记录为已对账 */\r\n    markWinningAsReconciled(winId) {\r\n      if (!this.reconciledWinningIds.includes(winId)) {\r\n        this.reconciledWinningIds.push(winId);\r\n        this.saveReconciliationState();\r\n      }\r\n    },\r\n    /** 取消中奖记录的对账状态 */\r\n    unmarkWinningAsReconciled(winId) {\r\n      const index = this.reconciledWinningIds.indexOf(winId);\r\n      if (index > -1) {\r\n        this.reconciledWinningIds.splice(index, 1);\r\n        this.saveReconciliationState();\r\n      }\r\n    },\r\n    /** 切换中奖记录的对账状态 */\r\n    toggleWinningReconciliation(winId) {\r\n      if (this.isWinningReconciled(winId)) {\r\n        this.unmarkWinningAsReconciled(winId);\r\n      } else {\r\n        this.markWinningAsReconciled(winId);\r\n      }\r\n    },\r\n    /** 检查中奖记录是否已对账 */\r\n    isWinningReconciled(winId) {\r\n      return this.reconciledWinningIds.includes(winId);\r\n    },\r\n    /** 保存对账状态到localStorage */\r\n    saveReconciliationState() {\r\n      try {\r\n        const reconciliationData = {\r\n          isReconciliationMode: this.isReconciliationMode,\r\n          reconciledWinningIds: this.reconciledWinningIds,\r\n          originalQueryParams: this.originalQueryParams,\r\n          timestamp: Date.now()\r\n        };\r\n        localStorage.setItem('winning_reconciliation_state', JSON.stringify(reconciliationData));\r\n      } catch (error) {\r\n        console.error('保存winning对账状态失败:', error);\r\n      }\r\n    },\r\n    /** 从localStorage恢复对账状态 */\r\n    restoreReconciliationState() {\r\n      try {\r\n        const savedData = localStorage.getItem('winning_reconciliation_state');\r\n        if (savedData) {\r\n          const reconciliationData = JSON.parse(savedData);\r\n\r\n          // 恢复对账模式状态\r\n          if (reconciliationData.isReconciliationMode) {\r\n            this.isReconciliationMode = true;\r\n            this.reconciledWinningIds = reconciliationData.reconciledWinningIds || [];\r\n            this.originalQueryParams = reconciliationData.originalQueryParams || null;\r\n\r\n            console.log('恢复winning对账模式状态:', {\r\n              reconciledCount: this.reconciledWinningIds.length,\r\n              hasOriginalParams: !!this.originalQueryParams\r\n            });\r\n\r\n            // 在下一个tick中重新加载数据以确保对账模式正确应用\r\n            this.$nextTick(() => {\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('恢复winning对账状态失败:', error);\r\n        // 清除损坏的数据\r\n        localStorage.removeItem('winning_reconciliation_state');\r\n      }\r\n    },\r\n    /** 清除对账状态 */\r\n    clearReconciliationState() {\r\n      try {\r\n        localStorage.removeItem('winning_reconciliation_state');\r\n      } catch (error) {\r\n        console.error('清除winning对账状态失败:', error);\r\n      }\r\n    },\r\n    /** 处理一键清空事件 */\r\n    handleClearAllData(event) {\r\n      console.log('winning页面收到一键清空事件:', event.detail);\r\n\r\n      // 只有当前页面处于对账模式时才处理，避免与record页面冲突\r\n      if (this.isReconciliationMode) {\r\n        // 添加短暂延迟，确保事件处理的顺序性\r\n        setTimeout(() => {\r\n          this.exitReconciliationMode();\r\n          console.log('winning页面因一键清空操作退出对账模式');\r\n          this.$message.info('检测到一键清空操作，已自动退出对账模式');\r\n        }, 100);\r\n      }\r\n    },\r\n    /** 处理滚动事件 */\r\n    handleScroll() {\r\n      if (!this.isReconciliationMode) {\r\n        return;\r\n      }\r\n\r\n      // 根据设备类型选择对应的提示框元素\r\n      const noticeElement = this.isMobile ? this.$refs.mobileReconciliationNotice : this.$refs.reconciliationNotice;\r\n      if (!noticeElement) {\r\n        return;\r\n      }\r\n\r\n      const rect = noticeElement.getBoundingClientRect();\r\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\r\n\r\n      // 初始化原始位置（只在第一次计算）\r\n      if (this.reconciliationOriginalTop === 0 && !this.isReconciliationFixed) {\r\n        this.reconciliationOriginalTop = scrollTop + rect.top;\r\n      }\r\n\r\n      // 使用防抖处理，避免频繁切换\r\n      if (!this.scrollDebounceTimer) {\r\n        this.scrollDebounceTimer = setTimeout(() => {\r\n          // 如果对账提示框的顶部离开屏幕超过阈值，则固定它\r\n          if (rect.top <= 0 && !this.isReconciliationFixed) {\r\n            this.isReconciliationFixed = true;\r\n          }\r\n          // 如果滚动回到原始位置附近，则取消固定\r\n          else if (scrollTop <= this.reconciliationOriginalTop - 20 && this.isReconciliationFixed) {\r\n            this.isReconciliationFixed = false;\r\n          }\r\n\r\n          this.scrollDebounceTimer = null;\r\n        }, 16); // 约60fps的更新频率\r\n      }\r\n    },\r\n    /** 撤销结算按钮操作 */\r\n    handleCancelPay(row) {\r\n      this.$modal.confirm('是否确认撤销该笔奖金的结算？').then(() => {\r\n        return cancelPay(row.winId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"撤销结算成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 获取玩法列表 */\r\n    getGameMethods() {\r\n      getAllMethods().then(response => {\r\n        this.$store.commit('game/SET_METHODS_DATA', response.rows)\r\n      })\r\n    },\r\n    /** 获取流水号列表 */\r\n    getSerialNumberList() {\r\n      // 注意：listSerial接口已经在后端实现了用户隔离，所以这里获取的就是当前用户的流水号\r\n      listSerial({ pageNum: 1, pageSize: 1000 }).then(response => {\r\n        \r\n        if (response && response.rows && response.rows.length > 0) {\r\n          // 提取流水号并去重排序\r\n          this.serialNumberList = [...new Set(response.rows.map(item => item.serialNumbers))]\r\n            .filter(item => item != null)\r\n            .sort((a, b) => b - a); // 降序排列，最新的在前面\r\n          \r\n        } else {\r\n          console.log('没有找到流水号数据，尝试从中奖记录获取');\r\n          this.getSerialNumberFromWinning();\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取流水号列表失败，尝试从中奖记录获取:', error);\r\n        this.getSerialNumberFromWinning();\r\n      })\r\n    },\r\n    /** 从中奖记录中获取流水号列表 */\r\n    getSerialNumberFromWinning() {\r\n      // 从中奖记录中提取流水号\r\n      listWinning({ pageNum: 1, pageSize: 1000 }).then(response => {\r\n        if (response && response.rows && response.rows.length > 0) {\r\n          // 从中奖记录中提取流水号并去重排序\r\n          this.serialNumberList = [...new Set(response.rows.map(item => item.serialNumber))]\r\n            .filter(item => item != null)\r\n            .sort((a, b) => b - a); // 降序排列，最新的在前面\r\n          console.log('从中奖记录获取的流水号列表:', this.serialNumberList);\r\n        }\r\n      }).catch(error => {\r\n        console.error('从中奖记录获取流水号失败:', error);\r\n      })\r\n    },\r\n    /** 展开/收起全部 */\r\n    toggleExpandAll() {\r\n      this.allExpanded = !this.allExpanded;\r\n      this.$nextTick(() => {\r\n        if (this.allExpanded) {\r\n          // 展开所有行\r\n          this.winningList.forEach((row, index) => {\r\n            this.$refs.winningTable.toggleRowExpansion(row, true);\r\n          });\r\n        } else {\r\n          // 收起所有行\r\n          this.winningList.forEach((row, index) => {\r\n            this.$refs.winningTable.toggleRowExpansion(row, false);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 重置展开状态 */\r\n    resetExpandState() {\r\n      this.allExpanded = false;\r\n      this.$nextTick(() => {\r\n        // 收起所有行\r\n        this.winningList.forEach((row) => {\r\n          this.$refs.winningTable.toggleRowExpansion(row, false);\r\n        });\r\n      });\r\n    },\r\n    /** 识别框输入处理 */\r\n    handleShiBieInput() {\r\n      // 防抖处理，避免频繁搜索\r\n      if (this.shiBieSearchTimer) {\r\n        clearTimeout(this.shiBieSearchTimer);\r\n      }\r\n      this.shiBieSearchTimer = setTimeout(() => {\r\n        if (this.queryParams.shibie && this.queryParams.shibie.trim()) {\r\n          this.handleQuery();\r\n        }\r\n      }, 500); // 500ms 延迟\r\n    },\r\n    /** 识别框清空处理 */\r\n    handleShiBieClear() {\r\n      this.queryParams.shibie = '';\r\n      this.handleQuery();\r\n    },\r\n    /** 中奖金额排序方法 */\r\n    sortByAmount(a, b) {\r\n      const amountA = parseFloat(a.winAmount) || 0;\r\n      const amountB = parseFloat(b.winAmount) || 0;\r\n      return amountA - amountB;\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面头部样式 */\r\n.page-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.header-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28px;\r\n}\r\n\r\n.header-info h2 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n}\r\n\r\n.header-info p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-container {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.reset-btn:hover {\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 工具栏样式 */\r\n.toolbar-container {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 16px;\r\n  padding: 20px 24px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.toolbar-container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);\r\n}\r\n\r\n/* 工具栏布局 */\r\n.toolbar-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.toolbar-right {\r\n  margin-left: auto;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 工具栏按钮美化 */\r\n.toolbar-btn {\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\r\n  border-width: 1.5px !important;\r\n  padding: 8px 16px !important;\r\n  font-size: 13px !important;\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.toolbar-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n  transition: left 0.5s;\r\n}\r\n\r\n.toolbar-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.toolbar-btn:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n/* 展开按钮样式 */\r\n.expand-btn.el-button--primary {\r\n  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%) !important;\r\n  border-color: #409eff !important;\r\n  color: white !important;\r\n}\r\n\r\n.expand-btn.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%) !important;\r\n  border-color: #66b1ff !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.5) !important;\r\n}\r\n\r\n/* 刷新按钮样式 */\r\n.refresh-btn.el-button--info {\r\n  background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%) !important;\r\n  border-color: #909399 !important;\r\n  color: white !important;\r\n}\r\n\r\n.refresh-btn.el-button--info:hover {\r\n  background: linear-gradient(135deg, #b1b3b8 0%, #909399 100%) !important;\r\n  border-color: #b1b3b8 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(144, 147, 153, 0.5) !important;\r\n}\r\n\r\n/* 导出按钮样式 */\r\n.export-btn.el-button--warning.is-plain {\r\n  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;\r\n  border-color: #e6a23c !important;\r\n  color: white !important;\r\n}\r\n\r\n.export-btn.el-button--warning.is-plain:hover {\r\n  background: linear-gradient(135deg, #ebb563 0%, #e6a23c 100%) !important;\r\n  border-color: #ebb563 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(230, 162, 60, 0.5) !important;\r\n}\r\n\r\n/* 对账按钮样式 */\r\n.reconciliation-btn.el-button--success {\r\n  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;\r\n  border-color: #67c23a !important;\r\n  color: white !important;\r\n}\r\n\r\n.reconciliation-btn.el-button--success:hover {\r\n  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%) !important;\r\n  border-color: #85ce61 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(103, 194, 58, 0.5) !important;\r\n}\r\n\r\n.reconciliation-btn.el-button--danger {\r\n  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%) !important;\r\n  border-color: #f56c6c !important;\r\n  color: white !important;\r\n}\r\n\r\n.reconciliation-btn.el-button--danger:hover {\r\n  background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%) !important;\r\n  border-color: #f78989 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(245, 108, 108, 0.5) !important;\r\n}\r\n\r\n/* 禁用状态样式 */\r\n.toolbar-btn.is-disabled {\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cdd7 100%) !important;\r\n  border-color: #dcdfe6 !important;\r\n  color: #c0c4cc !important;\r\n  cursor: not-allowed !important;\r\n  transform: none !important;\r\n  box-shadow: none !important;\r\n}\r\n\r\n.toolbar-btn.is-disabled::before {\r\n  display: none;\r\n}\r\n\r\n/* 按钮图标样式优化 */\r\n.toolbar-btn [class*=\"el-icon-\"] {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 1200px) {\r\n  .toolbar-container {\r\n    padding: 16px 20px;\r\n  }\r\n\r\n  .toolbar-left {\r\n    gap: 8px;\r\n  }\r\n\r\n  .toolbar-btn {\r\n    padding: 6px 12px !important;\r\n    font-size: 12px !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .toolbar-container {\r\n    padding: 12px 16px;\r\n  }\r\n\r\n  .toolbar-content {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .toolbar-left {\r\n    gap: 6px;\r\n  }\r\n\r\n  .toolbar-right {\r\n    margin-left: 0;\r\n    align-self: flex-end;\r\n  }\r\n\r\n  .toolbar-btn {\r\n    padding: 4px 8px !important;\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .toolbar-btn [class*=\"el-icon-\"] {\r\n    margin-right: 4px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 展开内容样式 */\r\n.expand-content {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.expand-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 16px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.expand-header i {\r\n  color: #667eea;\r\n}\r\n\r\n.table-expand {\r\n  font-size: 0;\r\n}\r\n\r\n.table-expand label {\r\n  width: 90px;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.table-expand .el-form-item {\r\n  margin-right: 0;\r\n  margin-bottom: 12px;\r\n  width: 50%;\r\n}\r\n\r\n.table-expand .el-form-item.full-width {\r\n  width: 100%;\r\n  margin-top: 0;\r\n}\r\n\r\n.expand-value {\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.number-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n}\r\n\r\n.number-tag {\r\n  margin: 0;\r\n  font-weight: 600;\r\n  border-radius: 6px;\r\n}\r\n\r\n.bet-tag {\r\n  background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.winning-tag {\r\n  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);\r\n  border-color: #67C23A;\r\n}\r\n\r\n.no-data {\r\n  color: #909399;\r\n  font-style: italic;\r\n}\r\n\r\n/* 表格列样式 */\r\n.user-info, .method-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-info {\r\n  color: #409EFF;\r\n}\r\n\r\n.method-info {\r\n  color: #67C23A;\r\n}\r\n\r\n.serial-number {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.serial-value {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.lottery-type {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.lottery-tag {\r\n  font-weight: 600;\r\n  border-radius: 6px;\r\n}\r\n\r\n.lottery-unknown {\r\n  color: #909399;\r\n  font-weight: 600;\r\n}\r\n\r\n.amount-simple {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #F56C6C;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .page-header {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .table-expand .el-form-item {\r\n    width: 100%;\r\n  }\r\n\r\n  .number-tags {\r\n    justify-content: center;\r\n  }\r\n\r\n  /* 移动端搜索按钮样式 */\r\n  .mobile-search-toggle {\r\n    margin-bottom: 10px;\r\n    text-align: center;\r\n  }\r\n\r\n  .mobile-search-btn {\r\n    width: 100%;\r\n    max-width: 200px;\r\n  }\r\n\r\n  /* 移动端搜索表单样式 */\r\n  .mobile-search-form {\r\n    padding: 10px;\r\n    background: #f5f7fa;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .mobile-search-form .el-form-item {\r\n    margin-bottom: 10px;\r\n    margin-right: 0;\r\n    width: 100%;\r\n  }\r\n\r\n  .mobile-search-form .el-form-item__label {\r\n    width: 60px !important;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .mobile-search-form .el-input,\r\n  .mobile-search-form .el-select {\r\n    width: 100% !important;\r\n  }\r\n\r\n  /* 移动端识别框输入区域优化 */\r\n  .mobile-search-form .el-form-item .el-textarea {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .mobile-search-form .el-form-item .el-textarea .el-textarea__inner {\r\n    width: 100% !important;\r\n    max-width: 100% !important;\r\n    min-height: 60px !important;\r\n    max-height: 80px !important;\r\n    resize: vertical;\r\n    box-sizing: border-box;\r\n    font-size: 14px;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  /* 移动端工具栏样式 */\r\n  .mobile-toolbar .toolbar-content {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .mobile-toolbar-left {\r\n    display: flex;\r\n    flex-wrap: nowrap;\r\n    gap: 3px;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .mobile-toolbar-left .toolbar-btn {\r\n    flex: 1;\r\n    min-width: calc(25% - 6px);\r\n    max-width: calc(25% - 6px);\r\n    padding: 4px 2px !important;\r\n    font-size: 9px !important;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n  }\r\n\r\n  /* 移动端卡片布局 */\r\n  .mobile-card-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .mobile-card {\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    margin-bottom: 12px;\r\n    overflow: hidden;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .mobile-card.reconciled {\r\n    border: 2px solid #67c23a;\r\n    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);\r\n  }\r\n\r\n  .mobile-card .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .mobile-card .card-header-left {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .mobile-card .winning-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .mobile-card .user-name {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #409eff;\r\n    margin-bottom: 4px;\r\n  }\r\n\r\n  .mobile-card .winning-meta {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 12px;\r\n    color: #666;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .mobile-card .serial-number {\r\n    color: #2c3e50;\r\n    font-weight: 600;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .mobile-card .method-name {\r\n    color: #67c23a;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .mobile-card .card-header-right {\r\n    text-align: right;\r\n  }\r\n\r\n  .mobile-card .win-amount {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    color: #f56c6c;\r\n    margin-bottom: 4px;\r\n  }\r\n\r\n  .mobile-card .reconciliation-status {\r\n    font-size: 12px;\r\n    padding: 2px 6px;\r\n    border-radius: 4px;\r\n    background: #e6f7ff;\r\n    color: #1890ff;\r\n  }\r\n\r\n  .mobile-card .reconciliation-status.reconciled {\r\n    background: #f6ffed;\r\n    color: #52c41a;\r\n  }\r\n\r\n  .mobile-card .card-content {\r\n    padding: 12px;\r\n    background: #fafafa;\r\n  }\r\n\r\n  .mobile-card .detail-row {\r\n    display: flex;\r\n    margin-bottom: 8px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .mobile-card .detail-row:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .mobile-card .detail-label {\r\n    width: 70px;\r\n    font-size: 12px;\r\n    color: #666;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .mobile-card .detail-value {\r\n    flex: 1;\r\n    font-size: 12px;\r\n    color: #333;\r\n    word-break: break-all;\r\n  }\r\n\r\n  .mobile-card .detail-value .el-tag {\r\n    font-size: 10px;\r\n    padding: 0 4px;\r\n    margin: 1px;\r\n  }\r\n\r\n  /* 移动端对账模式提示 */\r\n  .mobile-reconciliation-notice {\r\n    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);\r\n    border: 1px solid #b3d8ff;\r\n    border-radius: 6px;\r\n    padding: 8px 12px;\r\n    margin-bottom: 10px;\r\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);\r\n  }\r\n\r\n  .reconciliation-compact {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    font-size: 13px;\r\n    color: #1976d2;\r\n  }\r\n\r\n  .reconciliation-left {\r\n    display: flex;\r\n    align-items: center;\r\n    flex: 1;\r\n  }\r\n\r\n  .reconciliation-left i {\r\n    color: #409EFF;\r\n    margin-right: 6px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .mode-text {\r\n    font-weight: bold;\r\n    margin-right: 8px;\r\n  }\r\n\r\n  .stats-compact {\r\n    font-size: 11px;\r\n    color: #666;\r\n    background: rgba(255, 255, 255, 0.7);\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  .exit-btn-compact {\r\n    color: #f56c6c;\r\n    font-size: 16px;\r\n    padding: 0;\r\n    margin-left: 8px;\r\n  }\r\n\r\n  /* 移动端对账提示框吸附状态 */\r\n  .mobile-reconciliation-fixed {\r\n    position: fixed !important;\r\n    top: 0 !important;\r\n    left: 0 !important;\r\n    right: 0 !important;\r\n    z-index: 1000 !important;\r\n    margin: 0 !important;\r\n    border-radius: 0 !important;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;\r\n  }\r\n\r\n  /* 移动端卡片点击样式 */\r\n  .mobile-card-container:not(.reconciliation-mode) .mobile-card {\r\n    cursor: pointer;\r\n  }\r\n\r\n  .mobile-card-container:not(.reconciliation-mode) .mobile-card:hover {\r\n    background-color: rgba(64, 158, 255, 0.05);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .reconciliation-mode .mobile-card .card-header-left {\r\n    cursor: pointer;\r\n    transition: background-color 0.2s ease;\r\n  }\r\n\r\n  .reconciliation-mode .mobile-card .card-header-left:hover {\r\n    background-color: rgba(64, 158, 255, 0.1);\r\n  }\r\n\r\n  .mobile-card .card-header-right.reconciliation-clickable {\r\n    cursor: pointer;\r\n    transition: background-color 0.2s ease;\r\n    border-radius: 4px;\r\n    padding: 4px;\r\n  }\r\n\r\n  .mobile-card .card-header-right.reconciliation-clickable:hover {\r\n    background-color: rgba(245, 108, 108, 0.1);\r\n  }\r\n}\r\n/* 对账模式提示样式 */\r\n.reconciliation-notice {\r\n  margin-bottom: 16px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.reconciliation-notice.reconciliation-fixed {\r\n  position: fixed;\r\n  top: 10px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  z-index: 1001;\r\n  width: calc(100% - 40px);\r\n  max-width: 1200px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  animation: slideDown 0.3s ease-out;\r\n  backdrop-filter: blur(8px);\r\n  background: rgba(255, 255, 255, 0.95);\r\n}\r\n\r\n@keyframes slideDown {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(-50%) translateY(-20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(-50%) translateY(0);\r\n  }\r\n}\r\n\r\n.reconciliation-alert {\r\n  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);\r\n  border: 2px solid #1890ff;\r\n  border-radius: 12px;\r\n  padding: 16px 20px;\r\n  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\n.reconciliation-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.reconciliation-header i {\r\n  font-size: 18px;\r\n  color: #1890ff;\r\n  margin-right: 8px;\r\n}\r\n\r\n.reconciliation-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #1890ff;\r\n}\r\n\r\n.reconciliation-content {\r\n  margin: 0;\r\n}\r\n\r\n.reconciliation-instruction {\r\n  background: linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%);\r\n  border: 2px solid #ffa940;\r\n  border-radius: 8px;\r\n  padding: 12px 16px;\r\n  margin: 12px 0 !important;\r\n  font-size: 15px;\r\n  line-height: 1.6;\r\n  box-shadow: 0 2px 8px rgba(255, 169, 64, 0.2);\r\n}\r\n\r\n.highlight-amount {\r\n  background: linear-gradient(135deg, #0080FFFF 0%, #067EFFFF 100%);\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 6px;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  border: 2px dashed white;\r\n  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);\r\n  animation: highlightPulse 2s infinite;\r\n}\r\n\r\n@keyframes highlightPulse {\r\n  0%, 100% {\r\n    transform: scale(1);\r\n    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);\r\n  }\r\n  50% {\r\n    transform: scale(1.05);\r\n    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.5);\r\n  }\r\n}\r\n\r\n.highlight-amount::after {\r\n  content: \" 👆\";\r\n  animation: bounce 1.5s infinite;\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-5px);\r\n  }\r\n  60% {\r\n    transform: translateY(-3px);\r\n  }\r\n}\r\n\r\n.reconciliation-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  font-weight: 600;\r\n  font-size: 15px;\r\n}\r\n\r\n.stats-text {\r\n  flex: 1;\r\n}\r\n\r\n.reconciled-count {\r\n  color: #1890ff;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.reconciled-amount {\r\n  color: #52c41a;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.total-amount {\r\n  color: #8c8c8c;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.total-count {\r\n  color: #52c41a;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.exit-reconciliation-btn {\r\n  margin-left: 16px;\r\n  padding: 4px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 6px rgba(245, 108, 108, 0.3);\r\n}\r\n\r\n.exit-reconciliation-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.5);\r\n}\r\n\r\n/* 对账模式中奖金额样式 */\r\n.reconciliation-amount-container {\r\n  border: 2px dashed #1890ff;\r\n  border-radius: 8px;\r\n  padding: 8px 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);\r\n  position: relative;\r\n  min-height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.reconciliation-amount-container:hover {\r\n  border-color: #40a9ff;\r\n  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);\r\n  transform: scale(1.02);\r\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);\r\n}\r\n\r\n.reconciliation-amount-container.reconciled {\r\n  border-color: #52c41a;\r\n  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);\r\n  border-style: solid;\r\n}\r\n\r\n.reconciliation-amount-container.reconciled:hover {\r\n  border-color: #73d13d;\r\n  background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);\r\n}\r\n\r\n.reconciliation-amount-text {\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  color: #FF0037FF;\r\n}\r\n\r\n.reconciliation-amount-container.reconciled .reconciliation-amount-text {\r\n  color: #FF0000FF;\r\n}\r\n\r\n.reconciled-icon {\r\n  position: absolute;\r\n  top: 2px;\r\n  right: 2px;\r\n  background-color: #EAFFEAFF !important;\r\n  color: #52c41a;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.reconciliation-status-badge {\r\n  position: absolute;\r\n  top: -1px;\r\n  right: -1px;\r\n  background: #ff4d4f;\r\n  color: white;\r\n  font-size: 10px;\r\n  padding: 2px 6px;\r\n  border-radius: 0 6px 0 8px;\r\n  font-weight: bold;\r\n  line-height: 1;\r\n  z-index: 10;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.reconciliation-status-badge.reconciled {\r\n  background: #52c41a;\r\n}\r\n\r\n</style>\r\n"]}]}