{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\winning\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\winning\\index.vue", "mtime": 1758856163952}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0V2lubmluZywgZ2V0V2lubmluZywgZGVsV2lubmluZywgYWRkV2lubmluZywgdXBkYXRlV2lubmluZywgZXhwb3J0V2lubmluZyB9IGZyb20gIkAvYXBpL2dhbWUvd2lubmluZyINCmltcG9ydCB7IGxpc3RDdXN0b21lciB9IGZyb20gIkAvYXBpL2dhbWUvY3VzdG9tZXIiDQppbXBvcnQgeyBtYXBTdGF0ZSB9IGZyb20gJ3Z1ZXgnDQppbXBvcnQgeyBsaXN0VXNlciB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIg0KaW1wb3J0IHsgZ2V0QWxsTWV0aG9kcyB9IGZyb20gIkAvYXBpL2dhbWUvbWV0aG9kIg0KaW1wb3J0IHsgbGlzdERyYXcgfSBmcm9tICJAL2FwaS9nYW1lL2RyYXciDQppbXBvcnQgeyBwYXJzZVRpbWUgfSBmcm9tICdAL3V0aWxzL3J1b3lpJw0KaW1wb3J0IHsgY2FuY2VsUGF5IH0gZnJvbSAiQC9hcGkvZ2FtZS93aW5uaW5nIg0KaW1wb3J0IHsgbGlzdFNlcmlhbCB9IGZyb20gIkAvYXBpL2dhbWUvc2VyaWFsIg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJXaW5uaW5nIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g5a+85Ye66YGu572p5bGCDQogICAgICBleHBvcnRMb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDkuK3lpZbnrqHnkIbooajmoLzmlbDmja4NCiAgICAgIHdpbm5pbmdMaXN0OiBbXSwNCiAgICAgIC8vIOaYr+WQpuWFqOmDqOWxleW8gA0KICAgICAgYWxsRXhwYW5kZWQ6IGZhbHNlLA0KICAgICAgLy8g56e75Yqo56uv55u45YWzDQogICAgICBpc01vYmlsZTogZmFsc2UsDQogICAgICBzaG93TW9iaWxlU2VhcmNoOiBmYWxzZSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOeUqOaIt+WIl+ihqA0KICAgICAgdXNlckxpc3Q6IFtdLA0KICAgICAgLy8g5pyf5Y+35YiX6KGoDQogICAgICBpc3N1ZUxpc3Q6IFtdLA0KICAgICAgLy8g5rWB5rC05Y+35YiX6KGoDQogICAgICBzZXJpYWxOdW1iZXJMaXN0OiBbXSwNCiAgICAgIC8vIOivhuWIq+ahhuaQnOe0oumYsuaKluWumuaXtuWZqA0KICAgICAgc2hpQmllU2VhcmNoVGltZXI6IG51bGwsDQogICAgICAvLyDlr7notKbmqKHlvI/nm7jlhbMNCiAgICAgIGlzUmVjb25jaWxpYXRpb25Nb2RlOiBmYWxzZSwNCiAgICAgIHJlY29uY2lsaWF0aW9uTG9hZGluZzogZmFsc2UsDQogICAgICByZWNvbmNpbGVkV2lubmluZ0lkczogW10sIC8vIOW3suWvuei0pueahOS4reWlluiusOW9lUlE5pWw57uE77yI5ZON5bqU5byP77yJDQogICAgICBvcmlnaW5hbFF1ZXJ5UGFyYW1zOiBudWxsLCAvLyDkv53lrZjljp/lp4vmn6Xor6Llj4LmlbANCiAgICAgIGlzUmVjb25jaWxpYXRpb25GaXhlZDogZmFsc2UsIC8vIOWvuei0puaPkOekuuahhuaYr+WQpuWbuuWumuWcqOmhtumDqA0KICAgICAgcmVjb25jaWxpYXRpb25PcmlnaW5hbFRvcDogMCwgLy8g5a+56LSm5o+Q56S65qGG5Y6f5aeL5L2N572uDQogICAgICBzY3JvbGxEZWJvdW5jZVRpbWVyOiBudWxsLCAvLyDmu5rliqjpmLLmipblrprml7blmagNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDUwLA0KICAgICAgICB1c2VySWQ6IG51bGwsDQogICAgICAgIG1ldGhvZElkOiBudWxsLA0KICAgICAgICBiZXROdW1iZXJzOiBudWxsLA0KICAgICAgICBsb3R0ZXJ5SWQ6IG51bGwsDQogICAgICAgIGlzc3VlTnVtYmVyOiBudWxsLA0KICAgICAgICB3aW5BbW91bnQ6IG51bGwsDQogICAgICAgIGlzUGFpZDogbnVsbCwNCiAgICAgICAgd2lubmluZ051bWJlcnM6IG51bGwsDQogICAgICAgIHNoaWJpZTogbnVsbCwNCiAgICAgICAgc2VyaWFsTnVtYmVyOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgdXNlcklkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4reWllueUqOaIt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIG1ldGhvZElkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueOqeazleWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGxvdHRlcnlJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlvannp43lkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBpc3N1ZU51bWJlcjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuK3lpZbmnJ/lj7fkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAuLi5tYXBTdGF0ZSh7DQogICAgICBnYW1lTWV0aG9kc0RhdGE6IHN0YXRlID0+IHN0YXRlLmdhbWUubWV0aG9kc0RhdGENCiAgICB9KSwNCiAgICAvKiog6K6h566X5bey5a+56LSm55qE5Lit5aWW6YeR6aKd5oC75ZKMICovDQogICAgcmVjb25jaWxlZFdpbm5pbmdBbW91bnQoKSB7DQogICAgICBpZiAoIXRoaXMuaXNSZWNvbmNpbGlhdGlvbk1vZGUgfHwgIXRoaXMud2lubmluZ0xpc3QgfHwgdGhpcy53aW5uaW5nTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIDA7DQogICAgICB9DQoNCiAgICAgIHJldHVybiB0aGlzLndpbm5pbmdMaXN0DQogICAgICAgIC5maWx0ZXIod2lubmluZyA9PiB0aGlzLmlzV2lubmluZ1JlY29uY2lsZWQod2lubmluZy53aW5JZCkpDQogICAgICAgIC5yZWR1Y2UoKHRvdGFsLCB3aW5uaW5nKSA9PiB7DQogICAgICAgICAgY29uc3QgYW1vdW50ID0gcGFyc2VGbG9hdCh3aW5uaW5nLndpbkFtb3VudCkgfHwgMDsNCiAgICAgICAgICByZXR1cm4gdG90YWwgKyBhbW91bnQ7DQogICAgICAgIH0sIDApOw0KICAgIH0sDQogICAgLyoqIOiuoeeul+aJgOacieS4reWllumHkemineaAu+WSjCAqLw0KICAgIHRvdGFsV2lubmluZ0Ftb3VudCgpIHsNCiAgICAgIGlmICghdGhpcy53aW5uaW5nTGlzdCB8fCB0aGlzLndpbm5pbmdMaXN0Lmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gMDsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHRoaXMud2lubmluZ0xpc3QucmVkdWNlKCh0b3RhbCwgd2lubmluZykgPT4gew0KICAgICAgICBjb25zdCBhbW91bnQgPSBwYXJzZUZsb2F0KHdpbm5pbmcud2luQW1vdW50KSB8fCAwOw0KICAgICAgICByZXR1cm4gdG90YWwgKyBhbW91bnQ7DQogICAgICB9LCAwKTsNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgLy8g5Yid5aeL5YyW56e75Yqo56uv5qOA5rWLDQogICAgdGhpcy5pbml0TW9iaWxlRGV0ZWN0aW9uKCk7DQoNCiAgICB0aGlzLmdldExpc3QoKQ0KICAgIHRoaXMuZ2V0Q3VzdG9tZXJMaXN0KCkNCiAgICB0aGlzLmdldEdhbWVNZXRob2RzKCkNCiAgICB0aGlzLmdldElzc3VlTGlzdCgpDQogICAgdGhpcy5nZXRTZXJpYWxOdW1iZXJMaXN0KCkNCiAgICAvLyDmgaLlpI3lr7notKbmqKHlvI/nirbmgIENCiAgICB0aGlzLnJlc3RvcmVSZWNvbmNpbGlhdGlvblN0YXRlKCkNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDnm5HlkKzmu5rliqjkuovku7YNCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignc2Nyb2xsJywgdGhpcy5oYW5kbGVTY3JvbGwpOw0KICAgIC8vIOebkeWQrOS4gOmUrua4heepuuS6i+S7tg0KICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdjbGVhckFsbERhdGEnLCB0aGlzLmhhbmRsZUNsZWFyQWxsRGF0YSk7DQogICAgLy8g55uR5ZCs56qX5Y+j5aSn5bCP5Y+Y5YyWDQogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKTsNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDnp7vpmaTmu5rliqjnm5HlkKwNCiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignc2Nyb2xsJywgdGhpcy5oYW5kbGVTY3JvbGwpOw0KICAgIC8vIOenu+mZpOS4gOmUrua4heepuuS6i+S7tuebkeWQrA0KICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdjbGVhckFsbERhdGEnLCB0aGlzLmhhbmRsZUNsZWFyQWxsRGF0YSk7DQogICAgLy8g56e76Zmk56qX5Y+j5aSn5bCP5Y+Y5YyW55uR5ZCsDQogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKTsNCg0KICAgIC8vIOa4heeQhumYsuaKluWumuaXtuWZqA0KICAgIGlmICh0aGlzLnNjcm9sbERlYm91bmNlVGltZXIpIHsNCiAgICAgIGNsZWFyVGltZW91dCh0aGlzLnNjcm9sbERlYm91bmNlVGltZXIpOw0KICAgICAgdGhpcy5zY3JvbGxEZWJvdW5jZVRpbWVyID0gbnVsbDsNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5Yid5aeL5YyW56e75Yqo56uv5qOA5rWLICovDQogICAgaW5pdE1vYmlsZURldGVjdGlvbigpIHsNCiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykgew0KICAgICAgICB0aGlzLmlzTW9iaWxlID0gd2luZG93LmlubmVyV2lkdGggPD0gNzY4Ow0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG56qX5Y+j5aSn5bCP5Y+Y5YyWICovDQogICAgaGFuZGxlUmVzaXplKCkgew0KICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7DQogICAgICAgIGNvbnN0IG5ld0lzTW9iaWxlID0gd2luZG93LmlubmVyV2lkdGggPD0gNzY4Ow0KICAgICAgICBpZiAodGhpcy5pc01vYmlsZSAhPT0gbmV3SXNNb2JpbGUpIHsNCiAgICAgICAgICB0aGlzLmlzTW9iaWxlID0gbmV3SXNNb2JpbGU7DQogICAgICAgICAgLy8g5Zyo56e75Yqo56uv5ZKM5qGM6Z2i56uv5YiH5o2i5pe277yM6YeN572u5pCc57Si5qGG54q25oCBDQogICAgICAgICAgaWYgKCFuZXdJc01vYmlsZSAmJiB0aGlzLnNob3dNb2JpbGVTZWFyY2gpIHsNCiAgICAgICAgICAgIHRoaXMuc2hvd01vYmlsZVNlYXJjaCA9IGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyDlvLrliLbph43mlrDmuLLmn5Pku6XpgILlupTmlrDnmoTlsY/luZXlsLrlr7gNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDliIfmjaLnp7vliqjnq6/mkJzntKLmoYbmmL7npLogKi8NCiAgICB0b2dnbGVNb2JpbGVTZWFyY2goKSB7DQogICAgICB0aGlzLnNob3dNb2JpbGVTZWFyY2ggPSAhdGhpcy5zaG93TW9iaWxlU2VhcmNoOw0KICAgIH0sDQoNCiAgICAvKiog5YiH5o2i56e75Yqo56uv5Y2h54mH5bGV5byA54q25oCBICovDQogICAgdG9nZ2xlTW9iaWxlQ2FyZChpdGVtLCBpbmRleCkgew0KICAgICAgdGhpcy4kc2V0KGl0ZW0sICdtb2JpbGVFeHBhbmRlZCcsICFpdGVtLm1vYmlsZUV4cGFuZGVkKTsNCiAgICB9LA0KDQogICAgLyoqIOmAgOWHuuWvuei0puaooeW8jyAqLw0KICAgIGV4aXRSZWNvbmNpbGlhdGlvbk1vZGUoKSB7DQogICAgICB0aGlzLnRvZ2dsZVJlY29uY2lsaWF0aW9uTW9kZSgpOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5pyf5Y+35YiX6KGoICovDQogICAgZ2V0SXNzdWVMaXN0KCkgew0KICAgICAgbGlzdERyYXcoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5pc3N1ZUxpc3QgPSByZXNwb25zZS5yb3dzDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOiOt+WPlueUqOaIt+WIl+ihqCAqLw0KICAgIGdldEN1c3RvbWVyTGlzdCgpIHsNCiAgICAgIGxpc3RDdXN0b21lcigpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnVzZXJMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDmn6Xor6LkuK3lpZbnrqHnkIbliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcGFyYW1zID0geyAuLi50aGlzLnF1ZXJ5UGFyYW1zIH07DQoNCiAgICAgIC8vIOWmguaenOaYr+Wvuei0puaooeW8j++8jOiOt+WPluaJgOacieaVsOaNrg0KICAgICAgaWYgKHRoaXMuaXNSZWNvbmNpbGlhdGlvbk1vZGUpIHsNCiAgICAgICAgcGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgICBwYXJhbXMucGFnZVNpemUgPSA5OTk5OTk7DQogICAgICB9DQoNCiAgICAgIC8vIOiusOW9leaYr+WQpuaYr+ivhuWIq+ahhuaQnOe0og0KICAgICAgY29uc3QgaXNTaGlCaWVTZWFyY2ggPSBwYXJhbXMuc2hpYmllICYmIHBhcmFtcy5zaGliaWUudHJpbSgpICE9PSAnJzsNCg0KICAgICAgLy8g5aaC5p6c5pyJ6K+G5Yir5qGG5pCc57Si5YaF5a65DQogICAgICBpZiAoaXNTaGlCaWVTZWFyY2gpIHsNCiAgICAgICAgLy8g5Y676Zmk6aaW5bC+56m65qC8DQogICAgICAgIHBhcmFtcy5zaGliaWUgPSBwYXJhbXMuc2hpYmllLnRyaW0oKTsNCiAgICAgICAgLy8g5a+55LqO6K+G5Yir5qGG5pCc57Si77yM5LiN5Lyg6YCS57uZ5ZCO56uv77yM6ICM5piv6I635Y+W5omA5pyJ5pWw5o2u6L+b6KGM5YmN56uv6L+H5rukDQogICAgICAgIGRlbGV0ZSBwYXJhbXMuc2hpYmllOw0KICAgICAgfQ0KDQogICAgICBsaXN0V2lubmluZyhwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBsZXQgZmlsdGVyZWRSb3dzID0gcmVzcG9uc2Uucm93czsNCg0KICAgICAgICAvLyDlpoLmnpzmnInor4bliKvmoYbmkJzntKLlhoXlrrnkuJTmnInov5Tlm57nu5PmnpzvvIzov5vooYzliY3nq6/ov4fmu6QNCiAgICAgICAgaWYgKGlzU2hpQmllU2VhcmNoICYmIGZpbHRlcmVkUm93cy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgY29uc3Qgc2VhcmNoVGV4dCA9IHRoaXMucXVlcnlQYXJhbXMuc2hpYmllLnRyaW0oKTsNCg0KICAgICAgICAgIC8vIOaooeeziuWMuemFje+8muWMheWQq+aQnOe0ouaWh+acrOeahOaJgOacieiusOW9lQ0KICAgICAgICAgIGZpbHRlcmVkUm93cyA9IGZpbHRlcmVkUm93cy5maWx0ZXIocm93ID0+DQogICAgICAgICAgICByb3cuc2hpYmllICYmIHJvdy5zaGliaWUuaW5jbHVkZXMoc2VhcmNoVGV4dCkNCiAgICAgICAgICApOw0KDQogICAgICAgICAgLy8g5pu05paw5oC75pWw5Li66L+H5ruk5ZCO55qE5pWw6YePDQogICAgICAgICAgcmVzcG9uc2UudG90YWwgPSBmaWx0ZXJlZFJvd3MubGVuZ3RoOw0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy53aW5uaW5nTGlzdCA9IGZpbHRlcmVkUm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KDQogICAgICAgIC8vIOWmguaenOaYr+Wvuei0puaooeW8j++8jOW8uuWItuato+W6j+aOkuW6jw0KICAgICAgICBpZiAodGhpcy5pc1JlY29uY2lsaWF0aW9uTW9kZSkgew0KICAgICAgICAgIHRoaXMuc29ydFdpbm5pbmdMaXN0Rm9yUmVjb25jaWxpYXRpb24oKTsNCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KDQogICAgICAgIC8vIOWmguaenOaYr+ivhuWIq+ahhuaQnOe0ou+8jOiHquWKqOWxleW8gOaJgOacieaQnOe0oue7k+aenA0KICAgICAgICBpZiAoaXNTaGlCaWVTZWFyY2gpIHsNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmFsbEV4cGFuZGVkID0gdHJ1ZTsNCiAgICAgICAgICAgIHRoaXMud2lubmluZ0xpc3QuZm9yRWFjaCgocm93KSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMud2lubmluZ1RhYmxlLnRvZ2dsZVJvd0V4cGFuc2lvbihyb3csIHRydWUpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g6Z2e6K+G5Yir5qGG5pCc57Si77yM6YeN572u5bGV5byA54q25oCBDQogICAgICAgICAgdGhpcy5yZXNldEV4cGFuZFN0YXRlKCk7DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgdGhpcy5yZXNldCgpDQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgd2luSWQ6IG51bGwsDQogICAgICAgIHVzZXJJZDogbnVsbCwNCiAgICAgICAgbWV0aG9kSWQ6IG51bGwsDQogICAgICAgIGJldE51bWJlcjogbnVsbCwNCiAgICAgICAgbG90dGVyeUlkOiBudWxsLA0KICAgICAgICBpc3N1ZU51bWJlcjogbnVsbCwNCiAgICAgICAgd2luQW1vdW50OiBudWxsLA0KICAgICAgICBpc1BhaWQ6IG51bGwsDQogICAgICAgIHdpbm5pbmdOdW1iZXJzOiBudWxsDQogICAgICB9DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpDQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxDQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIikNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS53aW5JZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOWkhOeQhuihqOagvOihjOeCueWHuyAqLw0KICAgIGhhbmRsZVJvd0NsaWNrKHJvdywgY29sdW1uLCBldmVudCkgew0KICAgICAgLy8g6YG/5YWN54K55Ye75pON5L2c5oyJ6ZKu5pe26Kem5Y+R6KGM5bGV5byADQogICAgICBpZiAoY29sdW1uICYmIGNvbHVtbi50eXBlID09PSAnc2VsZWN0aW9uJykgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWcqOWvuei0puaooeW8j+S4i++8jOWmguaenOeCueWHu+eahOaYr+S4reWllumHkemineWIl++8jOS4jeWxleW8gOihjA0KICAgICAgaWYgKHRoaXMuaXNSZWNvbmNpbGlhdGlvbk1vZGUgJiYgY29sdW1uICYmIGNvbHVtbi5wcm9wZXJ0eSA9PT0gJ3dpbkFtb3VudCcpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZygn5Lit5aWW6K6w5b2V6KGo5qC86KGM54K55Ye7OicsIHJvdy53aW5JZCB8fCAndW5rbm93bicpOw0KDQogICAgICAvLyDliIfmjaLooYznmoTlsZXlvIDnirbmgIENCiAgICAgIGlmICh0aGlzLiRyZWZzLndpbm5pbmdUYWJsZSkgew0KICAgICAgICB0aGlzLiRyZWZzLndpbm5pbmdUYWJsZS50b2dnbGVSb3dFeHBhbnNpb24ocm93KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCkNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Lit5aWW566h55CGIg0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgY29uc3Qgd2luSWQgPSByb3cud2luSWQgfHwgdGhpcy5pZHMNCiAgICAgIGdldFdpbm5pbmcod2luSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnkuK3lpZbnrqHnkIYiDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLndpbklkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZVdpbm5pbmcodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFdpbm5pbmcodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IHdpbklkcyA9IHJvdy53aW5JZCB8fCB0aGlzLmlkcw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5Lit5aWW566h55CG57yW5Y+35Li6IicgKyB3aW5JZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxXaW5uaW5nKHdpbklkcykNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5Lit5aWW566h55CG5pWw5o2u6aG577yfJykudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IHRydWUNCiAgICAgICAgLy8g6I635Y+W5omA5pyJ5pWw5o2u55So5LqO5a+85Ye6DQogICAgICAgIGNvbnN0IGV4cG9ydFBhcmFtcyA9IHsNCiAgICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zLA0KICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgcGFnZVNpemU6IDEwMDAwIC8vIOiOt+WPluWkp+mHj+aVsOaNrg0KICAgICAgICB9DQoNCiAgICAgICAgbGlzdFdpbm5pbmcoZXhwb3J0UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2Uucm93cw0KICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZERhdGEgPSB0aGlzLmZvcm1hdERhdGFGb3JFeHBvcnQoZGF0YSkNCiAgICAgICAgICB0aGlzLmV4cG9ydFRvRXhjZWwoZm9ybWF0dGVkRGF0YSkNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlr7zlh7rlpLHotKUiKQ0KICAgICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgICB0aGlzLmV4cG9ydExvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCg0KICAgIC8qKiDmoLzlvI/ljJblr7zlh7rmlbDmja7vvIzkuI7pobXpnaLmmL7npLrkv53mjIHkuIDoh7QgKi8NCiAgICBmb3JtYXREYXRhRm9yRXhwb3J0KGRhdGEpIHsNCiAgICAgIHJldHVybiBkYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgLy8g5qC85byP5YyW5LiL5rOo5Y+356CBDQogICAgICAgIGxldCBiZXROdW1iZXJzRm9ybWF0dGVkID0gJycNCiAgICAgICAgaWYgKGl0ZW0uYmV0TnVtYmVyKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGNvbnN0IGJldERhdGEgPSBKU09OLnBhcnNlKGl0ZW0uYmV0TnVtYmVyKQ0KICAgICAgICAgICAgaWYgKGJldERhdGEubnVtYmVycyAmJiBBcnJheS5pc0FycmF5KGJldERhdGEubnVtYmVycykpIHsNCiAgICAgICAgICAgICAgLy8g6IOG5ouW546p5rOV5qC85byP5YyWIChtZXRob2RfaWQgNDQtNTkpDQogICAgICAgICAgICAgIGlmIChpdGVtLm1ldGhvZElkID49IDQ0ICYmIGl0ZW0ubWV0aG9kSWQgPD0gNTkpIHsNCiAgICAgICAgICAgICAgICBpZiAoYmV0RGF0YS5udW1iZXJzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICAgIGNvbnN0IGZpcnN0TnVtID0gYmV0RGF0YS5udW1iZXJzWzBdDQogICAgICAgICAgICAgICAgICBiZXROdW1iZXJzRm9ybWF0dGVkID0gYOiDhiR7Zmlyc3ROdW0uZGFubWF9IOaLliR7Zmlyc3ROdW0udHVvbWF9YA0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAvLyDot6jluqbnjqnms5XmoLzlvI/ljJYgKG1ldGhvZF9pZCA2MC02OSkNCiAgICAgICAgICAgICAgZWxzZSBpZiAoaXRlbS5tZXRob2RJZCA+PSA2MCAmJiBpdGVtLm1ldGhvZElkIDw9IDY5KSB7DQogICAgICAgICAgICAgICAgYmV0TnVtYmVyc0Zvcm1hdHRlZCA9IGDot6jluqYke2l0ZW0ubWV0aG9kSWQgLSA2MH1gDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgLy8g5Y6f5pyJ546p5rOV5qC85byP5YyWDQogICAgICAgICAgICAgIGVsc2Ugew0KICAgICAgICAgICAgICAgIGJldE51bWJlcnNGb3JtYXR0ZWQgPSBiZXREYXRhLm51bWJlcnMubWFwKG51bSA9PiBPYmplY3QudmFsdWVzKG51bSkuam9pbignLicpKS5qb2luKCcsICcpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICBiZXROdW1iZXJzRm9ybWF0dGVkID0gaXRlbS5iZXROdW1iZXJzIHx8ICcnDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGJldE51bWJlcnNGb3JtYXR0ZWQgPSBpdGVtLmJldE51bWJlcnMgfHwgJycNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOagvOW8j+WMluS4reWlluWPt+eggQ0KICAgICAgICBsZXQgd2lubmluZ051bWJlcnNGb3JtYXR0ZWQgPSAnJw0KICAgICAgICBpZiAoaXRlbS53aW5uaW5nTnVtYmVycykgew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBpZiAodHlwZW9mIGl0ZW0ud2lubmluZ051bWJlcnMgPT09ICdzdHJpbmcnKSB7DQogICAgICAgICAgICAgIGNvbnN0IHdpbkRhdGEgPSBKU09OLnBhcnNlKGl0ZW0ud2lubmluZ051bWJlcnMpDQogICAgICAgICAgICAgIGlmICh3aW5EYXRhLndpbm5pbmcgJiYgQXJyYXkuaXNBcnJheSh3aW5EYXRhLndpbm5pbmcpKSB7DQogICAgICAgICAgICAgICAgd2lubmluZ051bWJlcnNGb3JtYXR0ZWQgPSB3aW5EYXRhLndpbm5pbmcubWFwKG51bSA9PiB7DQogICAgICAgICAgICAgICAgICBpZiAobnVtLmIgPT09IHVuZGVmaW5lZCAmJiBudW0uYyA9PT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBudW0uYQ0KICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChudW0uYyA9PT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiBgJHtudW0uYX0uJHtudW0uYn1gDQogICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gYCR7bnVtLmF9LiR7bnVtLmJ9LiR7bnVtLmN9YA0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pLmpvaW4oJywgJykNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgd2lubmluZ051bWJlcnNGb3JtYXR0ZWQgPSBpdGVtLndpbm5pbmdOdW1iZXJzDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgd2lubmluZ051bWJlcnNGb3JtYXR0ZWQgPSBpdGVtLndpbm5pbmdOdW1iZXJzIHx8ICcnDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qC85byP5YyW5Lit5aWW6YeR6aKd77yI5Y675o6J77+l56ym5Y+377yJDQogICAgICAgIGNvbnN0IGZvcm1hdEFtb3VudEZvckV4cG9ydCA9IChhbW91bnQpID0+IHsNCiAgICAgICAgICBpZiAoIWFtb3VudCAmJiBhbW91bnQgIT09IDApIHJldHVybiAnMC4wMCcNCiAgICAgICAgICBjb25zdCBudW0gPSBwYXJzZUZsb2F0KGFtb3VudCkNCiAgICAgICAgICByZXR1cm4gbnVtLnRvRml4ZWQoMikNCiAgICAgICAgfQ0KDQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgJ+S4reWllueUqOaItyc6IHRoaXMuZ2V0VXNlck5hbWUoaXRlbS51c2VySWQpLA0KICAgICAgICAgICflvannp40nOiB0aGlzLmdldExvdHRlcnlOYW1lKGl0ZW0ubG90dGVyeUlkKSwNCiAgICAgICAgICAn546p5rOV5ZCN56ewJzogdGhpcy5nZXRNZXRob2ROYW1lKGl0ZW0ubWV0aG9kSWQpLA0KICAgICAgICAgICfkuIvms6jlj7fnoIEnOiBiZXROdW1iZXJzRm9ybWF0dGVkLA0KICAgICAgICAgICfkuK3lpZblj7fnoIEnOiB3aW5uaW5nTnVtYmVyc0Zvcm1hdHRlZCwNCiAgICAgICAgICAn5rWB5rC05Y+3JzogaXRlbS5zZXJpYWxOdW1iZXIsDQogICAgICAgICAgJ+S4reWllumHkeminSc6IGZvcm1hdEFtb3VudEZvckV4cG9ydChpdGVtLndpbkFtb3VudCksDQogICAgICAgICAgJ+WPt+eggeivhuWIqyc6IGl0ZW0uc2hpYmllIHx8ICcnDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8qKiDlr7zlh7rliLBFeGNlbCAqLw0KICAgIGV4cG9ydFRvRXhjZWwoZGF0YSkgew0KICAgICAgaW1wb3J0KCdAL3V0aWxzL0V4cG9ydDJFeGNlbCcpLnRoZW4oZXhjZWwgPT4gew0KICAgICAgICBjb25zdCB0SGVhZGVyID0gWyfkuK3lpZbnlKjmiLcnLCAn5b2p56eNJywgJ+eOqeazleWQjeensCcsICfkuIvms6jlj7fnoIEnLCAn5Lit5aWW5Y+356CBJywgJ+a1geawtOWPtycsICfkuK3lpZbph5Hpop0nLCAn5Y+356CB6K+G5YirJ10NCiAgICAgICAgY29uc3QgZmlsdGVyVmFsID0gWyfkuK3lpZbnlKjmiLcnLCAn5b2p56eNJywgJ+eOqeazleWQjeensCcsICfkuIvms6jlj7fnoIEnLCAn5Lit5aWW5Y+356CBJywgJ+a1geawtOWPtycsICfkuK3lpZbph5Hpop0nLCAn5Y+356CB6K+G5YirJ10NCiAgICAgICAgY29uc3QgZXhwb3J0RGF0YSA9IGRhdGEubWFwKHYgPT4gZmlsdGVyVmFsLm1hcChqID0+IHZbal0pKQ0KDQogICAgICAgIGV4Y2VsLmV4cG9ydF9qc29uX3RvX2V4Y2VsKHsNCiAgICAgICAgICBoZWFkZXI6IHRIZWFkZXIsDQogICAgICAgICAgZGF0YTogZXhwb3J0RGF0YSwNCiAgICAgICAgICBmaWxlbmFtZTogYOS4reWllueuoeeQhl8ke3RoaXMucGFyc2VUaW1lKG5ldyBEYXRlKCksICd7eX17bX17ZH1fe2h9e2l9e3N9Jyl9YCwNCiAgICAgICAgICBhdXRvV2lkdGg6IHRydWUsDQogICAgICAgICAgYm9va1R5cGU6ICd4bHN4Jw0KICAgICAgICB9KQ0KDQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWvvOWHuuaIkOWKnyIpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlr7zlh7rlpLHotKUiKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDojrflj5bnjqnms5XlkI3np7AgKi8NCiAgICBnZXRNZXRob2ROYW1lKG1ldGhvZElkKSB7DQogICAgICBpZiAoIXRoaXMuZ2FtZU1ldGhvZHNEYXRhIHx8ICF0aGlzLmdhbWVNZXRob2RzRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgcmV0dXJuIG1ldGhvZElkDQogICAgICB9DQogICAgICBjb25zdCBtZXRob2QgPSB0aGlzLmdhbWVNZXRob2RzRGF0YS5maW5kKGl0ZW0gPT4gTnVtYmVyKGl0ZW0ubWV0aG9kSWQpID09PSBOdW1iZXIobWV0aG9kSWQpKQ0KICAgICAgcmV0dXJuIG1ldGhvZCA/IG1ldGhvZC5tZXRob2ROYW1lIDogbWV0aG9kSWQNCiAgICB9LA0KICAgIC8qKiDojrflj5bnlKjmiLflkI3np7AgKi8NCiAgICBnZXRVc2VyTmFtZSh1c2VySWQpIHsNCiAgICAgIGlmICghdGhpcy51c2VyTGlzdCB8fCAhdGhpcy51c2VyTGlzdC5sZW5ndGgpIHsNCiAgICAgICAgcmV0dXJuIHVzZXJJZA0KICAgICAgfQ0KICAgICAgY29uc3QgdXNlciA9IHRoaXMudXNlckxpc3QuZmluZChpdGVtID0+IE51bWJlcihpdGVtLnVzZXJJZCkgPT09IE51bWJlcih1c2VySWQpKQ0KICAgICAgcmV0dXJuIHVzZXIgPyB1c2VyLm5hbWUgOiB1c2VySWQNCiAgICB9LA0KICAgIC8qKiDojrflj5blvannp43lkI3np7AgKi8NCiAgICBnZXRMb3R0ZXJ5TmFtZShsb3R0ZXJ5SWQpIHsNCiAgICAgIGNvbnN0IGxvdHRlcnlNYXAgPSB7DQogICAgICAgICcxJzogJ+emj+W9qTNEJywNCiAgICAgICAgJzInOiAn5L2T5b2p5o6S5LiJJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGxvdHRlcnlNYXBbbG90dGVyeUlkXSB8fCBsb3R0ZXJ5SWQNCiAgICB9LA0KICAgIC8qKiDlpITnkIbotZTku5ggKi8NCiAgICBoYW5kbGVQYXkocm93KSB7DQogICAgICB1cGRhdGVXaW5uaW5nKHsNCiAgICAgICAgd2luSWQ6IHJvdy53aW5JZCwNCiAgICAgICAgaXNQYWlkOiAxDQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6LWU5LuY5oiQ5YqfIikNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5om56YeP6LWU5LuYICovDQogICAgaGFuZGxlQmF0Y2hQYXkoKSB7DQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHotZTku5jnmoTorrDlvZUiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IHByb21pc2VzID0gdGhpcy5pZHMubWFwKHdpbklkID0+IA0KICAgICAgICB1cGRhdGVXaW5uaW5nKHsNCiAgICAgICAgICB3aW5JZDogd2luSWQsDQogICAgICAgICAgaXNQYWlkOiAxDQogICAgICAgIH0pDQogICAgICApDQogICAgICBQcm9taXNlLmFsbChwcm9taXNlcykudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaJuemHj+i1lOS7mOaIkOWKnyIpDQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLmibnph4/otZTku5jlpLHotKXvvJoiICsgKGVycm9yLm1lc3NhZ2UgfHwgIuacquefpemUmeivryIpKQ0KICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgZm9ybWF0QW1vdW50KGFtb3VudCkgew0KICAgICAgaWYgKGFtb3VudCA9PT0gbnVsbCB8fCBhbW91bnQgPT09IHVuZGVmaW5lZCkgew0KICAgICAgICByZXR1cm4gJ++/pTAuMDAnDQogICAgICB9DQogICAgICByZXR1cm4gJ++/pScgKyBwYXJzZUZsb2F0KGFtb3VudCkudG9GaXhlZCgyKQ0KICAgIH0sDQogICAgLyoqIOWIh+aNouWvuei0puaooeW8jyAqLw0KICAgIHRvZ2dsZVJlY29uY2lsaWF0aW9uTW9kZSgpIHsNCiAgICAgIGlmICghdGhpcy5pc1JlY29uY2lsaWF0aW9uTW9kZSkgew0KICAgICAgICAvLyDov5vlhaXlr7notKbmqKHlvI8NCiAgICAgICAgdGhpcy5lbnRlclJlY29uY2lsaWF0aW9uTW9kZSgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g6YCA5Ye65a+56LSm5qih5byPDQogICAgICAgIHRoaXMuZXhpdFJlY29uY2lsaWF0aW9uTW9kZSgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOi/m+WFpeWvuei0puaooeW8jyAqLw0KICAgIGVudGVyUmVjb25jaWxpYXRpb25Nb2RlKCkgew0KICAgICAgLy8g5L+d5a2Y5Y6f5aeL5p+l6K+i5Y+C5pWwDQogICAgICB0aGlzLm9yaWdpbmFsUXVlcnlQYXJhbXMgPSB7IC4uLnRoaXMucXVlcnlQYXJhbXMgfTsNCg0KICAgICAgLy8g5riF56m65bey5a+56LSm55qE5Lit5aWW6K6w5b2VSUTmlbDnu4QNCiAgICAgIHRoaXMucmVjb25jaWxlZFdpbm5pbmdJZHMgPSBbXTsNCg0KICAgICAgLy8g6YeN572u5Zu65a6a54q25oCB5ZKM6Ziy5oqW5a6a5pe25ZmoDQogICAgICB0aGlzLmlzUmVjb25jaWxpYXRpb25GaXhlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5yZWNvbmNpbGlhdGlvbk9yaWdpbmFsVG9wID0gMDsNCiAgICAgIGlmICh0aGlzLnNjcm9sbERlYm91bmNlVGltZXIpIHsNCiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuc2Nyb2xsRGVib3VuY2VUaW1lcik7DQogICAgICAgIHRoaXMuc2Nyb2xsRGVib3VuY2VUaW1lciA9IG51bGw7DQogICAgICB9DQoNCiAgICAgIC8vIOiuvue9ruS4uuiOt+WPluaJgOacieaVsOaNru+8iOS4jeWIhumhte+8iQ0KICAgICAgY29uc3QgYWxsRGF0YVBhcmFtcyA9IHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDk5OTk5OSAvLyDorr7nva7kuIDkuKrlvojlpKfnmoTmlbDlgLzmnaXojrflj5bmiYDmnInmlbDmja4NCiAgICAgIH07DQoNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQoNCiAgICAgIGxpc3RXaW5uaW5nKGFsbERhdGFQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLndpbm5pbmdMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnJvd3MubGVuZ3RoOw0KDQogICAgICAgIC8vIOW8uuWItuato+W6j+aOkuW6j++8iOWvuei0puaooeW8j+S4i+aMiXdpbklk5q2j5bqP77yJDQogICAgICAgIHRoaXMuc29ydFdpbm5pbmdMaXN0Rm9yUmVjb25jaWxpYXRpb24oKTsNCg0KICAgICAgICAvLyDorr7nva7lr7notKbmqKHlvI8NCiAgICAgICAgdGhpcy5pc1JlY29uY2lsaWF0aW9uTW9kZSA9IHRydWU7DQoNCiAgICAgICAgLy8g5L+d5a2Y5a+56LSm54q25oCBDQogICAgICAgIHRoaXMuc2F2ZVJlY29uY2lsaWF0aW9uU3RhdGUoKTsNCg0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5yZWNvbmNpbGlhdGlvbkxvYWRpbmcgPSBmYWxzZTsNCg0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKGDlt7Lov5vlhaXlr7notKbmqKHlvI/vvIzlhbHliqDovb0gJHtyZXNwb25zZS5yb3dzLmxlbmd0aH0g5p2h5Lit5aWW6K6w5b2V77yM5oyJ5rWB5rC05Y+35q2j5bqP5o6S5YiX5pi+56S6YCk7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLnJlY29uY2lsaWF0aW9uTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign6L+b5YWl5a+56LSm5qih5byP5aSx6LSl77yaJyArIGVycm9yLm1lc3NhZ2UpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6YCA5Ye65a+56LSm5qih5byPICovDQogICAgZXhpdFJlY29uY2lsaWF0aW9uTW9kZSgpIHsNCiAgICAgIC8vIOaBouWkjeWOn+Wni+afpeivouWPguaVsA0KICAgICAgaWYgKHRoaXMub3JpZ2luYWxRdWVyeVBhcmFtcykgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0geyAuLi50aGlzLm9yaWdpbmFsUXVlcnlQYXJhbXMgfTsNCiAgICAgIH0NCg0KICAgICAgLy8g5riF56m65bey5a+56LSm55qE5Lit5aWW6K6w5b2VSUTmlbDnu4QNCiAgICAgIHRoaXMucmVjb25jaWxlZFdpbm5pbmdJZHMgPSBbXTsNCg0KICAgICAgLy8g6YeN572u5Zu65a6a54q25oCB5ZKM5riF55CG5a6a5pe25ZmoDQogICAgICB0aGlzLmlzUmVjb25jaWxpYXRpb25GaXhlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5yZWNvbmNpbGlhdGlvbk9yaWdpbmFsVG9wID0gMDsNCiAgICAgIGlmICh0aGlzLnNjcm9sbERlYm91bmNlVGltZXIpIHsNCiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuc2Nyb2xsRGVib3VuY2VUaW1lcik7DQogICAgICAgIHRoaXMuc2Nyb2xsRGVib3VuY2VUaW1lciA9IG51bGw7DQogICAgICB9DQoNCiAgICAgIC8vIOiuvue9ruWvuei0puaooeW8j+S4umZhbHNlDQogICAgICB0aGlzLmlzUmVjb25jaWxpYXRpb25Nb2RlID0gZmFsc2U7DQoNCiAgICAgIC8vIOa4hemZpOWvuei0pueKtuaAgQ0KICAgICAgdGhpcy5jbGVhclJlY29uY2lsaWF0aW9uU3RhdGUoKTsNCg0KICAgICAgLy8g6YeN5paw5Yqg6L295pWw5o2uDQogICAgICB0aGlzLmdldExpc3QoKTsNCg0KICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5bey6YCA5Ye65a+56LSm5qih5byPJyk7DQogICAgfSwNCiAgICAvKiog5a+56LSm5qih5byP5LiL55qE5o6S5bqP77yI5by65Yi25q2j5bqP77yJICovDQogICAgc29ydFdpbm5pbmdMaXN0Rm9yUmVjb25jaWxpYXRpb24oKSB7DQogICAgICBpZiAoIXRoaXMud2lubmluZ0xpc3QgfHwgdGhpcy53aW5uaW5nTGlzdC5sZW5ndGggPT09IDApIHsNCiAgIA0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgIA0KDQogICAgICAvLyDlr7nkuK3lpZborrDlvZXmjInmtYHmsLTlj7fmraPluo/mjpLluo8NCiAgICAgIHRoaXMud2lubmluZ0xpc3Quc29ydCgoYSwgYikgPT4gew0KICAgICAgICBjb25zdCBhU2VyaWFsTnVtYmVyID0gYS5zZXJpYWxOdW1iZXIgfHwgMDsNCiAgICAgICAgY29uc3QgYlNlcmlhbE51bWJlciA9IGIuc2VyaWFsTnVtYmVyIHx8IDA7DQogICAgICAgIHJldHVybiBhU2VyaWFsTnVtYmVyIC0gYlNlcmlhbE51bWJlcjsgLy8g5by65Yi25oyJ5rWB5rC05Y+35q2j5bqPDQogICAgICB9KTsNCg0KIA0KICAgIA0KDQogICAgICAvLyDlvLrliLbop6blj5FWdWXnmoTlk43lupTlvI/mm7TmlrANCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7DQoNCiAgICAgIC8vIOehruS/neihqOagvOmHjeaWsOa4suafkw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBpZiAodGhpcy4kcmVmcy53aW5uaW5nVGFibGUpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLndpbm5pbmdUYWJsZS5kb0xheW91dCgpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmoIforrDkuK3lpZborrDlvZXkuLrlt7Llr7notKYgKi8NCiAgICBtYXJrV2lubmluZ0FzUmVjb25jaWxlZCh3aW5JZCkgew0KICAgICAgaWYgKCF0aGlzLnJlY29uY2lsZWRXaW5uaW5nSWRzLmluY2x1ZGVzKHdpbklkKSkgew0KICAgICAgICB0aGlzLnJlY29uY2lsZWRXaW5uaW5nSWRzLnB1c2god2luSWQpOw0KICAgICAgICB0aGlzLnNhdmVSZWNvbmNpbGlhdGlvblN0YXRlKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5Y+W5raI5Lit5aWW6K6w5b2V55qE5a+56LSm54q25oCBICovDQogICAgdW5tYXJrV2lubmluZ0FzUmVjb25jaWxlZCh3aW5JZCkgew0KICAgICAgY29uc3QgaW5kZXggPSB0aGlzLnJlY29uY2lsZWRXaW5uaW5nSWRzLmluZGV4T2Yod2luSWQpOw0KICAgICAgaWYgKGluZGV4ID4gLTEpIHsNCiAgICAgICAgdGhpcy5yZWNvbmNpbGVkV2lubmluZ0lkcy5zcGxpY2UoaW5kZXgsIDEpOw0KICAgICAgICB0aGlzLnNhdmVSZWNvbmNpbGlhdGlvblN0YXRlKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5YiH5o2i5Lit5aWW6K6w5b2V55qE5a+56LSm54q25oCBICovDQogICAgdG9nZ2xlV2lubmluZ1JlY29uY2lsaWF0aW9uKHdpbklkKSB7DQogICAgICBpZiAodGhpcy5pc1dpbm5pbmdSZWNvbmNpbGVkKHdpbklkKSkgew0KICAgICAgICB0aGlzLnVubWFya1dpbm5pbmdBc1JlY29uY2lsZWQod2luSWQpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tYXJrV2lubmluZ0FzUmVjb25jaWxlZCh3aW5JZCk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5qOA5p+l5Lit5aWW6K6w5b2V5piv5ZCm5bey5a+56LSmICovDQogICAgaXNXaW5uaW5nUmVjb25jaWxlZCh3aW5JZCkgew0KICAgICAgcmV0dXJuIHRoaXMucmVjb25jaWxlZFdpbm5pbmdJZHMuaW5jbHVkZXMod2luSWQpOw0KICAgIH0sDQogICAgLyoqIOS/neWtmOWvuei0pueKtuaAgeWIsGxvY2FsU3RvcmFnZSAqLw0KICAgIHNhdmVSZWNvbmNpbGlhdGlvblN0YXRlKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVjb25jaWxpYXRpb25EYXRhID0gew0KICAgICAgICAgIGlzUmVjb25jaWxpYXRpb25Nb2RlOiB0aGlzLmlzUmVjb25jaWxpYXRpb25Nb2RlLA0KICAgICAgICAgIHJlY29uY2lsZWRXaW5uaW5nSWRzOiB0aGlzLnJlY29uY2lsZWRXaW5uaW5nSWRzLA0KICAgICAgICAgIG9yaWdpbmFsUXVlcnlQYXJhbXM6IHRoaXMub3JpZ2luYWxRdWVyeVBhcmFtcywNCiAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCkNCiAgICAgICAgfTsNCiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3dpbm5pbmdfcmVjb25jaWxpYXRpb25fc3RhdGUnLCBKU09OLnN0cmluZ2lmeShyZWNvbmNpbGlhdGlvbkRhdGEpKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmHdpbm5pbmflr7notKbnirbmgIHlpLHotKU6JywgZXJyb3IpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOS7jmxvY2FsU3RvcmFnZeaBouWkjeWvuei0pueKtuaAgSAqLw0KICAgIHJlc3RvcmVSZWNvbmNpbGlhdGlvblN0YXRlKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3Qgc2F2ZWREYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3dpbm5pbmdfcmVjb25jaWxpYXRpb25fc3RhdGUnKTsNCiAgICAgICAgaWYgKHNhdmVkRGF0YSkgew0KICAgICAgICAgIGNvbnN0IHJlY29uY2lsaWF0aW9uRGF0YSA9IEpTT04ucGFyc2Uoc2F2ZWREYXRhKTsNCg0KICAgICAgICAgIC8vIOaBouWkjeWvuei0puaooeW8j+eKtuaAgQ0KICAgICAgICAgIGlmIChyZWNvbmNpbGlhdGlvbkRhdGEuaXNSZWNvbmNpbGlhdGlvbk1vZGUpIHsNCiAgICAgICAgICAgIHRoaXMuaXNSZWNvbmNpbGlhdGlvbk1vZGUgPSB0cnVlOw0KICAgICAgICAgICAgdGhpcy5yZWNvbmNpbGVkV2lubmluZ0lkcyA9IHJlY29uY2lsaWF0aW9uRGF0YS5yZWNvbmNpbGVkV2lubmluZ0lkcyB8fCBbXTsNCiAgICAgICAgICAgIHRoaXMub3JpZ2luYWxRdWVyeVBhcmFtcyA9IHJlY29uY2lsaWF0aW9uRGF0YS5vcmlnaW5hbFF1ZXJ5UGFyYW1zIHx8IG51bGw7DQoNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmgaLlpI13aW5uaW5n5a+56LSm5qih5byP54q25oCBOicsIHsNCiAgICAgICAgICAgICAgcmVjb25jaWxlZENvdW50OiB0aGlzLnJlY29uY2lsZWRXaW5uaW5nSWRzLmxlbmd0aCwNCiAgICAgICAgICAgICAgaGFzT3JpZ2luYWxQYXJhbXM6ICEhdGhpcy5vcmlnaW5hbFF1ZXJ5UGFyYW1zDQogICAgICAgICAgICB9KTsNCg0KICAgICAgICAgICAgLy8g5Zyo5LiL5LiA5LiqdGlja+S4remHjeaWsOWKoOi9veaVsOaNruS7peehruS/neWvuei0puaooeW8j+ato+ehruW6lOeUqA0KICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5oGi5aSNd2lubmluZ+Wvuei0pueKtuaAgeWksei0pTonLCBlcnJvcik7DQogICAgICAgIC8vIOa4hemZpOaNn+Wdj+eahOaVsOaNrg0KICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnd2lubmluZ19yZWNvbmNpbGlhdGlvbl9zdGF0ZScpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOa4hemZpOWvuei0pueKtuaAgSAqLw0KICAgIGNsZWFyUmVjb25jaWxpYXRpb25TdGF0ZSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd3aW5uaW5nX3JlY29uY2lsaWF0aW9uX3N0YXRlJyk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmuIXpmaR3aW5uaW5n5a+56LSm54q25oCB5aSx6LSlOicsIGVycm9yKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDlpITnkIbkuIDplK7muIXnqbrkuovku7YgKi8NCiAgICBoYW5kbGVDbGVhckFsbERhdGEoZXZlbnQpIHsNCiAgICAgIGNvbnNvbGUubG9nKCd3aW5uaW5n6aG16Z2i5pS25Yiw5LiA6ZSu5riF56m65LqL5Lu2OicsIGV2ZW50LmRldGFpbCk7DQoNCiAgICAgIC8vIOWPquacieW9k+WJjemhtemdouWkhOS6juWvuei0puaooeW8j+aXtuaJjeWkhOeQhu+8jOmBv+WFjeS4jnJlY29yZOmhtemdouWGsueqgQ0KICAgICAgaWYgKHRoaXMuaXNSZWNvbmNpbGlhdGlvbk1vZGUpIHsNCiAgICAgICAgLy8g5re75Yqg55+t5pqC5bu26L+f77yM56Gu5L+d5LqL5Lu25aSE55CG55qE6aG65bqP5oCnDQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMuZXhpdFJlY29uY2lsaWF0aW9uTW9kZSgpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCd3aW5uaW5n6aG16Z2i5Zug5LiA6ZSu5riF56m65pON5L2c6YCA5Ye65a+56LSm5qih5byPJyk7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfmo4DmtYvliLDkuIDplK7muIXnqbrmk43kvZzvvIzlt7Loh6rliqjpgIDlh7rlr7notKbmqKHlvI8nKTsNCiAgICAgICAgfSwgMTAwKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDlpITnkIbmu5rliqjkuovku7YgKi8NCiAgICBoYW5kbGVTY3JvbGwoKSB7DQogICAgICBpZiAoIXRoaXMuaXNSZWNvbmNpbGlhdGlvbk1vZGUgfHwgIXRoaXMuJHJlZnMucmVjb25jaWxpYXRpb25Ob3RpY2UpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBub3RpY2VFbGVtZW50ID0gdGhpcy4kcmVmcy5yZWNvbmNpbGlhdGlvbk5vdGljZTsNCiAgICAgIGNvbnN0IHJlY3QgPSBub3RpY2VFbGVtZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpOw0KICAgICAgY29uc3Qgc2Nyb2xsVG9wID0gd2luZG93LnBhZ2VZT2Zmc2V0IHx8IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zY3JvbGxUb3A7DQoNCiAgICAgIC8vIOWIneWni+WMluWOn+Wni+S9jee9ru+8iOWPquWcqOesrOS4gOasoeiuoeeul++8iQ0KICAgICAgaWYgKHRoaXMucmVjb25jaWxpYXRpb25PcmlnaW5hbFRvcCA9PT0gMCAmJiAhdGhpcy5pc1JlY29uY2lsaWF0aW9uRml4ZWQpIHsNCiAgICAgICAgdGhpcy5yZWNvbmNpbGlhdGlvbk9yaWdpbmFsVG9wID0gc2Nyb2xsVG9wICsgcmVjdC50b3A7DQogICAgICB9DQoNCiAgICAgIC8vIOS9v+eUqOmYsuaKluWkhOeQhu+8jOmBv+WFjemikee5geWIh+aNog0KICAgICAgaWYgKCF0aGlzLnNjcm9sbERlYm91bmNlVGltZXIpIHsNCiAgICAgICAgdGhpcy5zY3JvbGxEZWJvdW5jZVRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgLy8g5aaC5p6c5a+56LSm5o+Q56S65qGG55qE6aG26YOo56a75byA5bGP5bmV6LaF6L+H6ZiI5YC877yM5YiZ5Zu65a6a5a6DDQogICAgICAgICAgaWYgKHJlY3QudG9wIDw9IDAgJiYgIXRoaXMuaXNSZWNvbmNpbGlhdGlvbkZpeGVkKSB7DQogICAgICAgICAgICB0aGlzLmlzUmVjb25jaWxpYXRpb25GaXhlZCA9IHRydWU7DQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIOWmguaenOa7muWKqOWbnuWIsOWOn+Wni+S9jee9rumZhOi/ke+8jOWImeWPlua2iOWbuuWumg0KICAgICAgICAgIGVsc2UgaWYgKHNjcm9sbFRvcCA8PSB0aGlzLnJlY29uY2lsaWF0aW9uT3JpZ2luYWxUb3AgLSAyMCAmJiB0aGlzLmlzUmVjb25jaWxpYXRpb25GaXhlZCkgew0KICAgICAgICAgICAgdGhpcy5pc1JlY29uY2lsaWF0aW9uRml4ZWQgPSBmYWxzZTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICB0aGlzLnNjcm9sbERlYm91bmNlVGltZXIgPSBudWxsOw0KICAgICAgICB9LCAxNik7IC8vIOe6pjYwZnBz55qE5pu05paw6aKR546HDQogICAgICB9DQogICAgfSwNCiAgICAvKiog5pKk6ZSA57uT566X5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQ2FuY2VsUGF5KHJvdykgew0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5pKk6ZSA6K+l56yU5aWW6YeR55qE57uT566X77yfJykudGhlbigoKSA9PiB7DQogICAgICAgIHJldHVybiBjYW5jZWxQYXkocm93LndpbklkKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5pKk6ZSA57uT566X5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog6I635Y+W546p5rOV5YiX6KGoICovDQogICAgZ2V0R2FtZU1ldGhvZHMoKSB7DQogICAgICBnZXRBbGxNZXRob2RzKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgnZ2FtZS9TRVRfTUVUSE9EU19EQVRBJywgcmVzcG9uc2Uucm93cykNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog6I635Y+W5rWB5rC05Y+35YiX6KGoICovDQogICAgZ2V0U2VyaWFsTnVtYmVyTGlzdCgpIHsNCiAgICAgIC8vIOazqOaEj++8mmxpc3RTZXJpYWzmjqXlj6Plt7Lnu4/lnKjlkI7nq6/lrp7njrDkuobnlKjmiLfpmpTnprvvvIzmiYDku6Xov5nph4zojrflj5bnmoTlsLHmmK/lvZPliY3nlKjmiLfnmoTmtYHmsLTlj7cNCiAgICAgIGxpc3RTZXJpYWwoeyBwYWdlTnVtOiAxLCBwYWdlU2l6ZTogMTAwMCB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5yb3dzICYmIHJlc3BvbnNlLnJvd3MubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOaPkOWPlua1geawtOWPt+W5tuWOu+mHjeaOkuW6jw0KICAgICAgICAgIHRoaXMuc2VyaWFsTnVtYmVyTGlzdCA9IFsuLi5uZXcgU2V0KHJlc3BvbnNlLnJvd3MubWFwKGl0ZW0gPT4gaXRlbS5zZXJpYWxOdW1iZXJzKSldDQogICAgICAgICAgICAuZmlsdGVyKGl0ZW0gPT4gaXRlbSAhPSBudWxsKQ0KICAgICAgICAgICAgLnNvcnQoKGEsIGIpID0+IGIgLSBhKTsgLy8g6ZmN5bqP5o6S5YiX77yM5pyA5paw55qE5Zyo5YmN6Z2iDQogICAgICAgICAgDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ+ayoeacieaJvuWIsOa1geawtOWPt+aVsOaNru+8jOWwneivleS7juS4reWlluiusOW9leiOt+WPlicpOw0KICAgICAgICAgIHRoaXMuZ2V0U2VyaWFsTnVtYmVyRnJvbVdpbm5pbmcoKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bmtYHmsLTlj7fliJfooajlpLHotKXvvIzlsJ3or5Xku47kuK3lpZborrDlvZXojrflj5Y6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLmdldFNlcmlhbE51bWJlckZyb21XaW5uaW5nKCk7DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOS7juS4reWlluiusOW9leS4reiOt+WPlua1geawtOWPt+WIl+ihqCAqLw0KICAgIGdldFNlcmlhbE51bWJlckZyb21XaW5uaW5nKCkgew0KICAgICAgLy8g5LuO5Lit5aWW6K6w5b2V5Lit5o+Q5Y+W5rWB5rC05Y+3DQogICAgICBsaXN0V2lubmluZyh7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDAwIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2Uucm93cyAmJiByZXNwb25zZS5yb3dzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAvLyDku47kuK3lpZborrDlvZXkuK3mj5Dlj5bmtYHmsLTlj7flubbljrvph43mjpLluo8NCiAgICAgICAgICB0aGlzLnNlcmlhbE51bWJlckxpc3QgPSBbLi4ubmV3IFNldChyZXNwb25zZS5yb3dzLm1hcChpdGVtID0+IGl0ZW0uc2VyaWFsTnVtYmVyKSldDQogICAgICAgICAgICAuZmlsdGVyKGl0ZW0gPT4gaXRlbSAhPSBudWxsKQ0KICAgICAgICAgICAgLnNvcnQoKGEsIGIpID0+IGIgLSBhKTsgLy8g6ZmN5bqP5o6S5YiX77yM5pyA5paw55qE5Zyo5YmN6Z2iDQogICAgICAgICAgY29uc29sZS5sb2coJ+S7juS4reWlluiusOW9leiOt+WPlueahOa1geawtOWPt+WIl+ihqDonLCB0aGlzLnNlcmlhbE51bWJlckxpc3QpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S7juS4reWlluiusOW9leiOt+WPlua1geawtOWPt+Wksei0pTonLCBlcnJvcik7DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWxleW8gC/mlLbotbflhajpg6ggKi8NCiAgICB0b2dnbGVFeHBhbmRBbGwoKSB7DQogICAgICB0aGlzLmFsbEV4cGFuZGVkID0gIXRoaXMuYWxsRXhwYW5kZWQ7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGlmICh0aGlzLmFsbEV4cGFuZGVkKSB7DQogICAgICAgICAgLy8g5bGV5byA5omA5pyJ6KGMDQogICAgICAgICAgdGhpcy53aW5uaW5nTGlzdC5mb3JFYWNoKChyb3csIGluZGV4KSA9PiB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLndpbm5pbmdUYWJsZS50b2dnbGVSb3dFeHBhbnNpb24ocm93LCB0cnVlKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDmlLbotbfmiYDmnInooYwNCiAgICAgICAgICB0aGlzLndpbm5pbmdMaXN0LmZvckVhY2goKHJvdywgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMud2lubmluZ1RhYmxlLnRvZ2dsZVJvd0V4cGFuc2lvbihyb3csIGZhbHNlKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6YeN572u5bGV5byA54q25oCBICovDQogICAgcmVzZXRFeHBhbmRTdGF0ZSgpIHsNCiAgICAgIHRoaXMuYWxsRXhwYW5kZWQgPSBmYWxzZTsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgLy8g5pS26LW35omA5pyJ6KGMDQogICAgICAgIHRoaXMud2lubmluZ0xpc3QuZm9yRWFjaCgocm93KSA9PiB7DQogICAgICAgICAgdGhpcy4kcmVmcy53aW5uaW5nVGFibGUudG9nZ2xlUm93RXhwYW5zaW9uKHJvdywgZmFsc2UpOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOivhuWIq+ahhui+k+WFpeWkhOeQhiAqLw0KICAgIGhhbmRsZVNoaUJpZUlucHV0KCkgew0KICAgICAgLy8g6Ziy5oqW5aSE55CG77yM6YG/5YWN6aKR57mB5pCc57SiDQogICAgICBpZiAodGhpcy5zaGlCaWVTZWFyY2hUaW1lcikgew0KICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5zaGlCaWVTZWFyY2hUaW1lcik7DQogICAgICB9DQogICAgICB0aGlzLnNoaUJpZVNlYXJjaFRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLnNoaWJpZSAmJiB0aGlzLnF1ZXJ5UGFyYW1zLnNoaWJpZS50cmltKCkpIHsNCiAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgICAgIH0NCiAgICAgIH0sIDUwMCk7IC8vIDUwMG1zIOW7tui/nw0KICAgIH0sDQogICAgLyoqIOivhuWIq+ahhua4heepuuWkhOeQhiAqLw0KICAgIGhhbmRsZVNoaUJpZUNsZWFyKCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zaGliaWUgPSAnJzsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8qKiDkuK3lpZbph5Hpop3mjpLluo/mlrnms5UgKi8NCiAgICBzb3J0QnlBbW91bnQoYSwgYikgew0KICAgICAgY29uc3QgYW1vdW50QSA9IHBhcnNlRmxvYXQoYS53aW5BbW91bnQpIHx8IDA7DQogICAgICBjb25zdCBhbW91bnRCID0gcGFyc2VGbG9hdChiLndpbkFtb3VudCkgfHwgMDsNCiAgICAgIHJldHVybiBhbW91bnRBIC0gYW1vdW50QjsNCiAgICB9LA0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6o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file": "index.vue", "sourceRoot": "src/views/game/winning", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 移动端搜索按钮 -->\r\n    <div v-if=\"isMobile\" class=\"mobile-search-toggle\">\r\n      <el-button\r\n        type=\"primary\"\r\n        icon=\"el-icon-search\"\r\n        size=\"small\"\r\n        @click=\"toggleMobileSearch\"\r\n        class=\"mobile-search-btn\">\r\n        {{ showMobileSearch ? '收起搜索' : '展开搜索' }}\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-container\" v-show=\"isMobile ? showMobileSearch : showSearch\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"!isMobile\" label-width=\"80px\" :class=\"['search-form', { 'mobile-search-form': isMobile }]\">\r\n        <el-form-item label=\"中奖用户\" prop=\"userId\">\r\n          <el-select\r\n            v-model=\"queryParams.userId\"\r\n            placeholder=\"请选择中奖用户\"\r\n            clearable\r\n            filterable\r\n            @change=\"handleQuery\"\r\n            prefix-icon=\"el-icon-user\"\r\n            :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in userList\"\r\n              :key=\"item.userId\"\r\n              :label=\"item.name\"\r\n              :value=\"item.userId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"流水号\" prop=\"serialNumber\">\r\n          <el-select\r\n            v-model=\"queryParams.serialNumber\"\r\n            placeholder=\"请选择流水号\"\r\n            clearable\r\n            filterable\r\n            @change=\"handleQuery\"\r\n            prefix-icon=\"el-icon-document\"\r\n            :style=\"isMobile ? 'width: 100%;' : 'width: 200px;'\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in serialNumberList\"\r\n              :key=\"item\"\r\n              :label=\"item\"\r\n              :value=\"item\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n      <!-- <el-form-item label=\"彩种名称\" prop=\"lotteryId\">\r\n        <el-select\r\n          v-model=\"queryParams.lotteryId\"\r\n          placeholder=\"请选择彩种\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option label=\"福彩3D\" value=\"1\" />\r\n          <el-option label=\"体彩排三\" value=\"2\" />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"玩法名称\" prop=\"methodId\">\r\n        <el-select\r\n          v-model=\"queryParams.methodId\"\r\n          placeholder=\"请选择玩法名称\"\r\n          clearable\r\n          filterable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in gameMethodsData\"\r\n            :key=\"item.methodId\"\r\n            :label=\"item.methodName\"\r\n            :value=\"item.methodId\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"中奖期号\" prop=\"issueNumber\">\r\n        <el-select\r\n          v-model=\"queryParams.issueNumber\"\r\n          placeholder=\"请选择中奖期号\"\r\n          clearable\r\n          filterable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in issueList\"\r\n            :key=\"item.qihao\"\r\n            :label=\"item.qihao\"\r\n            :value=\"item.qihao\"\r\n          />\r\n        </el-select>\r\n      </el-form-item> -->\r\n\r\n        <el-form-item label=\"识别框\" prop=\"shibie\">\r\n          <el-input\r\n            v-model=\"queryParams.shibie\"\r\n            placeholder=\"请输入识别内容（支持模糊搜索）\"\r\n            clearable\r\n            type=\"textarea\"\r\n            prefix-icon=\"el-icon-search\"\r\n            :style=\"isMobile ? 'width: 100%;' : 'width: 350px;'\"\r\n            @input=\"handleShiBieInput\"\r\n            @clear=\"handleShiBieClear\"\r\n          />\r\n          <div v-if=\"queryParams.shibie && queryParams.shibie.trim()\" class=\"search-tip\">\r\n            <i class=\"el-icon-info\"></i>\r\n            模糊搜索模式：将显示所有包含\"{{ queryParams.shibie.trim() }}\"的记录\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\" class=\"search-btn\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" class=\"reset-btn\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n   <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n       <el-col :span=\"1.5\">\r\n        <el-popconfirm\r\n          title=\"确认要批量赔付选中的奖金吗？\"\r\n          @confirm=\"handleBatchPay\"\r\n        >\r\n          <el-button\r\n            slot=\"reference\"\r\n            type=\"primary\"\r\n            plain\r\n            icon=\"el-icon-s-finance\"\r\n            size=\"mini\"\r\n            :disabled=\"multiple\"\r\n            v-hasPermi=\"['game:winning:edit']\"\r\n          >批量赔付</el-button>\r\n        </el-popconfirm>\r\n      </el-col>\r\n   \r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['game:winning:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['game:winning:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row> -->\r\n\r\n    <!-- 工具栏 -->\r\n    <div class=\"toolbar-container\" :class=\"{ 'mobile-toolbar': isMobile }\">\r\n      <div class=\"toolbar-content\">\r\n        <div class=\"toolbar-left\" :class=\"{ 'mobile-toolbar-left': isMobile }\">\r\n          <el-button\r\n            type=\"primary\"\r\n            :icon=\"allExpanded ? 'el-icon-minus' : 'el-icon-plus'\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"toggleExpandAll\"\r\n            class=\"toolbar-btn expand-btn\"\r\n          >{{ isMobile ? (allExpanded ? '收起' : '展开') : (allExpanded ? '收起全部' : '展开全部') }}</el-button>\r\n          <el-button\r\n            type=\"info\"\r\n            icon=\"el-icon-refresh\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"getList\"\r\n            class=\"toolbar-btn refresh-btn\"\r\n          >{{ isMobile ? '刷新' : '刷新数据' }}</el-button>\r\n          <el-button\r\n            type=\"warning\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"handleExport\"\r\n            v-hasPermi=\"['game:winning:export']\"\r\n            class=\"toolbar-btn export-btn\"\r\n          >导出</el-button>\r\n          <el-button\r\n            :type=\"isReconciliationMode ? 'danger' : 'success'\"\r\n            :icon=\"isReconciliationMode ? 'el-icon-close' : 'el-icon-check'\"\r\n            :size=\"isMobile ? 'mini' : 'small'\"\r\n            @click=\"toggleReconciliationMode\"\r\n            :loading=\"reconciliationLoading\"\r\n            class=\"toolbar-btn reconciliation-btn\"\r\n          >{{ isReconciliationMode ? '退出对账' : '对账' }}</el-button>\r\n        </div>\r\n        <div class=\"toolbar-right\">\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 对账模式提示框 -->\r\n    <div v-if=\"isReconciliationMode\" ref=\"reconciliationNotice\" class=\"reconciliation-notice\" :class=\"{ 'reconciliation-fixed': isReconciliationFixed }\">\r\n      <div class=\"reconciliation-alert\">\r\n        <div class=\"reconciliation-header\">\r\n          <i class=\"el-icon-info\"></i>\r\n          <span class=\"reconciliation-title\">对账模式</span>\r\n        </div>\r\n        <div class=\"reconciliation-content\">\r\n          <p class=\"reconciliation-instruction\">\r\n            <strong style=\"color: #ff4d4f;\">操作提示：</strong>点击表格中蓝色虚线框的\r\n            <span class=\"highlight-amount\">\"中奖金额: ￥xxx.xx\"</span>\r\n            区域即可标记该记录为已对账！\r\n          </p>\r\n          <div class=\"reconciliation-stats\">\r\n            <span class=\"stats-text\">\r\n              已对账中奖记录数量：\r\n              <span class=\"reconciled-count\">{{ reconciledWinningIds.length }}</span> /\r\n              <span class=\"total-count\">{{ winningList.length }}</span>\r\n            </span>\r\n            <span class=\"stats-text\" style=\"margin-left: 20px;\">\r\n              已对账中奖金额：\r\n              <span class=\"reconciled-amount\">￥{{ formatAmount(reconciledWinningAmount) }}</span> /\r\n              <span class=\"total-amount\">￥{{ formatAmount(totalWinningAmount) }}</span>\r\n            </span>\r\n            <el-button\r\n              v-if=\"isReconciliationFixed\"\r\n              type=\"danger\"\r\n              size=\"mini\"\r\n              icon=\"el-icon-close\"\r\n              @click=\"exitReconciliationMode\"\r\n              class=\"exit-reconciliation-btn\">\r\n              退出对账\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格 -->\r\n    <div class=\"table-container\" :class=\"{ 'mobile-table-container': isMobile }\">\r\n      <!-- 移动端卡片式布局 -->\r\n      <div v-if=\"isMobile\" class=\"mobile-card-container\" :class=\"{ 'reconciliation-mode': isReconciliationMode }\">\r\n        <!-- 移动端对账模式提示 -->\r\n        <div v-if=\"isReconciliationMode\" class=\"mobile-reconciliation-notice\">\r\n          <div class=\"reconciliation-header\">\r\n            <i class=\"el-icon-info\"></i>\r\n            <span>对账模式</span>\r\n            <el-button type=\"text\" @click=\"exitReconciliationMode\" class=\"exit-btn\">\r\n              <i class=\"el-icon-close\"></i>\r\n            </el-button>\r\n          </div>\r\n          <div class=\"reconciliation-stats\">\r\n            <span class=\"stat-item\">已对账: <strong>{{ reconciledWinningIds.length }}</strong></span>\r\n            <span class=\"stat-item\">总额: <strong>¥{{ reconciledWinningAmount.toFixed(2) }}</strong></span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 移动端卡片列表 -->\r\n        <div v-for=\"(item, index) in winningList\" :key=\"item.winId\" class=\"mobile-card\"\r\n             :class=\"{ 'reconciled': isReconciliationMode && isWinningReconciled(item.winId) }\"\r\n             @click=\"!isReconciliationMode ? toggleMobileCard(item, index) : null\">\r\n\r\n          <!-- 卡片头部 -->\r\n          <div class=\"card-header\">\r\n            <!-- 左侧点击区域：展开/收起卡片 -->\r\n            <div class=\"card-header-left\" @click=\"isReconciliationMode ? toggleMobileCard(item, index) : null\">\r\n              <div class=\"winning-info\">\r\n                <div class=\"user-name\">{{ getUserName(item.userId) }}</div>\r\n                <div class=\"winning-meta\">\r\n                  <span class=\"lottery-type\">\r\n                    <el-tag v-if=\"item.lotteryId === 1\" type=\"danger\" size=\"mini\">福彩</el-tag>\r\n                    <el-tag v-else-if=\"item.lotteryId === 2\" type=\"primary\" size=\"mini\">体彩</el-tag>\r\n                    <span v-else>{{ item.lotteryId }}</span>\r\n                  </span>\r\n                  <span class=\"method-name\">{{ getMethodName(item.methodId) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右侧点击区域：对账操作（仅在对账模式下可点击） -->\r\n            <div class=\"card-header-right\"\r\n                 :class=\"{ 'reconciliation-clickable': isReconciliationMode }\"\r\n                 @click=\"isReconciliationMode ? toggleWinningReconciliation(item.winId) : null\">\r\n              <div class=\"amount-info\">\r\n                <div class=\"win-amount\">¥{{ formatAmount(item.winAmount) }}</div>\r\n                <div v-if=\"isReconciliationMode\" class=\"reconciliation-status\"\r\n                     :class=\"{ 'reconciled': isWinningReconciled(item.winId) }\">\r\n                  {{ isWinningReconciled(item.winId) ? '已对账' : '待对账' }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 卡片内容（展开时显示） -->\r\n          <div v-if=\"item.mobileExpanded\" class=\"card-content\">\r\n            <div class=\"detail-row\">\r\n              <span class=\"detail-label\">流水号:</span>\r\n              <span class=\"detail-value\">{{ item.serialNumber }}</span>\r\n            </div>\r\n            <div class=\"detail-row\">\r\n              <span class=\"detail-label\">下注号码:</span>\r\n              <div class=\"detail-value\">\r\n                <div v-if=\"item.betNumber\">\r\n                  <!-- 胆拖玩法显示 -->\r\n                  <div v-if=\"item.methodId >= 44 && item.methodId <= 59\">\r\n                    <template v-if=\"JSON.parse(item.betNumber).numbers.length > 0\">\r\n                      <el-tag type=\"danger\" size=\"mini\" style=\"margin: 1px;\">\r\n                        胆{{ JSON.parse(item.betNumber).numbers[0].danma }}\r\n                      </el-tag>\r\n                      <el-tag size=\"mini\" style=\"margin: 1px;\">\r\n                        拖{{ JSON.parse(item.betNumber).numbers[0].tuoma }}\r\n                      </el-tag>\r\n                    </template>\r\n                  </div>\r\n                  <!-- 跨度玩法显示 -->\r\n                  <div v-else-if=\"item.methodId >= 60 && item.methodId <= 69\">\r\n                    <el-tag size=\"mini\">跨度{{ item.methodId - 60 }}</el-tag>\r\n                  </div>\r\n                  <!-- 其他玩法显示 -->\r\n                  <div v-else>\r\n                    <template v-if=\"typeof item.betNumber === 'string'\">\r\n                      <div v-for=\"(num, numIndex) in JSON.parse(item.betNumber).numbers\" :key=\"numIndex\">\r\n                        <el-tag v-for=\"(value, key) in num\" :key=\"key\" size=\"mini\" style=\"margin: 1px;\">\r\n                          {{ value }}\r\n                        </el-tag>\r\n                      </div>\r\n                    </template>\r\n                    <span v-else>{{ item.betNumbers }}</span>\r\n                  </div>\r\n                </div>\r\n                <span v-else>{{ item.betNumbers }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"detail-row\">\r\n              <span class=\"detail-label\">中奖号码:</span>\r\n              <div class=\"detail-value\">\r\n                <div v-if=\"item.winningNumbers\">\r\n                  <template v-if=\"typeof item.winningNumbers === 'string'\">\r\n                    <div v-for=\"(winItem, winIndex) in JSON.parse(item.winningNumbers).winning\" :key=\"winIndex\">\r\n                      <el-tag type=\"success\" size=\"mini\" style=\"margin: 1px;\">\r\n                        <template v-if=\"winItem.b === undefined && winItem.c === undefined\">\r\n                          {{ winItem.a }}\r\n                        </template>\r\n                        <template v-else-if=\"winItem.c === undefined\">\r\n                          {{ winItem.a }}.{{ winItem.b }}\r\n                        </template>\r\n                        <template v-else>\r\n                          {{ winItem.a }}.{{ winItem.b }}.{{ winItem.c }}\r\n                        </template>\r\n                      </el-tag>\r\n                    </div>\r\n                  </template>\r\n                  <span v-else>{{ item.winningNumbers }}</span>\r\n                </div>\r\n                <span v-else>{{ item.winningNumbers }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 桌面端表格 -->\r\n      <el-table v-else\r\n        v-loading=\"loading\"\r\n        :data=\"winningList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        @row-click=\"handleRowClick\"\r\n        ref=\"winningTable\"\r\n        border\r\n        :header-cell-style=\"{ background: '#f8f9fa', color: '#606266', fontWeight: 'bold' }\"\r\n      >\r\n        <el-table-column type=\"expand\" width=\"60\">\r\n          <template slot-scope=\"props\">\r\n            <div class=\"expand-content\">\r\n              <div class=\"expand-header\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>详细信息</span>\r\n              </div>\r\n              <el-form label-position=\"left\" inline class=\"table-expand\">\r\n                <el-form-item label=\"号码识别\" class=\"full-width\">\r\n                  <div class=\"expand-value\">{{ props.row.shibie || '无' }}</div>\r\n                </el-form-item>\r\n                <el-form-item label=\"下注号码\" class=\"full-width\">\r\n                  <div class=\"expand-value\">\r\n                    <div v-if=\"props.row.betNumber\" class=\"number-tags\">\r\n                      <!-- 胆拖玩法详情显示 -->\r\n                      <div v-if=\"props.row.methodId >= 44 && props.row.methodId <= 59\">\r\n                        <template v-if=\"JSON.parse(props.row.betNumber).numbers.length > 0\">\r\n                          <el-tag type=\"danger\" effect=\"dark\" size=\"medium\" style=\"margin: 4px; font-size: 16px; font-weight: bold;\">\r\n                            胆码: {{ JSON.parse(props.row.betNumber).numbers[0].danma }}\r\n                          </el-tag>\r\n                          <el-tag type=\"info\" effect=\"dark\" size=\"medium\" style=\"margin: 4px; font-size: 16px; font-weight: bold;\">\r\n                            拖码: {{ JSON.parse(props.row.betNumber).numbers[0].tuoma }}\r\n                          </el-tag>\r\n                        </template>\r\n                      </div>\r\n\r\n                      <!-- 跨度玩法详情显示 -->\r\n                      <div v-else-if=\"props.row.methodId >= 60 && props.row.methodId <= 69\">\r\n                        <el-tag type=\"warning\" effect=\"dark\" size=\"medium\" style=\"margin: 4px; font-size: 16px; font-weight: bold;\">\r\n                          跨度值: {{ props.row.methodId - 60 }}\r\n                        </el-tag>\r\n                      </div>\r\n\r\n                      <!-- 原有玩法详情显示 -->\r\n                      <div v-else>\r\n                        <el-tag\r\n                          v-for=\"(item, index) in JSON.parse(props.row.betNumber).numbers\"\r\n                          :key=\"index\"\r\n                          type=\"primary\"\r\n                          effect=\"dark\"\r\n                          size=\"small\"\r\n                          class=\"number-tag bet-tag\"\r\n                        >\r\n                          {{ Object.values(item).join('.') }}\r\n                        </el-tag>\r\n                      </div>\r\n                    </div>\r\n                    <span v-else class=\"no-data\">{{ props.row.betNumbers || '无' }}</span>\r\n                  </div>\r\n                </el-form-item>\r\n                <el-form-item label=\"中奖号码\" class=\"full-width\">\r\n                  <div class=\"expand-value\">\r\n                    <div v-if=\"props.row.winningNumbers\" class=\"number-tags\">\r\n                      <template v-if=\"typeof props.row.winningNumbers === 'string'\">\r\n                        <el-tag\r\n                          v-for=\"(item, index) in JSON.parse(props.row.winningNumbers).winning\"\r\n                          :key=\"index\"\r\n                          type=\"success\"\r\n                          effect=\"dark\"\r\n                          size=\"small\"\r\n                          class=\"number-tag winning-tag\"\r\n                        >\r\n                          <template v-if=\"item.b === undefined && item.c === undefined\">\r\n                            {{ item.a }}\r\n                          </template>\r\n                          <template v-else-if=\"item.c === undefined\">\r\n                            {{ item.a }}.{{ item.b }}\r\n                          </template>\r\n                          <template v-else>\r\n                            {{ item.a }}.{{ item.b }}.{{ item.c }}\r\n                          </template>\r\n                        </el-tag>\r\n                      </template>\r\n                      <template v-else>\r\n                        <span class=\"no-data\">{{ props.row.winningNumbers }}</span>\r\n                      </template>\r\n                    </div>\r\n                    <span v-else class=\"no-data\">{{ props.row.winningNumbers || '无' }}</span>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </template>\r\n      </el-table-column>\r\n        <el-table-column label=\"中奖用户\" align=\"center\" prop=\"userId\" min-width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"user-info\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>{{ getUserName(scope.row.userId) }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"彩种\" align=\"center\" prop=\"lotteryId\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"lottery-type\">\r\n              <el-tag v-if=\"scope.row.lotteryId === 1\" type=\"danger\" effect=\"dark\" size=\"small\" class=\"lottery-tag\">福彩</el-tag>\r\n              <el-tag v-else-if=\"scope.row.lotteryId === 2\" type=\"primary\" effect=\"dark\" size=\"small\" class=\"lottery-tag\">体彩</el-tag>\r\n              <span v-else class=\"lottery-unknown\">{{ scope.row.lotteryId }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"玩法名称\" align=\"center\" prop=\"methodId\" min-width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"method-info\">\r\n              <i class=\"el-icon-star-on\"></i>\r\n              <span>{{ getMethodName(scope.row.methodId) }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      <el-table-column label=\"下注号码\" align=\"center\" prop=\"betNumber\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.betNumber\">\r\n            <!-- 胆拖玩法显示 (method_id 44-59) -->\r\n            <div v-if=\"scope.row.methodId >= 44 && scope.row.methodId <= 59\" style=\"white-space: nowrap;\">\r\n              <template v-if=\"JSON.parse(scope.row.betNumber).numbers.length > 0\">\r\n                <el-tag type=\"danger\" effect=\"dark\" size=\"mini\" style=\"margin: 1px; font-weight: bold;\">\r\n                  胆{{ JSON.parse(scope.row.betNumber).numbers[0].danma }}\r\n                </el-tag>\r\n                <el-tag effect=\"dark\" size=\"mini\" style=\"margin: 1px; font-weight: bold;\">\r\n                  拖{{ JSON.parse(scope.row.betNumber).numbers[0].tuoma }}\r\n                </el-tag>\r\n              </template>\r\n            </div>\r\n\r\n            <!-- 跨度玩法显示 (method_id 60-69) -->\r\n            <div v-else-if=\"scope.row.methodId >= 60 && scope.row.methodId <= 69\" style=\"white-space: nowrap;\">\r\n              <el-tag  effect=\"dark\" size=\"mini\" style=\"margin: 1px; font-weight: bold;\">\r\n                跨度{{ scope.row.methodId - 60 }}\r\n              </el-tag>\r\n            </div>\r\n\r\n            <!-- 原有玩法显示保持不变 -->\r\n            <div v-else style=\"white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">\r\n              <div v-for=\"(item, index) in JSON.parse(scope.row.betNumber).numbers\" :key=\"index\" style=\"display: inline-block;\">\r\n                <el-tag\r\n                  type=\"primary\"\r\n                  effect=\"dark\"\r\n                  size=\"small\"\r\n                  style=\"margin: 2px; font-weight: bold;\"\r\n                >\r\n                  {{ Object.values(item).join('.') }}\r\n                </el-tag>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <span v-else style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.betNumbers }}</span>\r\n        </template>\r\n      </el-table-column>\r\n         <el-table-column label=\"中奖号码\" align=\"center\" prop=\"winningNumbers\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.winningNumbers\" style=\"white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">\r\n            <template v-if=\"typeof scope.row.winningNumbers === 'string'\">\r\n              <div v-for=\"(item, index) in JSON.parse(scope.row.winningNumbers).winning\" :key=\"index\"\r\n                style=\"display: inline-block;\">\r\n                <el-tag type=\"success\" effect=\"dark\" size=\"small\" style=\"margin: 2px; font-weight: bold;\">\r\n                  <template v-if=\"item.b === undefined && item.c === undefined\">\r\n                    {{ item.a }}\r\n                  </template>\r\n                  <template v-else-if=\"item.c === undefined\">\r\n                    {{ item.a }}.{{ item.b }}\r\n                  </template>\r\n                  <template v-else>\r\n                    {{ item.a }}.{{ item.b }}.{{ item.c }}\r\n                  </template>\r\n                </el-tag>\r\n              </div>\r\n            </template>\r\n            <template v-else>\r\n              <span style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.winningNumbers }}</span>\r\n            </template>\r\n          </div>\r\n          <span v-else style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.winningNumbers }}</span>\r\n        </template>\r\n      </el-table-column>\r\n    \r\n        <el-table-column label=\"流水号\" align=\"center\" prop=\"serialNumber\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"serial-number\">\r\n              <span class=\"serial-value\">{{ scope.row.serialNumber }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"中奖金额\" align=\"center\" prop=\"winAmount\" min-width=\"120\" sortable :sort-method=\"sortByAmount\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"isReconciliationMode\"\r\n                 class=\"reconciliation-amount-container\"\r\n                 :class=\"{ 'reconciled': isWinningReconciled(scope.row.winId) }\"\r\n                 @click.stop=\"toggleWinningReconciliation(scope.row.winId)\">\r\n              <span class=\"reconciliation-amount-text\">\r\n                中奖金额: {{ formatAmount(scope.row.winAmount) }}\r\n              </span>\r\n              <span class=\"reconciliation-status-badge\"\r\n                    :class=\"{ 'reconciled': isWinningReconciled(scope.row.winId) }\">\r\n                {{ isWinningReconciled(scope.row.winId) ? '已对账' : '待对账' }}\r\n              </span>\r\n            </div>\r\n            <span v-else class=\"amount-simple\">{{ formatAmount(scope.row.winAmount) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      <!-- <el-table-column label=\"是否赔付\" align=\"center\" prop=\"isPaid\">\r\n        <template slot-scope=\"scope\">\r\n          <span style=\"font-size: 14px; font-weight: bold;\">{{ scope.row.isPaid === 1 ? '是' : '否' }}</span>\r\n        </template>\r\n      </el-table-column> -->\r\n   \r\n      <!-- <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popconfirm\r\n            title=\"确认要赔付该笔奖金吗？\"\r\n            @confirm=\"handlePay(scope.row)\"\r\n            v-if=\"scope.row.isPaid === 0\"\r\n          >\r\n            <el-button\r\n              slot=\"reference\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-s-finance\"\r\n              style=\"font-size: 14px; font-weight: bold;\"\r\n              v-hasPermi=\"['game:winning:edit']\"\r\n            >赔付</el-button>\r\n          </el-popconfirm>\r\n          <span v-else style=\"color: #56575AFF; font-size: 14px; font-weight: 700;\">已赔付</span>\r\n        </template>\r\n      </el-table-column> -->\r\n      </el-table>\r\n    </div>\r\n\r\n    <pagination\r\n      v-show=\"total > 0 && !isReconciliationMode\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改中奖管理对话框 -->\r\n    <!-- <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body :close-on-click-modal=\"false\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"中奖用户\" prop=\"userId\">\r\n          <el-input v-model=\"form.userId\" placeholder=\"请输入中奖用户\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"玩法名称\" prop=\"methodId\">\r\n          <el-input v-model=\"form.methodId\" placeholder=\"请输入玩法名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"下注号码\" prop=\"betNumber\">\r\n          <el-input v-model=\"form.betNumber\" placeholder=\"请输入下注号码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"彩种名称\" prop=\"lotteryId\">\r\n          <el-input v-model=\"form.lotteryId\" placeholder=\"请输入彩种名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"中奖期号\" prop=\"issueNumber\">\r\n          <el-input v-model=\"form.issueNumber\" placeholder=\"请输入中奖期号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"中奖金额\" prop=\"winAmount\">\r\n          <el-input v-model=\"form.winAmount\" placeholder=\"请输入中奖金额\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否赔付\" prop=\"isPaid\">\r\n          <el-input v-model=\"form.isPaid\" placeholder=\"请输入是否赔付\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"中奖号码\" prop=\"winningNumbers\">\r\n          <el-input v-model=\"form.winningNumbers\" placeholder=\"请输入中奖号码\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listWinning, getWinning, delWinning, addWinning, updateWinning, exportWinning } from \"@/api/game/winning\"\r\nimport { listCustomer } from \"@/api/game/customer\"\r\nimport { mapState } from 'vuex'\r\nimport { listUser } from \"@/api/system/user\"\r\nimport { getAllMethods } from \"@/api/game/method\"\r\nimport { listDraw } from \"@/api/game/draw\"\r\nimport { parseTime } from '@/utils/ruoyi'\r\nimport { cancelPay } from \"@/api/game/winning\"\r\nimport { listSerial } from \"@/api/game/serial\"\r\n\r\nexport default {\r\n  name: \"Winning\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 中奖管理表格数据\r\n      winningList: [],\r\n      // 是否全部展开\r\n      allExpanded: false,\r\n      // 移动端相关\r\n      isMobile: false,\r\n      showMobileSearch: false,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 用户列表\r\n      userList: [],\r\n      // 期号列表\r\n      issueList: [],\r\n      // 流水号列表\r\n      serialNumberList: [],\r\n      // 识别框搜索防抖定时器\r\n      shiBieSearchTimer: null,\r\n      // 对账模式相关\r\n      isReconciliationMode: false,\r\n      reconciliationLoading: false,\r\n      reconciledWinningIds: [], // 已对账的中奖记录ID数组（响应式）\r\n      originalQueryParams: null, // 保存原始查询参数\r\n      isReconciliationFixed: false, // 对账提示框是否固定在顶部\r\n      reconciliationOriginalTop: 0, // 对账提示框原始位置\r\n      scrollDebounceTimer: null, // 滚动防抖定时器\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        userId: null,\r\n        methodId: null,\r\n        betNumbers: null,\r\n        lotteryId: null,\r\n        issueNumber: null,\r\n        winAmount: null,\r\n        isPaid: null,\r\n        winningNumbers: null,\r\n        shibie: null,\r\n        serialNumber: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userId: [\r\n          { required: true, message: \"中奖用户不能为空\", trigger: \"blur\" }\r\n        ],\r\n        methodId: [\r\n          { required: true, message: \"玩法名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        lotteryId: [\r\n          { required: true, message: \"彩种名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        issueNumber: [\r\n          { required: true, message: \"中奖期号不能为空\", trigger: \"blur\" }\r\n        ],\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      gameMethodsData: state => state.game.methodsData\r\n    }),\r\n    /** 计算已对账的中奖金额总和 */\r\n    reconciledWinningAmount() {\r\n      if (!this.isReconciliationMode || !this.winningList || this.winningList.length === 0) {\r\n        return 0;\r\n      }\r\n\r\n      return this.winningList\r\n        .filter(winning => this.isWinningReconciled(winning.winId))\r\n        .reduce((total, winning) => {\r\n          const amount = parseFloat(winning.winAmount) || 0;\r\n          return total + amount;\r\n        }, 0);\r\n    },\r\n    /** 计算所有中奖金额总和 */\r\n    totalWinningAmount() {\r\n      if (!this.winningList || this.winningList.length === 0) {\r\n        return 0;\r\n      }\r\n\r\n      return this.winningList.reduce((total, winning) => {\r\n        const amount = parseFloat(winning.winAmount) || 0;\r\n        return total + amount;\r\n      }, 0);\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化移动端检测\r\n    this.initMobileDetection();\r\n\r\n    this.getList()\r\n    this.getCustomerList()\r\n    this.getGameMethods()\r\n    this.getIssueList()\r\n    this.getSerialNumberList()\r\n    // 恢复对账模式状态\r\n    this.restoreReconciliationState()\r\n  },\r\n  mounted() {\r\n    // 监听滚动事件\r\n    window.addEventListener('scroll', this.handleScroll);\r\n    // 监听一键清空事件\r\n    window.addEventListener('clearAllData', this.handleClearAllData);\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize);\r\n  },\r\n  beforeDestroy() {\r\n    // 移除滚动监听\r\n    window.removeEventListener('scroll', this.handleScroll);\r\n    // 移除一键清空事件监听\r\n    window.removeEventListener('clearAllData', this.handleClearAllData);\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.handleResize);\r\n\r\n    // 清理防抖定时器\r\n    if (this.scrollDebounceTimer) {\r\n      clearTimeout(this.scrollDebounceTimer);\r\n      this.scrollDebounceTimer = null;\r\n    }\r\n  },\r\n  methods: {\r\n    /** 初始化移动端检测 */\r\n    initMobileDetection() {\r\n      if (typeof window !== 'undefined') {\r\n        this.isMobile = window.innerWidth <= 768;\r\n      }\r\n    },\r\n\r\n    /** 处理窗口大小变化 */\r\n    handleResize() {\r\n      if (typeof window !== 'undefined') {\r\n        const newIsMobile = window.innerWidth <= 768;\r\n        if (this.isMobile !== newIsMobile) {\r\n          this.isMobile = newIsMobile;\r\n          // 在移动端和桌面端切换时，重置搜索框状态\r\n          if (!newIsMobile && this.showMobileSearch) {\r\n            this.showMobileSearch = false;\r\n          }\r\n          // 强制重新渲染以适应新的屏幕尺寸\r\n          this.$nextTick(() => {\r\n            this.$forceUpdate();\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 切换移动端搜索框显示 */\r\n    toggleMobileSearch() {\r\n      this.showMobileSearch = !this.showMobileSearch;\r\n    },\r\n\r\n    /** 切换移动端卡片展开状态 */\r\n    toggleMobileCard(item, index) {\r\n      this.$set(item, 'mobileExpanded', !item.mobileExpanded);\r\n    },\r\n\r\n    /** 退出对账模式 */\r\n    exitReconciliationMode() {\r\n      this.toggleReconciliationMode();\r\n    },\r\n\r\n    /** 获取期号列表 */\r\n    getIssueList() {\r\n      listDraw().then(response => {\r\n        this.issueList = response.rows\r\n      })\r\n    },\r\n    /** 获取用户列表 */\r\n    getCustomerList() {\r\n      listCustomer().then(response => {\r\n        this.userList = response.rows\r\n      })\r\n    },\r\n    /** 查询中奖管理列表 */\r\n    getList() {\r\n      this.loading = true\r\n      const params = { ...this.queryParams };\r\n\r\n      // 如果是对账模式，获取所有数据\r\n      if (this.isReconciliationMode) {\r\n        params.pageNum = 1;\r\n        params.pageSize = 999999;\r\n      }\r\n\r\n      // 记录是否是识别框搜索\r\n      const isShiBieSearch = params.shibie && params.shibie.trim() !== '';\r\n\r\n      // 如果有识别框搜索内容\r\n      if (isShiBieSearch) {\r\n        // 去除首尾空格\r\n        params.shibie = params.shibie.trim();\r\n        // 对于识别框搜索，不传递给后端，而是获取所有数据进行前端过滤\r\n        delete params.shibie;\r\n      }\r\n\r\n      listWinning(params).then(response => {\r\n        let filteredRows = response.rows;\r\n\r\n        // 如果有识别框搜索内容且有返回结果，进行前端过滤\r\n        if (isShiBieSearch && filteredRows.length > 0) {\r\n          const searchText = this.queryParams.shibie.trim();\r\n\r\n          // 模糊匹配：包含搜索文本的所有记录\r\n          filteredRows = filteredRows.filter(row =>\r\n            row.shibie && row.shibie.includes(searchText)\r\n          );\r\n\r\n          // 更新总数为过滤后的数量\r\n          response.total = filteredRows.length;\r\n        }\r\n\r\n        this.winningList = filteredRows;\r\n        this.total = response.total;\r\n\r\n        // 如果是对账模式，强制正序排序\r\n        if (this.isReconciliationMode) {\r\n          this.sortWinningListForReconciliation();\r\n        }\r\n\r\n        this.loading = false;\r\n\r\n        // 如果是识别框搜索，自动展开所有搜索结果\r\n        if (isShiBieSearch) {\r\n          this.$nextTick(() => {\r\n            this.allExpanded = true;\r\n            this.winningList.forEach((row) => {\r\n              this.$refs.winningTable.toggleRowExpansion(row, true);\r\n            });\r\n          });\r\n        } else {\r\n          // 非识别框搜索，重置展开状态\r\n          this.resetExpandState();\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        winId: null,\r\n        userId: null,\r\n        methodId: null,\r\n        betNumber: null,\r\n        lotteryId: null,\r\n        issueNumber: null,\r\n        winAmount: null,\r\n        isPaid: null,\r\n        winningNumbers: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.winId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 处理表格行点击 */\r\n    handleRowClick(row, column, event) {\r\n      // 避免点击操作按钮时触发行展开\r\n      if (column && column.type === 'selection') {\r\n        return;\r\n      }\r\n\r\n      // 在对账模式下，如果点击的是中奖金额列，不展开行\r\n      if (this.isReconciliationMode && column && column.property === 'winAmount') {\r\n        return;\r\n      }\r\n\r\n      console.log('中奖记录表格行点击:', row.winId || 'unknown');\r\n\r\n      // 切换行的展开状态\r\n      if (this.$refs.winningTable) {\r\n        this.$refs.winningTable.toggleRowExpansion(row);\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加中奖管理\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const winId = row.winId || this.ids\r\n      getWinning(winId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改中奖管理\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.winId != null) {\r\n            updateWinning(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addWinning(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const winIds = row.winId || this.ids\r\n      this.$modal.confirm('是否确认删除中奖管理编号为\"' + winIds + '\"的数据项？').then(function() {\r\n        return delWinning(winIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出所有中奖管理数据项？').then(() => {\r\n        this.exportLoading = true\r\n        // 获取所有数据用于导出\r\n        const exportParams = {\r\n          ...this.queryParams,\r\n          pageNum: 1,\r\n          pageSize: 10000 // 获取大量数据\r\n        }\r\n\r\n        listWinning(exportParams).then(response => {\r\n          const data = response.rows\r\n          const formattedData = this.formatDataForExport(data)\r\n          this.exportToExcel(formattedData)\r\n        }).catch(() => {\r\n          this.$modal.msgError(\"导出失败\")\r\n        }).finally(() => {\r\n          this.exportLoading = false\r\n        })\r\n      }).catch(() => {})\r\n    },\r\n\r\n    /** 格式化导出数据，与页面显示保持一致 */\r\n    formatDataForExport(data) {\r\n      return data.map(item => {\r\n        // 格式化下注号码\r\n        let betNumbersFormatted = ''\r\n        if (item.betNumber) {\r\n          try {\r\n            const betData = JSON.parse(item.betNumber)\r\n            if (betData.numbers && Array.isArray(betData.numbers)) {\r\n              // 胆拖玩法格式化 (method_id 44-59)\r\n              if (item.methodId >= 44 && item.methodId <= 59) {\r\n                if (betData.numbers.length > 0) {\r\n                  const firstNum = betData.numbers[0]\r\n                  betNumbersFormatted = `胆${firstNum.danma} 拖${firstNum.tuoma}`\r\n                }\r\n              }\r\n              // 跨度玩法格式化 (method_id 60-69)\r\n              else if (item.methodId >= 60 && item.methodId <= 69) {\r\n                betNumbersFormatted = `跨度${item.methodId - 60}`\r\n              }\r\n              // 原有玩法格式化\r\n              else {\r\n                betNumbersFormatted = betData.numbers.map(num => Object.values(num).join('.')).join(', ')\r\n              }\r\n            }\r\n          } catch (e) {\r\n            betNumbersFormatted = item.betNumbers || ''\r\n          }\r\n        } else {\r\n          betNumbersFormatted = item.betNumbers || ''\r\n        }\r\n\r\n        // 格式化中奖号码\r\n        let winningNumbersFormatted = ''\r\n        if (item.winningNumbers) {\r\n          try {\r\n            if (typeof item.winningNumbers === 'string') {\r\n              const winData = JSON.parse(item.winningNumbers)\r\n              if (winData.winning && Array.isArray(winData.winning)) {\r\n                winningNumbersFormatted = winData.winning.map(num => {\r\n                  if (num.b === undefined && num.c === undefined) {\r\n                    return num.a\r\n                  } else if (num.c === undefined) {\r\n                    return `${num.a}.${num.b}`\r\n                  } else {\r\n                    return `${num.a}.${num.b}.${num.c}`\r\n                  }\r\n                }).join(', ')\r\n              }\r\n            } else {\r\n              winningNumbersFormatted = item.winningNumbers\r\n            }\r\n          } catch (e) {\r\n            winningNumbersFormatted = item.winningNumbers || ''\r\n          }\r\n        }\r\n\r\n        // 格式化中奖金额（去掉￥符号）\r\n        const formatAmountForExport = (amount) => {\r\n          if (!amount && amount !== 0) return '0.00'\r\n          const num = parseFloat(amount)\r\n          return num.toFixed(2)\r\n        }\r\n\r\n        return {\r\n          '中奖用户': this.getUserName(item.userId),\r\n          '彩种': this.getLotteryName(item.lotteryId),\r\n          '玩法名称': this.getMethodName(item.methodId),\r\n          '下注号码': betNumbersFormatted,\r\n          '中奖号码': winningNumbersFormatted,\r\n          '流水号': item.serialNumber,\r\n          '中奖金额': formatAmountForExport(item.winAmount),\r\n          '号码识别': item.shibie || ''\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出到Excel */\r\n    exportToExcel(data) {\r\n      import('@/utils/Export2Excel').then(excel => {\r\n        const tHeader = ['中奖用户', '彩种', '玩法名称', '下注号码', '中奖号码', '流水号', '中奖金额', '号码识别']\r\n        const filterVal = ['中奖用户', '彩种', '玩法名称', '下注号码', '中奖号码', '流水号', '中奖金额', '号码识别']\r\n        const exportData = data.map(v => filterVal.map(j => v[j]))\r\n\r\n        excel.export_json_to_excel({\r\n          header: tHeader,\r\n          data: exportData,\r\n          filename: `中奖管理_${this.parseTime(new Date(), '{y}{m}{d}_{h}{i}{s}')}`,\r\n          autoWidth: true,\r\n          bookType: 'xlsx'\r\n        })\r\n\r\n        this.$modal.msgSuccess(\"导出成功\")\r\n      }).catch(() => {\r\n        this.$modal.msgError(\"导出失败\")\r\n      })\r\n    },\r\n    /** 获取玩法名称 */\r\n    getMethodName(methodId) {\r\n      if (!this.gameMethodsData || !this.gameMethodsData.length) {\r\n        return methodId\r\n      }\r\n      const method = this.gameMethodsData.find(item => Number(item.methodId) === Number(methodId))\r\n      return method ? method.methodName : methodId\r\n    },\r\n    /** 获取用户名称 */\r\n    getUserName(userId) {\r\n      if (!this.userList || !this.userList.length) {\r\n        return userId\r\n      }\r\n      const user = this.userList.find(item => Number(item.userId) === Number(userId))\r\n      return user ? user.name : userId\r\n    },\r\n    /** 获取彩种名称 */\r\n    getLotteryName(lotteryId) {\r\n      const lotteryMap = {\r\n        '1': '福彩3D',\r\n        '2': '体彩排三'\r\n      }\r\n      return lotteryMap[lotteryId] || lotteryId\r\n    },\r\n    /** 处理赔付 */\r\n    handlePay(row) {\r\n      updateWinning({\r\n        winId: row.winId,\r\n        isPaid: 1\r\n      }).then(response => {\r\n        this.$modal.msgSuccess(\"赔付成功\")\r\n        this.getList()\r\n      })\r\n    },\r\n    /** 批量赔付 */\r\n    handleBatchPay() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请选择要赔付的记录\")\r\n        return\r\n      }\r\n      this.loading = true\r\n      const promises = this.ids.map(winId => \r\n        updateWinning({\r\n          winId: winId,\r\n          isPaid: 1\r\n        })\r\n      )\r\n      Promise.all(promises).then(() => {\r\n        this.$modal.msgSuccess(\"批量赔付成功\")\r\n        this.getList()\r\n      }).catch(error => {\r\n        this.$modal.msgError(\"批量赔付失败：\" + (error.message || \"未知错误\"))\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    formatAmount(amount) {\r\n      if (amount === null || amount === undefined) {\r\n        return '￥0.00'\r\n      }\r\n      return '￥' + parseFloat(amount).toFixed(2)\r\n    },\r\n    /** 切换对账模式 */\r\n    toggleReconciliationMode() {\r\n      if (!this.isReconciliationMode) {\r\n        // 进入对账模式\r\n        this.enterReconciliationMode();\r\n      } else {\r\n        // 退出对账模式\r\n        this.exitReconciliationMode();\r\n      }\r\n    },\r\n    /** 进入对账模式 */\r\n    enterReconciliationMode() {\r\n      // 保存原始查询参数\r\n      this.originalQueryParams = { ...this.queryParams };\r\n\r\n      // 清空已对账的中奖记录ID数组\r\n      this.reconciledWinningIds = [];\r\n\r\n      // 重置固定状态和防抖定时器\r\n      this.isReconciliationFixed = false;\r\n      this.reconciliationOriginalTop = 0;\r\n      if (this.scrollDebounceTimer) {\r\n        clearTimeout(this.scrollDebounceTimer);\r\n        this.scrollDebounceTimer = null;\r\n      }\r\n\r\n      // 设置为获取所有数据（不分页）\r\n      const allDataParams = {\r\n        ...this.queryParams,\r\n        pageNum: 1,\r\n        pageSize: 999999 // 设置一个很大的数值来获取所有数据\r\n      };\r\n\r\n      this.loading = true;\r\n\r\n      listWinning(allDataParams).then(response => {\r\n        this.winningList = response.rows;\r\n        this.total = response.rows.length;\r\n\r\n        // 强制正序排序（对账模式下按winId正序）\r\n        this.sortWinningListForReconciliation();\r\n\r\n        // 设置对账模式\r\n        this.isReconciliationMode = true;\r\n\r\n        // 保存对账状态\r\n        this.saveReconciliationState();\r\n\r\n        this.loading = false;\r\n        this.reconciliationLoading = false;\r\n\r\n        this.$modal.msgSuccess(`已进入对账模式，共加载 ${response.rows.length} 条中奖记录，按流水号正序排列显示`);\r\n      }).catch(error => {\r\n        this.loading = false;\r\n        this.reconciliationLoading = false;\r\n        this.$modal.msgError('进入对账模式失败：' + error.message);\r\n      });\r\n    },\r\n    /** 退出对账模式 */\r\n    exitReconciliationMode() {\r\n      // 恢复原始查询参数\r\n      if (this.originalQueryParams) {\r\n        this.queryParams = { ...this.originalQueryParams };\r\n      }\r\n\r\n      // 清空已对账的中奖记录ID数组\r\n      this.reconciledWinningIds = [];\r\n\r\n      // 重置固定状态和清理定时器\r\n      this.isReconciliationFixed = false;\r\n      this.reconciliationOriginalTop = 0;\r\n      if (this.scrollDebounceTimer) {\r\n        clearTimeout(this.scrollDebounceTimer);\r\n        this.scrollDebounceTimer = null;\r\n      }\r\n\r\n      // 设置对账模式为false\r\n      this.isReconciliationMode = false;\r\n\r\n      // 清除对账状态\r\n      this.clearReconciliationState();\r\n\r\n      // 重新加载数据\r\n      this.getList();\r\n\r\n      this.$modal.msgSuccess('已退出对账模式');\r\n    },\r\n    /** 对账模式下的排序（强制正序） */\r\n    sortWinningListForReconciliation() {\r\n      if (!this.winningList || this.winningList.length === 0) {\r\n   \r\n        return;\r\n      }\r\n\r\n   \r\n\r\n      // 对中奖记录按流水号正序排序\r\n      this.winningList.sort((a, b) => {\r\n        const aSerialNumber = a.serialNumber || 0;\r\n        const bSerialNumber = b.serialNumber || 0;\r\n        return aSerialNumber - bSerialNumber; // 强制按流水号正序\r\n      });\r\n\r\n \r\n    \r\n\r\n      // 强制触发Vue的响应式更新\r\n      this.$forceUpdate();\r\n\r\n      // 确保表格重新渲染\r\n      this.$nextTick(() => {\r\n        if (this.$refs.winningTable) {\r\n          this.$refs.winningTable.doLayout();\r\n        }\r\n      });\r\n    },\r\n    /** 标记中奖记录为已对账 */\r\n    markWinningAsReconciled(winId) {\r\n      if (!this.reconciledWinningIds.includes(winId)) {\r\n        this.reconciledWinningIds.push(winId);\r\n        this.saveReconciliationState();\r\n      }\r\n    },\r\n    /** 取消中奖记录的对账状态 */\r\n    unmarkWinningAsReconciled(winId) {\r\n      const index = this.reconciledWinningIds.indexOf(winId);\r\n      if (index > -1) {\r\n        this.reconciledWinningIds.splice(index, 1);\r\n        this.saveReconciliationState();\r\n      }\r\n    },\r\n    /** 切换中奖记录的对账状态 */\r\n    toggleWinningReconciliation(winId) {\r\n      if (this.isWinningReconciled(winId)) {\r\n        this.unmarkWinningAsReconciled(winId);\r\n      } else {\r\n        this.markWinningAsReconciled(winId);\r\n      }\r\n    },\r\n    /** 检查中奖记录是否已对账 */\r\n    isWinningReconciled(winId) {\r\n      return this.reconciledWinningIds.includes(winId);\r\n    },\r\n    /** 保存对账状态到localStorage */\r\n    saveReconciliationState() {\r\n      try {\r\n        const reconciliationData = {\r\n          isReconciliationMode: this.isReconciliationMode,\r\n          reconciledWinningIds: this.reconciledWinningIds,\r\n          originalQueryParams: this.originalQueryParams,\r\n          timestamp: Date.now()\r\n        };\r\n        localStorage.setItem('winning_reconciliation_state', JSON.stringify(reconciliationData));\r\n      } catch (error) {\r\n        console.error('保存winning对账状态失败:', error);\r\n      }\r\n    },\r\n    /** 从localStorage恢复对账状态 */\r\n    restoreReconciliationState() {\r\n      try {\r\n        const savedData = localStorage.getItem('winning_reconciliation_state');\r\n        if (savedData) {\r\n          const reconciliationData = JSON.parse(savedData);\r\n\r\n          // 恢复对账模式状态\r\n          if (reconciliationData.isReconciliationMode) {\r\n            this.isReconciliationMode = true;\r\n            this.reconciledWinningIds = reconciliationData.reconciledWinningIds || [];\r\n            this.originalQueryParams = reconciliationData.originalQueryParams || null;\r\n\r\n            console.log('恢复winning对账模式状态:', {\r\n              reconciledCount: this.reconciledWinningIds.length,\r\n              hasOriginalParams: !!this.originalQueryParams\r\n            });\r\n\r\n            // 在下一个tick中重新加载数据以确保对账模式正确应用\r\n            this.$nextTick(() => {\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('恢复winning对账状态失败:', error);\r\n        // 清除损坏的数据\r\n        localStorage.removeItem('winning_reconciliation_state');\r\n      }\r\n    },\r\n    /** 清除对账状态 */\r\n    clearReconciliationState() {\r\n      try {\r\n        localStorage.removeItem('winning_reconciliation_state');\r\n      } catch (error) {\r\n        console.error('清除winning对账状态失败:', error);\r\n      }\r\n    },\r\n    /** 处理一键清空事件 */\r\n    handleClearAllData(event) {\r\n      console.log('winning页面收到一键清空事件:', event.detail);\r\n\r\n      // 只有当前页面处于对账模式时才处理，避免与record页面冲突\r\n      if (this.isReconciliationMode) {\r\n        // 添加短暂延迟，确保事件处理的顺序性\r\n        setTimeout(() => {\r\n          this.exitReconciliationMode();\r\n          console.log('winning页面因一键清空操作退出对账模式');\r\n          this.$message.info('检测到一键清空操作，已自动退出对账模式');\r\n        }, 100);\r\n      }\r\n    },\r\n    /** 处理滚动事件 */\r\n    handleScroll() {\r\n      if (!this.isReconciliationMode || !this.$refs.reconciliationNotice) {\r\n        return;\r\n      }\r\n\r\n      const noticeElement = this.$refs.reconciliationNotice;\r\n      const rect = noticeElement.getBoundingClientRect();\r\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\r\n\r\n      // 初始化原始位置（只在第一次计算）\r\n      if (this.reconciliationOriginalTop === 0 && !this.isReconciliationFixed) {\r\n        this.reconciliationOriginalTop = scrollTop + rect.top;\r\n      }\r\n\r\n      // 使用防抖处理，避免频繁切换\r\n      if (!this.scrollDebounceTimer) {\r\n        this.scrollDebounceTimer = setTimeout(() => {\r\n          // 如果对账提示框的顶部离开屏幕超过阈值，则固定它\r\n          if (rect.top <= 0 && !this.isReconciliationFixed) {\r\n            this.isReconciliationFixed = true;\r\n          }\r\n          // 如果滚动回到原始位置附近，则取消固定\r\n          else if (scrollTop <= this.reconciliationOriginalTop - 20 && this.isReconciliationFixed) {\r\n            this.isReconciliationFixed = false;\r\n          }\r\n\r\n          this.scrollDebounceTimer = null;\r\n        }, 16); // 约60fps的更新频率\r\n      }\r\n    },\r\n    /** 撤销结算按钮操作 */\r\n    handleCancelPay(row) {\r\n      this.$modal.confirm('是否确认撤销该笔奖金的结算？').then(() => {\r\n        return cancelPay(row.winId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"撤销结算成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 获取玩法列表 */\r\n    getGameMethods() {\r\n      getAllMethods().then(response => {\r\n        this.$store.commit('game/SET_METHODS_DATA', response.rows)\r\n      })\r\n    },\r\n    /** 获取流水号列表 */\r\n    getSerialNumberList() {\r\n      // 注意：listSerial接口已经在后端实现了用户隔离，所以这里获取的就是当前用户的流水号\r\n      listSerial({ pageNum: 1, pageSize: 1000 }).then(response => {\r\n        \r\n        if (response && response.rows && response.rows.length > 0) {\r\n          // 提取流水号并去重排序\r\n          this.serialNumberList = [...new Set(response.rows.map(item => item.serialNumbers))]\r\n            .filter(item => item != null)\r\n            .sort((a, b) => b - a); // 降序排列，最新的在前面\r\n          \r\n        } else {\r\n          console.log('没有找到流水号数据，尝试从中奖记录获取');\r\n          this.getSerialNumberFromWinning();\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取流水号列表失败，尝试从中奖记录获取:', error);\r\n        this.getSerialNumberFromWinning();\r\n      })\r\n    },\r\n    /** 从中奖记录中获取流水号列表 */\r\n    getSerialNumberFromWinning() {\r\n      // 从中奖记录中提取流水号\r\n      listWinning({ pageNum: 1, pageSize: 1000 }).then(response => {\r\n        if (response && response.rows && response.rows.length > 0) {\r\n          // 从中奖记录中提取流水号并去重排序\r\n          this.serialNumberList = [...new Set(response.rows.map(item => item.serialNumber))]\r\n            .filter(item => item != null)\r\n            .sort((a, b) => b - a); // 降序排列，最新的在前面\r\n          console.log('从中奖记录获取的流水号列表:', this.serialNumberList);\r\n        }\r\n      }).catch(error => {\r\n        console.error('从中奖记录获取流水号失败:', error);\r\n      })\r\n    },\r\n    /** 展开/收起全部 */\r\n    toggleExpandAll() {\r\n      this.allExpanded = !this.allExpanded;\r\n      this.$nextTick(() => {\r\n        if (this.allExpanded) {\r\n          // 展开所有行\r\n          this.winningList.forEach((row, index) => {\r\n            this.$refs.winningTable.toggleRowExpansion(row, true);\r\n          });\r\n        } else {\r\n          // 收起所有行\r\n          this.winningList.forEach((row, index) => {\r\n            this.$refs.winningTable.toggleRowExpansion(row, false);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 重置展开状态 */\r\n    resetExpandState() {\r\n      this.allExpanded = false;\r\n      this.$nextTick(() => {\r\n        // 收起所有行\r\n        this.winningList.forEach((row) => {\r\n          this.$refs.winningTable.toggleRowExpansion(row, false);\r\n        });\r\n      });\r\n    },\r\n    /** 识别框输入处理 */\r\n    handleShiBieInput() {\r\n      // 防抖处理，避免频繁搜索\r\n      if (this.shiBieSearchTimer) {\r\n        clearTimeout(this.shiBieSearchTimer);\r\n      }\r\n      this.shiBieSearchTimer = setTimeout(() => {\r\n        if (this.queryParams.shibie && this.queryParams.shibie.trim()) {\r\n          this.handleQuery();\r\n        }\r\n      }, 500); // 500ms 延迟\r\n    },\r\n    /** 识别框清空处理 */\r\n    handleShiBieClear() {\r\n      this.queryParams.shibie = '';\r\n      this.handleQuery();\r\n    },\r\n    /** 中奖金额排序方法 */\r\n    sortByAmount(a, b) {\r\n      const amountA = parseFloat(a.winAmount) || 0;\r\n      const amountB = parseFloat(b.winAmount) || 0;\r\n      return amountA - amountB;\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面头部样式 */\r\n.page-header {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.header-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28px;\r\n}\r\n\r\n.header-info h2 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n}\r\n\r\n.header-info p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.search-container {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.search-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.search-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.search-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.reset-btn:hover {\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* 工具栏样式 */\r\n.toolbar-container {\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  border-radius: 16px;\r\n  padding: 20px 24px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.toolbar-container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);\r\n}\r\n\r\n/* 工具栏布局 */\r\n.toolbar-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.toolbar-right {\r\n  margin-left: auto;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* 工具栏按钮美化 */\r\n.toolbar-btn {\r\n  border-radius: 8px !important;\r\n  font-weight: 500 !important;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\r\n  border-width: 1.5px !important;\r\n  padding: 8px 16px !important;\r\n  font-size: 13px !important;\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.toolbar-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n  transition: left 0.5s;\r\n}\r\n\r\n.toolbar-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.toolbar-btn:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;\r\n}\r\n\r\n/* 展开按钮样式 */\r\n.expand-btn.el-button--primary {\r\n  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%) !important;\r\n  border-color: #409eff !important;\r\n  color: white !important;\r\n}\r\n\r\n.expand-btn.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%) !important;\r\n  border-color: #66b1ff !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.5) !important;\r\n}\r\n\r\n/* 刷新按钮样式 */\r\n.refresh-btn.el-button--info {\r\n  background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%) !important;\r\n  border-color: #909399 !important;\r\n  color: white !important;\r\n}\r\n\r\n.refresh-btn.el-button--info:hover {\r\n  background: linear-gradient(135deg, #b1b3b8 0%, #909399 100%) !important;\r\n  border-color: #b1b3b8 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(144, 147, 153, 0.5) !important;\r\n}\r\n\r\n/* 导出按钮样式 */\r\n.export-btn.el-button--warning.is-plain {\r\n  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;\r\n  border-color: #e6a23c !important;\r\n  color: white !important;\r\n}\r\n\r\n.export-btn.el-button--warning.is-plain:hover {\r\n  background: linear-gradient(135deg, #ebb563 0%, #e6a23c 100%) !important;\r\n  border-color: #ebb563 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(230, 162, 60, 0.5) !important;\r\n}\r\n\r\n/* 对账按钮样式 */\r\n.reconciliation-btn.el-button--success {\r\n  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;\r\n  border-color: #67c23a !important;\r\n  color: white !important;\r\n}\r\n\r\n.reconciliation-btn.el-button--success:hover {\r\n  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%) !important;\r\n  border-color: #85ce61 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(103, 194, 58, 0.5) !important;\r\n}\r\n\r\n.reconciliation-btn.el-button--danger {\r\n  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%) !important;\r\n  border-color: #f56c6c !important;\r\n  color: white !important;\r\n}\r\n\r\n.reconciliation-btn.el-button--danger:hover {\r\n  background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%) !important;\r\n  border-color: #f78989 !important;\r\n  color: white !important;\r\n  box-shadow: 0 6px 20px rgba(245, 108, 108, 0.5) !important;\r\n}\r\n\r\n/* 禁用状态样式 */\r\n.toolbar-btn.is-disabled {\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cdd7 100%) !important;\r\n  border-color: #dcdfe6 !important;\r\n  color: #c0c4cc !important;\r\n  cursor: not-allowed !important;\r\n  transform: none !important;\r\n  box-shadow: none !important;\r\n}\r\n\r\n.toolbar-btn.is-disabled::before {\r\n  display: none;\r\n}\r\n\r\n/* 按钮图标样式优化 */\r\n.toolbar-btn [class*=\"el-icon-\"] {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 1200px) {\r\n  .toolbar-container {\r\n    padding: 16px 20px;\r\n  }\r\n\r\n  .toolbar-left {\r\n    gap: 8px;\r\n  }\r\n\r\n  .toolbar-btn {\r\n    padding: 6px 12px !important;\r\n    font-size: 12px !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .toolbar-container {\r\n    padding: 12px 16px;\r\n  }\r\n\r\n  .toolbar-content {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .toolbar-left {\r\n    gap: 6px;\r\n  }\r\n\r\n  .toolbar-right {\r\n    margin-left: 0;\r\n    align-self: flex-end;\r\n  }\r\n\r\n  .toolbar-btn {\r\n    padding: 4px 8px !important;\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .toolbar-btn [class*=\"el-icon-\"] {\r\n    margin-right: 4px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 展开内容样式 */\r\n.expand-content {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.expand-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 16px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.expand-header i {\r\n  color: #667eea;\r\n}\r\n\r\n.table-expand {\r\n  font-size: 0;\r\n}\r\n\r\n.table-expand label {\r\n  width: 90px;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.table-expand .el-form-item {\r\n  margin-right: 0;\r\n  margin-bottom: 12px;\r\n  width: 50%;\r\n}\r\n\r\n.table-expand .el-form-item.full-width {\r\n  width: 100%;\r\n  margin-top: 0;\r\n}\r\n\r\n.expand-value {\r\n  font-size: 14px;\r\n  color: #2c3e50;\r\n}\r\n\r\n.number-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n}\r\n\r\n.number-tag {\r\n  margin: 0;\r\n  font-weight: 600;\r\n  border-radius: 6px;\r\n}\r\n\r\n.bet-tag {\r\n  background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\r\n  border-color: #409EFF;\r\n}\r\n\r\n.winning-tag {\r\n  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);\r\n  border-color: #67C23A;\r\n}\r\n\r\n.no-data {\r\n  color: #909399;\r\n  font-style: italic;\r\n}\r\n\r\n/* 表格列样式 */\r\n.user-info, .method-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 6px;\r\n  font-weight: 600;\r\n}\r\n\r\n.user-info {\r\n  color: #409EFF;\r\n}\r\n\r\n.method-info {\r\n  color: #67C23A;\r\n}\r\n\r\n.serial-number {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.serial-value {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.lottery-type {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.lottery-tag {\r\n  font-weight: 600;\r\n  border-radius: 6px;\r\n}\r\n\r\n.lottery-unknown {\r\n  color: #909399;\r\n  font-weight: 600;\r\n}\r\n\r\n.amount-simple {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #F56C6C;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .page-header {\r\n    padding: 16px;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .table-expand .el-form-item {\r\n    width: 100%;\r\n  }\r\n\r\n  .number-tags {\r\n    justify-content: center;\r\n  }\r\n\r\n  /* 移动端搜索按钮样式 */\r\n  .mobile-search-toggle {\r\n    margin-bottom: 10px;\r\n    text-align: center;\r\n  }\r\n\r\n  .mobile-search-btn {\r\n    width: 100%;\r\n    max-width: 200px;\r\n  }\r\n\r\n  /* 移动端搜索表单样式 */\r\n  .mobile-search-form {\r\n    padding: 10px;\r\n    background: #f5f7fa;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .mobile-search-form .el-form-item {\r\n    margin-bottom: 10px;\r\n    margin-right: 0;\r\n    width: 100%;\r\n  }\r\n\r\n  .mobile-search-form .el-form-item__label {\r\n    width: 60px !important;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .mobile-search-form .el-input,\r\n  .mobile-search-form .el-select {\r\n    width: 100% !important;\r\n  }\r\n\r\n  /* 移动端识别框输入区域优化 */\r\n  .mobile-search-form .el-form-item .el-textarea {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .mobile-search-form .el-form-item .el-textarea .el-textarea__inner {\r\n    width: 100% !important;\r\n    max-width: 100% !important;\r\n    min-height: 60px !important;\r\n    max-height: 80px !important;\r\n    resize: vertical;\r\n    box-sizing: border-box;\r\n    font-size: 14px;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  /* 移动端工具栏样式 */\r\n  .mobile-toolbar .toolbar-content {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .mobile-toolbar-left {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 6px;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .mobile-toolbar-left .toolbar-btn {\r\n    flex: 1;\r\n    min-width: calc(50% - 3px);\r\n    padding: 4px 8px !important;\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  /* 移动端卡片布局 */\r\n  .mobile-card-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .mobile-card {\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    margin-bottom: 12px;\r\n    overflow: hidden;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .mobile-card.reconciled {\r\n    border: 2px solid #67c23a;\r\n    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);\r\n  }\r\n\r\n  .mobile-card .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 12px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .mobile-card .card-header-left {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .mobile-card .winning-info {\r\n    flex: 1;\r\n  }\r\n\r\n  .mobile-card .user-name {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #409eff;\r\n    margin-bottom: 4px;\r\n  }\r\n\r\n  .mobile-card .winning-meta {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 12px;\r\n    color: #666;\r\n  }\r\n\r\n  .mobile-card .method-name {\r\n    color: #67c23a;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .mobile-card .card-header-right {\r\n    text-align: right;\r\n  }\r\n\r\n  .mobile-card .win-amount {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    color: #f56c6c;\r\n    margin-bottom: 4px;\r\n  }\r\n\r\n  .mobile-card .reconciliation-status {\r\n    font-size: 12px;\r\n    padding: 2px 6px;\r\n    border-radius: 4px;\r\n    background: #e6f7ff;\r\n    color: #1890ff;\r\n  }\r\n\r\n  .mobile-card .reconciliation-status.reconciled {\r\n    background: #f6ffed;\r\n    color: #52c41a;\r\n  }\r\n\r\n  .mobile-card .card-content {\r\n    padding: 12px;\r\n    background: #fafafa;\r\n  }\r\n\r\n  .mobile-card .detail-row {\r\n    display: flex;\r\n    margin-bottom: 8px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .mobile-card .detail-row:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .mobile-card .detail-label {\r\n    width: 70px;\r\n    font-size: 12px;\r\n    color: #666;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .mobile-card .detail-value {\r\n    flex: 1;\r\n    font-size: 12px;\r\n    color: #333;\r\n    word-break: break-all;\r\n  }\r\n\r\n  .mobile-card .detail-value .el-tag {\r\n    font-size: 10px;\r\n    padding: 0 4px;\r\n    margin: 1px;\r\n  }\r\n\r\n  /* 移动端对账模式提示 */\r\n  .mobile-reconciliation-notice {\r\n    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);\r\n    border: 1px solid #b3d8ff;\r\n    border-radius: 8px;\r\n    padding: 12px;\r\n    margin-bottom: 15px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .mobile-reconciliation-notice .reconciliation-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .mobile-reconciliation-notice .reconciliation-header i {\r\n    color: #409EFF;\r\n    margin-right: 6px;\r\n  }\r\n\r\n  .mobile-reconciliation-notice .reconciliation-stats {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .mobile-reconciliation-notice .stat-item {\r\n    color: #666;\r\n  }\r\n\r\n  .mobile-reconciliation-notice .exit-btn {\r\n    padding: 0;\r\n    font-size: 16px;\r\n    color: #f56c6c;\r\n  }\r\n\r\n  /* 移动端卡片点击样式 */\r\n  .mobile-card-container:not(.reconciliation-mode) .mobile-card {\r\n    cursor: pointer;\r\n  }\r\n\r\n  .mobile-card-container:not(.reconciliation-mode) .mobile-card:hover {\r\n    background-color: rgba(64, 158, 255, 0.05);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .reconciliation-mode .mobile-card .card-header-left {\r\n    cursor: pointer;\r\n    transition: background-color 0.2s ease;\r\n  }\r\n\r\n  .reconciliation-mode .mobile-card .card-header-left:hover {\r\n    background-color: rgba(64, 158, 255, 0.1);\r\n  }\r\n\r\n  .mobile-card .card-header-right.reconciliation-clickable {\r\n    cursor: pointer;\r\n    transition: background-color 0.2s ease;\r\n    border-radius: 4px;\r\n    padding: 4px;\r\n  }\r\n\r\n  .mobile-card .card-header-right.reconciliation-clickable:hover {\r\n    background-color: rgba(245, 108, 108, 0.1);\r\n  }\r\n}\r\n/* 对账模式提示样式 */\r\n.reconciliation-notice {\r\n  margin-bottom: 16px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.reconciliation-notice.reconciliation-fixed {\r\n  position: fixed;\r\n  top: 10px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  z-index: 1001;\r\n  width: calc(100% - 40px);\r\n  max-width: 1200px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  animation: slideDown 0.3s ease-out;\r\n  backdrop-filter: blur(8px);\r\n  background: rgba(255, 255, 255, 0.95);\r\n}\r\n\r\n@keyframes slideDown {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(-50%) translateY(-20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(-50%) translateY(0);\r\n  }\r\n}\r\n\r\n.reconciliation-alert {\r\n  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);\r\n  border: 2px solid #1890ff;\r\n  border-radius: 12px;\r\n  padding: 16px 20px;\r\n  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\n.reconciliation-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.reconciliation-header i {\r\n  font-size: 18px;\r\n  color: #1890ff;\r\n  margin-right: 8px;\r\n}\r\n\r\n.reconciliation-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #1890ff;\r\n}\r\n\r\n.reconciliation-content {\r\n  margin: 0;\r\n}\r\n\r\n.reconciliation-instruction {\r\n  background: linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%);\r\n  border: 2px solid #ffa940;\r\n  border-radius: 8px;\r\n  padding: 12px 16px;\r\n  margin: 12px 0 !important;\r\n  font-size: 15px;\r\n  line-height: 1.6;\r\n  box-shadow: 0 2px 8px rgba(255, 169, 64, 0.2);\r\n}\r\n\r\n.highlight-amount {\r\n  background: linear-gradient(135deg, #0080FFFF 0%, #067EFFFF 100%);\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 6px;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  border: 2px dashed white;\r\n  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);\r\n  animation: highlightPulse 2s infinite;\r\n}\r\n\r\n@keyframes highlightPulse {\r\n  0%, 100% {\r\n    transform: scale(1);\r\n    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);\r\n  }\r\n  50% {\r\n    transform: scale(1.05);\r\n    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.5);\r\n  }\r\n}\r\n\r\n.highlight-amount::after {\r\n  content: \" 👆\";\r\n  animation: bounce 1.5s infinite;\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-5px);\r\n  }\r\n  60% {\r\n    transform: translateY(-3px);\r\n  }\r\n}\r\n\r\n.reconciliation-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  font-weight: 600;\r\n  font-size: 15px;\r\n}\r\n\r\n.stats-text {\r\n  flex: 1;\r\n}\r\n\r\n.reconciled-count {\r\n  color: #1890ff;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.reconciled-amount {\r\n  color: #52c41a;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.total-amount {\r\n  color: #8c8c8c;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.total-count {\r\n  color: #52c41a;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n}\r\n\r\n.exit-reconciliation-btn {\r\n  margin-left: 16px;\r\n  padding: 4px 12px;\r\n  font-size: 12px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 6px rgba(245, 108, 108, 0.3);\r\n}\r\n\r\n.exit-reconciliation-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.5);\r\n}\r\n\r\n/* 对账模式中奖金额样式 */\r\n.reconciliation-amount-container {\r\n  border: 2px dashed #1890ff;\r\n  border-radius: 8px;\r\n  padding: 8px 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);\r\n  position: relative;\r\n  min-height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.reconciliation-amount-container:hover {\r\n  border-color: #40a9ff;\r\n  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);\r\n  transform: scale(1.02);\r\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);\r\n}\r\n\r\n.reconciliation-amount-container.reconciled {\r\n  border-color: #52c41a;\r\n  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);\r\n  border-style: solid;\r\n}\r\n\r\n.reconciliation-amount-container.reconciled:hover {\r\n  border-color: #73d13d;\r\n  background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);\r\n}\r\n\r\n.reconciliation-amount-text {\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  color: #FF0037FF;\r\n}\r\n\r\n.reconciliation-amount-container.reconciled .reconciliation-amount-text {\r\n  color: #FF0000FF;\r\n}\r\n\r\n.reconciled-icon {\r\n  position: absolute;\r\n  top: 2px;\r\n  right: 2px;\r\n  background-color: #EAFFEAFF !important;\r\n  color: #52c41a;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.reconciliation-status-badge {\r\n  position: absolute;\r\n  top: -1px;\r\n  right: -1px;\r\n  background: #ff4d4f;\r\n  color: white;\r\n  font-size: 10px;\r\n  padding: 2px 6px;\r\n  border-radius: 0 6px 0 8px;\r\n  font-weight: bold;\r\n  line-height: 1;\r\n  z-index: 10;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.reconciliation-status-badge.reconciled {\r\n  background: #52c41a;\r\n}\r\n\r\n</style>\r\n"]}]}